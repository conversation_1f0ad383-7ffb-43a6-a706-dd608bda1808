package com.geeksec.common.core.exceptions;

import com.geeksec.common.core.enums.ErrorCode;

import cn.hutool.http.HttpStatus;

/**
 * 基础异常类
 * 
 * 提供统一的异常处理基类，包含错误码和错误消息
 * 所有业务异常都应该继承此类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /** 错误码 */
    private final int code;

    /** 错误消息 */
    private final String message;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BaseException(String message) {
        super(message);
        this.code = HttpStatus.HTTP_INTERNAL_ERROR;
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BaseException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     */
    public BaseException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param message   自定义错误消息
     */
    public BaseException(ErrorCode errorCode, String message) {
        super(message);
        this.code = errorCode.getCode();
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.code = HttpStatus.HTTP_INTERNAL_ERROR;
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   原因异常
     */
    public BaseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param cause     原因异常
     */
    public BaseException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param message   自定义错误消息
     * @param cause     原因异常
     */
    public BaseException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.code = errorCode.getCode();
        this.message = message;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    @Override
    public String getMessage() {
        return message;
    }

    /**
     * 获取对应的 ErrorCode
     * 如果 code 不是有效的业务状态码，则返回 null
     *
     * @return 对应的 ErrorCode，如果不存在则返回 null
     */
    public ErrorCode getErrorCode() {
        return ErrorCode.getByCode(code);
    }

    /**
     * 获取对应的 ErrorCode，如果不存在则返回默认值
     *
     * @param defaultValue 默认返回值
     * @return 对应的 ErrorCode，如果不存在则返回指定的默认值
     */
    public ErrorCode getErrorCode(ErrorCode defaultValue) {
        return ErrorCode.getByCode(code, defaultValue);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("BaseException{");
        sb.append("code=").append(code);
        sb.append(", message='").append(message).append('\'');

        ErrorCode errorCode = getErrorCode();
        if (errorCode != null) {
            sb.append(", errorCode=").append(errorCode.name());
        }

        Throwable cause = getCause();
        if (cause != null) {
            sb.append(", cause=").append(cause);
        }

        sb.append('}');
        return sb.toString();
    }
}
