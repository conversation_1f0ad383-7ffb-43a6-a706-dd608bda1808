package com.geeksec.common.utils;

import com.geeksec.common.toolkit.crypto.HashUtils;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * HashUtils测试类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
class HashUtilsTest {

    @Test
    void testMd5() {
        // 测试基本MD5计算
        String input = "hello world";
        String expected = "5d41402abc4b2a76b9719d911017c592";
        assertEquals(expected, HashUtils.md5(input));

        // 测试空字符串
        assertEquals("", HashUtils.md5(""));
        assertEquals("", HashUtils.md5((String) null));
    }

    @Test
    void testSha1() {
        String input = "hello world";
        String expected = "2aae6c35c94fcfb415dbe95f408b9ce91ee846ed";
        assertEquals(expected, HashUtils.sha1(input));
    }

    @Test
    void testSha256() {
        String input = "hello world";
        String expected = "b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9";
        assertEquals(expected, HashUtils.sha256(input));
    }

    @Test
    void testMd5WithSalt() {
        String input = "password";
        String salt = "salt123";
        String result = HashUtils.md5WithSalt(input, salt);
        
        // 验证带盐值的MD5
        assertTrue(HashUtils.verifyMd5WithSalt(input, salt, result));
        assertFalse(HashUtils.verifyMd5WithSalt(input, "wrongsalt", result));
    }

    @Test
    void testGenerateSalt() {
        String salt1 = HashUtils.generateSalt();
        String salt2 = HashUtils.generateSalt();
        
        // 验证盐值不为空且不相同
        assertNotNull(salt1);
        assertNotNull(salt2);
        assertNotEquals(salt1, salt2);
        
        // 验证默认长度（16字节 = 32个十六进制字符）
        assertEquals(32, salt1.length());
    }

    @Test
    void testBytesToHex() {
        byte[] bytes = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xab, (byte) 0xcd, (byte) 0xef};
        String expected = "0123456789abcdef";
        assertEquals(expected, HashUtils.bytesToHex(bytes));
        
        // 测试空数组
        assertEquals("", HashUtils.bytesToHex(new byte[0]));
        assertEquals("", HashUtils.bytesToHex(null));
    }

    @Test
    void testHexToBytes() {
        String hex = "0123456789abcdef";
        byte[] expected = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xab, (byte) 0xcd, (byte) 0xef};
        assertArrayEquals(expected, HashUtils.hexToBytes(hex));
        
        // 测试空字符串
        assertArrayEquals(new byte[0], HashUtils.hexToBytes(""));
        assertArrayEquals(new byte[0], HashUtils.hexToBytes(null));
    }

    @Test
    void testHexToBytesInvalidInput() {
        // 测试奇数长度的十六进制字符串
        assertThrows(IllegalArgumentException.class, () -> {
            HashUtils.hexToBytes("123");
        });
    }

    @Test
    void testVerifyMd5() {
        String input = "test";
        String correctHash = HashUtils.md5(input);
        String wrongHash = "wronghash";
        
        assertTrue(HashUtils.verifyMd5(input, correctHash));
        assertFalse(HashUtils.verifyMd5(input, wrongHash));
    }

    @Test
    void testQuickMd5() {
        // 测试快速MD5方法（兼容性方法）
        String input = "test";
        assertEquals(HashUtils.md5(input), HashUtils.quickMd5(input));
    }

    @Test
    void testHashFile() {
        // 测试文件哈希计算
        byte[] fileContent = "file content".getBytes();
        String md5Hash = HashUtils.hashFile(fileContent, "MD5");
        String sha1Hash = HashUtils.hashFile(fileContent, "SHA-1");
        
        assertNotNull(md5Hash);
        assertNotNull(sha1Hash);
        assertNotEquals(md5Hash, sha1Hash);
        
        // 验证与直接计算的结果一致
        assertEquals(HashUtils.md5(fileContent), md5Hash);
        assertEquals(HashUtils.sha1(fileContent), sha1Hash);
    }
}
