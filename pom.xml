<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>common</module>
        <module>services</module>
        <module>flink-jobs</module>
        <module>deployment</module>
        <module>frontend</module>
    </modules>

    <groupId>com.geeksec</groupId>
    <artifactId>nta-platform</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <name>nta</name>
    <description>Network Traffic Analysis Platform</description>

    <properties>
        <!-- 基础配置 -->
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.release>${java.version}</maven.compiler.release>


        <!-- Spring Boot 相关 -->
        <spring-boot.version>3.3.6</spring-boot.version>

        <!-- 数据库相关 -->
        <postgresql.version>42.7.4</postgresql.version>
        <mysql.connector.version>8.4.0</mysql.connector.version>
        <druid.version>1.2.23</druid.version>
        <mybatis-flex.version>1.10.9</mybatis-flex.version>

        <!-- 存储相关 -->
        <elasticsearch.version>7.17.26</elasticsearch.version>
        <nebula.version>3.8.0</nebula.version>
        <jedis.version>5.2.0</jedis.version>

        <!-- 消息队列相关 -->
        <kafka.version>3.9.0</kafka.version>

        <!-- 图数据库 ORM -->
        <ngbatis.version>1.3.0-jdk17</ngbatis.version>

        <!-- 日志相关 -->
        <log4j.version>2.24.2</log4j.version>
        <slf4j.version>2.0.16</slf4j.version>

        <!-- 工具库 -->
        <commons.lang3.version>3.17.0</commons.lang3.version>
        <commons.io.version>2.18.0</commons.io.version>
        <commons.collections4.version>4.5.0-M2</commons.collections4.version>
        <commons.text.version>1.12.0</commons.text.version>
        <commons.validator.version>1.9.0</commons.validator.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.8.34</hutool.version>

        <lombok.version>1.18.36</lombok.version>
        <icu4j.version>76.1</icu4j.version>
        <protobuf.version>4.29.2</protobuf.version>
        <kryo.version>5.6.2</kryo.version>
        <guava.version>33.3.1-jre</guava.version>
        <knife4j.version>4.5.0</knife4j.version>
        <sa-token.version>1.44.0</sa-token.version>
        <chill.protobuf.version>0.10.0</chill.protobuf.version>
        <spring.core.version>5.3.20</spring.core.version>
        <os.maven.plugin.version>1.6.2</os.maven.plugin.version>
        <protobuf.maven.plugin.version>0.6.1</protobuf.maven.plugin.version>
        <build.helper.maven.plugin.version>3.3.0</build.helper.maven.plugin.version>
        <maven.antrun.plugin.version>3.1.0</maven.antrun.plugin.version>
        <maven.jar.plugin.version>3.3.0</maven.jar.plugin.version>
        <geoip2.version>4.2.1</geoip2.version>

        <!-- 安全相关 -->
        <bouncycastle.version>1.80</bouncycastle.version>

        <!-- 测试相关 -->
        <junit.jupiter.version>5.11.3</junit.jupiter.version>
        <junit.platform.version>1.11.3</junit.platform.version>
        <mockito.version>5.14.2</mockito.version>
        <surefire.plugin.version>3.5.2</surefire.plugin.version>

        <!-- JSON 处理相关 -->
        <jackson.version>2.18.2</jackson.version>
        <json.version>20240303</json.version>
        <fastjson2.version>2.0.54</fastjson2.version>
        <minio.version>8.5.14</minio.version>
        <doris.flink.connector.version>25.1.0</doris.flink.connector.version>
        <flink.version>1.20.1</flink.version>
        <assertj.version>3.26.3</assertj.version>

        <!-- Commons Libraries -->
        <commons.cli.version>1.5.0</commons.cli.version>
        <commons.configuration2.version>2.9.0</commons.configuration2.version>
        <commons.beanutils.version>1.9.4</commons.beanutils.version>

        <!-- Docker 和部署相关 -->
        <docker.registry>hb.gs.lan</docker.registry>
        <docker.image.prefix>nta</docker.image.prefix>
        <helm.chart.dir>${project.basedir}/deployment/helm</helm.chart.dir>
        <helm.chart.name>nta</helm.chart.name>
        <harbor.helm.repo>hb.gs.lan/chartrepo/helm-charts</harbor.helm.repo>
        <harbor.helm.username>admin</harbor.helm.username>
        <harbor.helm.password>Harbor12345</harbor.helm.password>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- 数据库相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- MyBatis Flex -->
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-processor</artifactId>
                <version>${mybatis-flex.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-core</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>

            <!-- API文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- 安全相关 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- 工具类 -->
            <dependency>
                <groupId>com.ibm.icu</groupId>
                <artifactId>icu4j</artifactId>
                <version>${icu4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <!-- 通用工具类依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collections4.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons.text.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons.validator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>



            <!-- 日志依赖 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- 数据库依赖 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>

            <!-- 测试依赖 -->

            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.geeksec</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 测试相关依赖 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-commons</artifactId>
                <version>${junit.platform.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-engine</artifactId>
                <version>${junit.platform.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- JSON 处理 -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- Flink Jobs Common Module -->
            <dependency>
                <groupId>com.geeksec</groupId>
                <artifactId>flink-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Nebula 图数据库相关 -->
            <dependency>
                <groupId>com.vesoft</groupId>
                <artifactId>client</artifactId>
                <version>${nebula.version}</version>
            </dependency>

            <!-- ngbatis 图数据库 ORM -->
            <dependency>
                <groupId>org.nebula-contrib</groupId>
                <artifactId>ngbatis</artifactId>
                <version>${ngbatis.version}</version>
            </dependency>

            <!-- Protobuf 相关 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.twitter</groupId>
                <artifactId>chill-protobuf</artifactId>
                <version>${chill.protobuf.version}</version>
            </dependency>

            <!-- Redis 客户端 -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- Kafka 消息队列 -->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <!-- Elasticsearch 搜索引擎 -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- MinIO 对象存储 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- Doris Flink Connector -->
            <dependency>
                <groupId>org.apache.doris</groupId>
                <artifactId>flink-doris-connector-1.20</artifactId>
                <version>${doris.flink.connector.version}</version>
            </dependency>

            <!-- Flink 核心依赖 -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-core</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- AssertJ 测试框架 -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nta-releases</id>
            <name>NTA Release Repository</name>
            <url>http://hb.gs.lan/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nta-snapshots</id>
            <name>NTA Snapshot Repository</name>
            <url>http://hb.gs.lan/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.2</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <release>${maven.compiler.release}</release>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>0.45.0</version>
                    <configuration>
                        <registry>${docker.registry}</registry>
                        <images>
                            <image>
                                <name>
                                    ${docker.registry}/${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                                <build>
                                    <contextDir>${project.basedir}</contextDir>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                    <args>
                                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                                    </args>
                                </build>
                            </image>
                        </images>
                    </configuration>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>push-image</id>
                            <phase>deploy</phase>
                            <goals>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.8.0</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <transformers>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                        <mainClass>${main.class}</mainClass>
                                    </transformer>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                                </transformers>
                                <filters>
                                    <filter>
                                        <artifact>*:*</artifact>
                                        <excludes>
                                            <exclude>META-INF/*.SF</exclude>
                                            <exclude>META-INF/*.DSA</exclude>
                                            <exclude>META-INF/*.RSA</exclude>
                                        </excludes>
                                    </filter>
                                </filters>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.4.2</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>lib/</classpathPrefix>
                                <mainClass>${main.class}</mainClass>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>com.kiwigrid</groupId>
                    <artifactId>helm-maven-plugin</artifactId>
                    <version>5.9</version>
                    <configuration>
                        <chartDirectory>${helm.chart.dir}</chartDirectory>
                        <chartVersion>${project.version}</chartVersion>
                        <uploadRepoStable>
                            <name>harbor-helm</name>
                            <url>https://${harbor.helm.repo}</url>
                            <username>${harbor.helm.username}</username>
                            <password>${harbor.helm.password}</password>
                            <type>CHARTMUSEUM</type>
                        </uploadRepoStable>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.junit.jupiter</groupId>
                            <artifactId>junit-jupiter-engine</artifactId>
                            <version>${junit.jupiter.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>