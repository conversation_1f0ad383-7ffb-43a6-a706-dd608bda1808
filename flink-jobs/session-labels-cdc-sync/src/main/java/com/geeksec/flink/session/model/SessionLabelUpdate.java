package com.geeksec.flink.session.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 会话标签更新事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(of = {"sessionId", "labels"})
public class SessionLabelUpdate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("labels")
    private List<Integer> labels;
    
    @JsonProperty("update_timestamp")
    private Long updateTimestamp;
    
    @JsonProperty("change_count")
    private Integer changeCount;
    
    @JsonProperty("last_updated_by")
    private Integer lastUpdatedBy;
    
    {
        this.updateTimestamp = Instant.now().toEpochMilli();
        this.changeCount = 0;
    }
    
    public SessionLabelUpdate(String sessionId, List<Integer> labels) {
        this();
        this.sessionId = sessionId;
        this.labels = labels;
    }
    
    /**
     * 增加变更计数
     */
    public void incrementChangeCount() {
        this.changeCount = (this.changeCount == null ? 0 : this.changeCount) + 1;
    }
    
    /**
     * 检查标签是否发生变化
     */
    public boolean hasLabelsChanged(List<Integer> otherLabels) {
        if (this.labels == null && otherLabels == null) {
            return false;
        }
        if (this.labels == null || otherLabels == null) {
            return true;
        }
        return !Objects.equals(this.labels, otherLabels);
    }
    

}
