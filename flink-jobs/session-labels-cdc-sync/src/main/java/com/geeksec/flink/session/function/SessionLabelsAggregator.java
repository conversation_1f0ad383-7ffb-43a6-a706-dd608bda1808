package com.geeksec.flink.session.function;

import com.geeksec.flink.session.model.SessionLabelChange;
import com.geeksec.flink.session.model.SessionLabelUpdate;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话标签聚合器
 * 
 * 功能：
 * 1. 维护每个会话的当前标签集合
 * 2. 处理标签的增删操作
 * 3. 输出标签变更事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class SessionLabelsAggregator extends KeyedProcessFunction<String, SessionLabelChange, SessionLabelUpdate> {
    
    private final Configuration config;
    
    // 状态：当前会话的标签集合
    private transient ValueState<Set<Integer>> currentLabelsState;
    
    // 状态：最后更新时间
    private transient ValueState<Long> lastUpdateTimeState;
    
    // 状态：变更计数
    private transient ValueState<Integer> changeCountState;
    
    // 状态：最后更新者
    private transient ValueState<Integer> lastUpdatedByState;
    
    // 配置：批量输出间隔（毫秒）
    private final long batchIntervalMs;
    
    // 配置：状态TTL（毫秒）
    private final long stateTtlMs;
    
    public SessionLabelsAggregator(Configuration config) {
        this.config = config;
        this.batchIntervalMs = config.getLong("aggregator.batch-interval-ms", 5000L);
        this.stateTtlMs = config.getLong("aggregator.state-ttl-ms", 7 * 24 * 60 * 60 * 1000L); // 默认7天
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        ValueStateDescriptor<Set<Integer>> labelsDescriptor = new ValueStateDescriptor<>(
            "current-labels",
            TypeInformation.of(new TypeHint<Set<Integer>>() {})
        );
        currentLabelsState = getRuntimeContext().getState(labelsDescriptor);
        
        ValueStateDescriptor<Long> timeDescriptor = new ValueStateDescriptor<>(
            "last-update-time",
            Long.class
        );
        lastUpdateTimeState = getRuntimeContext().getState(timeDescriptor);
        
        ValueStateDescriptor<Integer> countDescriptor = new ValueStateDescriptor<>(
            "change-count",
            Integer.class
        );
        changeCountState = getRuntimeContext().getState(countDescriptor);
        
        ValueStateDescriptor<Integer> updatedByDescriptor = new ValueStateDescriptor<>(
            "last-updated-by",
            Integer.class
        );
        lastUpdatedByState = getRuntimeContext().getState(updatedByDescriptor);
        
        log.info("会话标签聚合器初始化完成，批量间隔: {}ms, 状态TTL: {}ms", batchIntervalMs, stateTtlMs);
    }
    
    @Override
    public void processElement(SessionLabelChange change, Context ctx, Collector<SessionLabelUpdate> out) 
            throws Exception {
        
        String sessionId = change.getSessionId();
        Integer labelId = change.getLabelId();
        String operation = change.getOperation();
        
        log.debug("处理会话标签变更: sessionId={}, labelId={}, operation={}", 
                sessionId, labelId, operation);
        
        // 获取当前标签集合
        Set<Integer> currentLabels = currentLabelsState.value();
        if (currentLabels == null) {
            currentLabels = new HashSet<>();
        }
        
        // 记录变更前的标签集合
        Set<Integer> previousLabels = new HashSet<>(currentLabels);
        
        // 处理标签变更
        boolean changed = false;
        switch (operation.toUpperCase()) {
            case "INSERT":
            case "UPDATE":
                if (labelId != null) {
                    changed = currentLabels.add(labelId);
                }
                break;
            case "DELETE":
                if (labelId != null) {
                    changed = currentLabels.remove(labelId);
                }
                break;
            default:
                log.warn("未知的操作类型: {}", operation);
                return;
        }
        
        // 如果标签集合发生变化，更新状态并输出事件
        if (changed) {
            // 更新状态
            currentLabelsState.update(currentLabels);
            lastUpdateTimeState.update(System.currentTimeMillis());
            
            Integer changeCount = changeCountState.value();
            changeCount = (changeCount == null ? 0 : changeCount) + 1;
            changeCountState.update(changeCount);
            
            if (change.getCreatedBy() != null) {
                lastUpdatedByState.update(change.getCreatedBy());
            }
            
            // 创建更新事件
            SessionLabelUpdate update = new SessionLabelUpdate();
            update.setSessionId(sessionId);
            update.setLabels(currentLabels.stream().sorted().collect(Collectors.toList()));
            update.setUpdateTimestamp(System.currentTimeMillis());
            update.setChangeCount(changeCount);
            update.setLastUpdatedBy(change.getCreatedBy());
            
            // 注册定时器，延迟输出以支持批量处理
            long currentTime = ctx.timerService().currentProcessingTime();
            ctx.timerService().registerProcessingTimeTimer(currentTime + batchIntervalMs);
            
            log.debug("会话标签已更新: sessionId={}, 标签数量: {} -> {}, 变更计数: {}", 
                    sessionId, previousLabels.size(), currentLabels.size(), changeCount);
        } else {
            log.debug("会话标签无变化: sessionId={}, labelId={}, operation={}", 
                    sessionId, labelId, operation);
        }
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<SessionLabelUpdate> out) 
            throws Exception {
        
        String sessionId = ctx.getCurrentKey();
        
        // 获取当前状态
        Set<Integer> currentLabels = currentLabelsState.value();
        Long lastUpdateTime = lastUpdateTimeState.value();
        Integer changeCount = changeCountState.value();
        Integer lastUpdatedBy = lastUpdatedByState.value();
        
        if (currentLabels != null && lastUpdateTime != null) {
            // 检查是否在批量间隔内有新的变更
            long currentTime = ctx.timerService().currentProcessingTime();
            if (currentTime - lastUpdateTime >= batchIntervalMs) {
                // 输出聚合后的更新事件
                SessionLabelUpdate update = new SessionLabelUpdate();
                update.setSessionId(sessionId);
                update.setLabels(currentLabels.stream().sorted().collect(Collectors.toList()));
                update.setUpdateTimestamp(lastUpdateTime);
                update.setChangeCount(changeCount);
                update.setLastUpdatedBy(lastUpdatedBy);
                
                out.collect(update);
                
                log.debug("输出会话标签更新: sessionId={}, 标签数量: {}, 变更计数: {}", 
                        sessionId, currentLabels.size(), changeCount);
            }
        }
        
        // 清理过期状态
        if (lastUpdateTime != null && 
            ctx.timerService().currentProcessingTime() - lastUpdateTime > stateTtlMs) {
            
            currentLabelsState.clear();
            lastUpdateTimeState.clear();
            changeCountState.clear();
            lastUpdatedByState.clear();
            
            log.debug("清理过期状态: sessionId={}", sessionId);
        }
    }
}
