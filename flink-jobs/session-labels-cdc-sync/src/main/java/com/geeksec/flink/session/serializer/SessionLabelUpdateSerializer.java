package com.geeksec.flink.session.serializer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.flink.session.model.SessionLabelUpdate;
import org.apache.doris.flink.sink.writer.serializer.DorisRecordSerializer;
import org.apache.flink.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会话标签更新序列化器
 * 
 * 功能：
 * 1. 将 SessionLabelUpdate 序列化为 Doris Stream Load 格式
 * 2. 生成 UPDATE SQL 语句
 * 3. 处理标签数组的格式转换
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Slf4j
public class SessionLabelUpdateSerializer implements DorisRecordSerializer<SessionLabelUpdate> {
    
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String serialize(SessionLabelUpdate record) {
        try {
            // 构建更新的JSON数据
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("session_id", record.getSessionId());
            
            // 处理标签数组
            if (record.getLabels() != null && !record.getLabels().isEmpty()) {
                // 将标签数组转换为Doris ARRAY格式
                String labelsArray = "[" + record.getLabels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")) + "]";
                updateData.put("labels", labelsArray);
            } else {
                // 空标签数组
                updateData.put("labels", "[]");
            }
            
            // 添加更新时间戳
            updateData.put("labels_updated_at", record.getUpdateTimestamp());
            
            // 序列化为JSON字符串
            String jsonString = objectMapper.writeValueAsString(updateData);
            
            log.debug("序列化会话标签更新: sessionId={}, labels={}", 
                    record.getSessionId(), record.getLabels());
            
            return jsonString;
            
        } catch (Exception e) {
            log.error("序列化会话标签更新失败: {}", record, e);
            return null;
        }
    }
    
    /**
     * 生成 Doris Stream Load 的 UPDATE 语句
     */
    public String generateUpdateSql(SessionLabelUpdate record) {
        if (StringUtils.isNullOrWhitespaceOnly(record.getSessionId())) {
            log.warn("会话ID为空，跳过生成UPDATE语句");
            return null;
        }
        
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE dwd_session_logs SET ");
            
            // 更新标签字段
            if (record.getLabels() != null && !record.getLabels().isEmpty()) {
                String labelsArray = "[" + record.getLabels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")) + "]";
                sql.append("labels = ").append(labelsArray);
            } else {
                sql.append("labels = []");
            }
            
            // 添加更新时间戳
            sql.append(", labels_updated_at = ").append(record.getUpdateTimestamp());
            
            // WHERE 条件
            sql.append(" WHERE session_id = '").append(record.getSessionId()).append("'");
            
            String updateSql = sql.toString();
            log.debug("生成UPDATE SQL: {}", updateSql);
            
            return updateSql;
            
        } catch (Exception e) {
            log.error("生成UPDATE SQL失败: {}", record, e);
            return null;
        }
    }
    
    /**
     * 验证记录的有效性
     */
    private boolean isValidRecord(SessionLabelUpdate record) {
        if (record == null) {
            log.warn("记录为null");
            return false;
        }
        
        if (StringUtils.isNullOrWhitespaceOnly(record.getSessionId())) {
            log.warn("会话ID为空或空白");
            return false;
        }
        
        return true;
    }
    
    /**
     * 格式化标签数组为Doris ARRAY格式
     */
    private String formatLabelsArray(SessionLabelUpdate record) {
        if (record.getLabels() == null || record.getLabels().isEmpty()) {
            return "[]";
        }
        
        return "[" + record.getLabels().stream()
            .map(String::valueOf)
            .collect(Collectors.joining(",")) + "]";
    }
}
