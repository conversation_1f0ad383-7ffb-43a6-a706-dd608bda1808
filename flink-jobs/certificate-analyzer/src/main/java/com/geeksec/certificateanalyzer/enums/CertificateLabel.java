package com.geeksec.certificateanalyzer.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 证书标签枚举
 * 标签ID范围：1001-1999
 * 包含威胁评分和信任评分定义
 *
 * <AUTHOR>
 * @since 2025/06/20
 */
public enum CertificateLabel {
    // 证书信任相关标签
    TRUSTED_CA_CERT(1001, "Trusted CA Cert", 0, 20),
    FAKE_CERT(1002, "Fake Cert", 30, 0),
    CHAIN_MESS(1003, "Chain Mess", 15, 0),
    SELF_SIGNED_CERT(1004, "Self Signed Cert", 10, 0),
    UNKNOWN_CA(1005, "Unknown CA", 8, 0),
    LONG_CERT_LIST(1006, "Long CertList", 5, 0),
    SYSTEM_CERT(1007, "System Cert", 0, 15),

    // 操作系统信任标签
    WINDOWS_TRUST(1008, "Windows Trust", 0, 25),
    CENTOS_TRUST(1009, "Centos Trust", 0, 25),
    APPLE_TRUST(1010, "Apple Trust", 0, 25),
    ANDROID_TRUST(1011, "Android Trust", 0, 25),
    FIREFOX_TRUST(1012, "Firefox Trust", 0, 20),

    // 证书费用类型标签
    FREE_CERTIFICATE(1015, "Free Certificate", 3, 0),
    PAID_CERTIFICATE(1016, "Paid Certificate", 0, 5),

    // 证书类型标签
    ROOT_CA(1020, "Root CA", 0, 30),
    INTERMEDIATE_CA(1021, "Intermediate CA", 0, 20),
    END_ENTITY(1022, "End Entity", 0, 5),
    EV_CERT(1023, "EV Cert", 0, 25),

    // 签名验证标签
    SIGNATURE_VERIFIED(1024, "Signature Verified", 0, 10),

    // 验签后标签处理标签
    DGA_DOMAIN_CERT(1025, "DGA Domain Cert", 25, 0),
    SUSPICIOUS_ORGANIZATION(1026, "Suspicious Organization", 15, 0),
    SUSPICIOUS_ISSUER(1027, "Suspicious Issuer", 20, 0),
    SHORT_VALIDITY_PERIOD(1028, "Short Validity Period", 8, 0),
    LONG_VALIDITY_PERIOD(1029, "Long Validity Period", 5, 0),
    WEAK_SIGNATURE_ALGORITHM(1030, "Weak Signature Algorithm", 15, 0),
    IOC_DOMAIN_CERT(1031, "IOC Domain Cert", 35, 0),
    APT_RELATED_CERT(1032, "APT Related Cert", 40, 0),

    // 威胁相关标签
    THREAT(1030, "Threat", 30, 0),
    APT(1031, "APT", 45, 0),
    MALWARE(1032, "Malware", 40, 0),
    PHISHING(1033, "Phishing", 35, 0),
    BOTNET(1034, "Botnet", 40, 0),
    TOR(1035, "Tor", 20, 0),
    BLOCKED(1036, "Blocked", 25, 0),
    SELF_SIGNED(1037, "Self-Signed", 10, 0),
    TYPOSQUATTING(1038, "Typosquatting", 25, 0),

    // 算法安全标签
    WEAK_ALGORITHM(1040, "Weak Algorithm", 15, 0),
    STRONG_ALGORITHM(1041, "Strong Algorithm", 0, 10),
    DEPRECATED_ALGORITHM(1042, "Deprecated Algorithm", 12, 0),
    INSECURE_PUBKEY(1043, "Insecure PubKey", 18, 0),

    // 证书状态标签
    EXPIRED(1050, "Expired", 20, 0),
    REVOKED(1051, "Revoked", 30, 0),
    VALID(1052, "Valid", 0, 15),

    // 证书版本和用途标签
    INSECURE_VERSION(1060, "Insecure Version", 15, 0),
    SERVER_CERT_AS_CLIENT(1061, "Server Cert as Client", 10, 0),

    // 证书注册和使用标签
    RECENTLY_REGISTERED(1062, "Recent Registered Cert", 8, 0),
    UNHOT_TLD(1063, "Unhot TLD Cert", 5, 0),

    // APT29 相关标签
    LOST_CERT_LIST(1064, "Lost CertList", 12, 0),
    SPECIAL_KEY_ID(1065, "Special Key ID", 15, 0),
    IP_IN_SAN(1066, "IP in SAN", 8, 0),
    WILDCARD_IN_ISSUER(1067, "Wild card in Issuer", 10, 0),
    LONG_VALIDITY_CERT(1068, "Long Validity Cert", 5, 0),

    // 证书类型详细标签
    USER_CERT(1070, "User Cert", 0, 5),
    LEAF_CERT(1071, "Leaf Cert", 0, 5),
    APP_LEAF_CERT(1072, "APP Leaf Cert", 0, 8),
    PRIVATE_CA(1073, "Private CA", 5, 0),
    CA_CERT(1074, "CA", 0, 15),
    DV_CERT(1075, "DV Cert", 0, 8),
    OV_CERT(1076, "OV Cert", 0, 12),

    // SAN 相关标签
    WILDCARD_IN_SAN(1080, "Wildcard in SAN", 3, 0),
    EMAIL_IN_SAN(1081, "Email in SAN", 2, 0),
    URI_IN_SAN(1082, "URI in SAN", 3, 0),

    // 域名和网络相关标签
    HOT_DOMAIN(1090, "Hot Domain", 0, 15),
    CDN_CERT(1091, "CDN Cert", 0, 10),
    TRANCO_TOP_DOMAIN(1092, "Tranco Top Domain", 0, 20),
    FAKE_HOT_DOMAIN(1093, "Fake Hot Domain", 25, 0),

    // 特殊用途标签
    CODE_SIGNING_CERT(1100, "Code Signing Cert", 0, 10),
    EMAIL_CERT(1101, "Email Cert", 0, 8),
    SERVER_AUTH_CERT(1102, "Server Auth Cert", 0, 10),
    CLIENT_AUTH_CERT(1103, "Client Auth Cert", 0, 8),

    // 威胁检测标签 - Botnet
    BOTNET_DANABOT(1110, "Botnet DanaBot Cert", 45, 0),
    BOTNET_STEALC(1111, "Botnet Stealc Cert", 45, 0),
    BOTNET_QUAKBOT(1112, "Botnet Quakbot Cert", 45, 0),

    // 威胁检测标签 - APT
    APT28_CERT(1120, "APT28", 50, 0),
    APT29_CERT(1121, "APT29", 50, 0),
    APT_PATCHWORK(1122, "Patchwork Cert", 45, 0),

    // 威胁检测标签 - Tor
    TOR_V2_CERT(1130, "Tor V2 Cert", 25, 0),
    TOR_V3_CERT(1131, "Tor V3 Cert", 25, 0),
    NETWORK_PENETRATION_CERT(1132, "Network Penetration Cert", 35, 0),

    // 威胁检测标签 - C&C 和恶意活动
    CC_CERT(1140, "C&C Cert", 40, 0),
    REMOTE_CONTROL_CERT(1141, "Remote Control Cert", 35, 0),
    MALICIOUS_DOMAIN_CERT(1142, "Malicious Domain Cert", 35, 0),
    IOC_IP_CERT(1143, "IOC IP Cert", 30, 0),
    DISTRIBUTED_SERVICES_CERT(1144, "Distributed Services Cert", 20, 0),
    MINING_CERT(1145, "Mining Cert", 25, 0),

    // 特殊签名和生成标签
    OPENSSL_SIGNED(1150, "OpenSSL Signed", 0, 5),

    // 信任相关标签
    WHITE_CERT(1160, "WhiteCert", 0, 30),
    WITHDRAW_CERT(1161, "Withdraw Cert", 15, 0),
    MICROSOFT_SERVER_GATED_CRYPTO(1162, "Microsoft Server Gated Crypto", 0, 8),
    KEY_AGREEMENT(1163, "Key Agreement", 0, 5),

    // 有效期相关标签
    SHORT_VALIDITY_CERT(1170, "Short Validity Cert", 8, 0),
    LONG_DURATION_CERT(1171, "Long Duration Cert", 5, 0),

    // 其他特殊标签
    UNCOMMON_OID(1180, "Uncommon OID", 5, 0),
    SPECIAL_EXTENSION(1181, "Special Extension", 3, 0),

    // 哈希碰撞相关标签
    MD5_COLLISION(1190, "MD5 Collision", 25, 0),

    // 证书链验证相关标签
    MISSING_CHAIN(1200, "Missing Chain", 15, 0),
    MULTIPLE_CHAINS(1201, "Multiple Chains", 8, 0),
    ILLEGAL_CHAIN(1202, "Illegal Chain", 20, 0),
    TRUSTED_CHAIN(1203, "Trusted Chain", 0, 25),
    CHAIN_LOOP(1204, "Chain Loop", 18, 0),
    FAKE_CERTIFICATE(1205, "Fake Certificate", 35, 0),
    VALIDATION_ERROR(1206, "Validation Error", 12, 0),
    TRUSTED_CA(1207, "Trusted CA", 0, 30),
    INSECURE_CHAIN(1208, "Insecure Chain", 15, 0),
    MULTI_CERT_LIST(1209, "Multi CertList", 5, 0);

    private static final Map<Integer, CertificateLabel> ID_MAP = new HashMap<>();

    static {
        for (CertificateLabel label : values()) {
            ID_MAP.put(label.id, label);
        }
    }

    private final int id;
    private final String displayName;
    private final int threatScore;
    private final int trustScore;

    CertificateLabel(int id, String displayName, int threatScore, int trustScore) {
        this.id = id;
        this.displayName = displayName;
        this.threatScore = threatScore;
        this.trustScore = trustScore;
    }

    public int getId() {
        return id;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取威胁评分
     * @return 威胁评分（0-100）
     */
    public int getThreatScore() {
        return threatScore;
    }

    /**
     * 获取信任评分
     * @return 信任评分（0-100）
     */
    public int getTrustScore() {
        return trustScore;
    }

    public static CertificateLabel fromId(int id) {
        return ID_MAP.get(id);
    }
}
