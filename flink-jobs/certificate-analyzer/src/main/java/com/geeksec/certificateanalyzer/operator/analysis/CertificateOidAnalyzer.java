package com.geeksec.certificateanalyzer.operator.analysis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.model.extension.UncommonOID;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书OID识别与分析器
 * 负责识别和分析证书中的对象标识符(OID)
 * 包括：不常见OID检测、OID描述查询、安全风险评估等
 *
 * 重构说明：
 * - 移除了对FileUtil和config.properties文件的依赖
 * - 改为使用KnowledgeBaseClient从knowledge-base服务获取OID配置数据
 * - 保持原有的业务逻辑不变
 *
 * <AUTHOR>
 * @Date 2023/12/18
 * @Modified hufengkai - 业务语义优化
 * @Date 2024/12/19
 * @Modified hufengkai - 重构为使用知识库服务
 * @Date 2024/12/22
 */
@Slf4j
public class CertificateOidAnalyzer extends RichMapFunction<X509Certificate, X509Certificate> {

    /**
     * OID分析配置，从知识库服务获取
     */
    private Map<String, String> oidAnalysisConfig = new HashMap<>();

    /**
     * 知识库客户端
     */
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书OID分析器初始化开始");

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        // 从知识库服务或配置中加载OID分析配置
        loadOidAnalysisConfig(parameters);

        log.info("证书OID分析器初始化完成，配置项: {} 条", oidAnalysisConfig.size());
    }

    /**
     * 加载OID分析配置
     */
    private void loadOidAnalysisConfig(Configuration parameters) {
        try {
            // TODO: 待knowledge-base服务补充OID配置API后，替换以下实现
            // 目前从Flink配置中读取OID分析相关配置
            oidAnalysisConfig = new HashMap<>();

            // 从Flink配置中读取OID相关配置
            String oidServiceUrl = parameters.getString("oid.service.url", "");
            String oidCacheSize = parameters.getString("oid.cache.size", "1000");
            String oidTimeout = parameters.getString("oid.timeout", "5000");

            if (!oidServiceUrl.isEmpty()) {
                oidAnalysisConfig.put("oid.service.url", oidServiceUrl);
            }
            oidAnalysisConfig.put("oid.cache.size", oidCacheSize);
            oidAnalysisConfig.put("oid.timeout", oidTimeout);

            log.info("成功加载OID分析配置: {}", oidAnalysisConfig);

        } catch (Exception e) {
            log.error("加载OID分析配置失败，使用默认配置", e);
            oidAnalysisConfig = new HashMap<>();
        }
    }

    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        log.debug("分析证书OID，证书ID: {}", cert.getDerSha1());

        // 分析证书中的OID
        analyzeOids(cert);

        // 检测不常见OID
        detectUncommonOids(cert);

        return cert;
    }

    /**
     * 分析证书中的OID
     */
    private void analyzeOids(X509Certificate cert) {
        // 分析扩展中的OID
        if (cert.getCertificateExtensions() != null &&
            cert.getCertificateExtensions().getMiscellaneousExtensions() != null) {

            Map<String, Object> extensions = cert.getCertificateExtensions().getMiscellaneousExtensions();
            for (Map.Entry<String, Object> entry : extensions.entrySet()) {
                String extensionName = entry.getKey();
                Object extensionValue = entry.getValue();

                // 检查是否包含不常见的OID
                analyzeExtensionOids(cert, extensionName, extensionValue);
            }
        }

        // 分析签名算法OID
        analyzeSignatureAlgorithmOid(cert);

        // 分析公钥算法OID
        analyzePublicKeyAlgorithmOid(cert);
    }

    /**
     * 检测不常见OID
     */
    private void detectUncommonOids(X509Certificate cert) {
        List<UncommonOID> oidLabels = cert.getUncommonOIDs();
        if (oidLabels == null) {
            oidLabels = new ArrayList<>();
        }

        List<UncommonOID> processedOidLabels = new ArrayList<>();

        for (UncommonOID oidLabel : oidLabels) {
            String oidValue = oidLabel.getOID();
            try {
                // 从知识库获取OID描述
                String description = getOidDescription(oidValue);
                oidLabel.setDescription(description);

                // 检查是否为不常见OID
                if (isUncommonOid(oidValue)) {
                    // 添加不常见OID标签
                    Set<CertificateLabel> labels = cert.getLabels();
                    if (labels == null) {
                        labels = new HashSet<>();
                    }
                    labels.add(CertificateLabel.UNCOMMON_OID);
                    cert.setLabels(labels);
                }

            } catch (Exception e) {
                log.error("OID标签处理失败，OID: {}, error: {}", oidValue, e.getMessage());
                oidLabel.setDescription("Unknown OID: " + oidValue);
            }

            processedOidLabels.add(oidLabel);
        }
        cert.setUncommonOIDs(processedOidLabels);
    }

    /**
     * 分析扩展中的OID
     */
    private void analyzeExtensionOids(X509Certificate cert, String extensionName, Object extensionValue) {
        // 检查扩展名是否为OID格式
        if (isOidFormat(extensionName)) {
            // 检查是否为不常见OID
            if (isUncommonOid(extensionName)) {
                addUncommonOid(cert, extensionName, "Extension OID");
            }
        }

        // 分析扩展值中可能包含的OID
        if (extensionValue != null) {
            String valueStr = extensionValue.toString();
            List<String> oids = extractOidsFromString(valueStr);
            for (String oid : oids) {
                if (isUncommonOid(oid)) {
                    addUncommonOid(cert, oid, "OID in extension value");
                }
            }
        }
    }

    /**
     * 分析签名算法OID
     */
    private void analyzeSignatureAlgorithmOid(X509Certificate cert) {
        String signatureAlgOid = cert.getSignatureAlgOid();
        if (signatureAlgOid != null && isUncommonOid(signatureAlgOid)) {
            addUncommonOid(cert, signatureAlgOid, "Signature Algorithm OID");
        }
    }

    /**
     * 分析公钥算法OID
     */
    private void analyzePublicKeyAlgorithmOid(X509Certificate cert) {
        String publicKeyAlgOid = cert.getPublicKeyAlgOid();
        if (publicKeyAlgOid != null && isUncommonOid(publicKeyAlgOid)) {
            addUncommonOid(cert, publicKeyAlgOid, "Public Key Algorithm OID");
        }

        String publicKeyParamOid = cert.getPublicKeyParamOid();
        if (publicKeyParamOid != null && isUncommonOid(publicKeyParamOid)) {
            addUncommonOid(cert, publicKeyParamOid, "Public Key Parameter OID");
        }
    }

    /**
     * 检查是否为不常见OID
     */
    private boolean isUncommonOid(String oid) {
        if (oid == null || oid.isEmpty()) {
            return false;
        }

        // 常见OID列表（简化版本）
        Set<String> commonOids = Set.of(
            "2.5.29.15",    // keyUsage
            "2.5.29.37",    // extKeyUsage
            "2.5.29.17",    // subjectAltName
            "2.5.29.18",    // issuerAltName
            "2.5.29.19",    // basicConstraints
            "2.5.29.31",    // cRLDistributionPoints
            "1.3.6.1.5.5.7.1.1", // authorityInfoAccess
            "2.5.29.32",    // certificatePolicies
            "2.5.29.35",    // authorityKeyIdentifier
            "2.5.29.14",    // subjectKeyIdentifier
            "1.2.840.113549.1.1.11", // sha256WithRSAEncryption
            "1.2.840.113549.1.1.5",  // sha1WithRSAEncryption
            "1.2.840.10045.4.3.2",   // ecdsa-with-SHA256
            "1.2.840.113549.1.1.1",  // rsaEncryption
            "1.2.840.10045.2.1"      // ecPublicKey
        );

        return !commonOids.contains(oid);
    }

    /**
     * 检查字符串是否为OID格式
     */
    private boolean isOidFormat(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // OID格式：数字.数字.数字...
        Pattern oidPattern = Pattern.compile("^\\d+(\\.\\d+)*$");
        return oidPattern.matcher(str).matches();
    }

    /**
     * 从字符串中提取OID
     */
    private List<String> extractOidsFromString(String str) {
        List<String> oids = new ArrayList<>();
        if (str == null || str.isEmpty()) {
            return oids;
        }

        // 简单的OID提取正则表达式
        Pattern oidPattern = Pattern.compile("\\b\\d+(?:\\.\\d+){2,}\\b");
        java.util.regex.Matcher matcher = oidPattern.matcher(str);
        while (matcher.find()) {
            oids.add(matcher.group());
        }

        return oids;
    }

    /**
     * 添加不常见OID到证书
     */
    private void addUncommonOid(X509Certificate cert, String oid, String context) {
        List<UncommonOID> uncommonOids = cert.getUncommonOIDs();
        if (uncommonOids == null) {
            uncommonOids = new ArrayList<>();
        }

        // 检查是否已存在
        boolean exists = uncommonOids.stream()
                .anyMatch(existing -> oid.equals(existing.getOID()));

        if (!exists) {
            UncommonOID uncommonOid = new UncommonOID();
            uncommonOid.setOID(oid);
            uncommonOid.setDescription(getOidDescription(oid) + " (" + context + ")");
            uncommonOids.add(uncommonOid);
            cert.setUncommonOIDs(uncommonOids);
        }
    }

    /**
     * 获取OID描述信息
     *
     * @param oidValue OID值
     * @return OID描述
     */
    private String getOidDescription(String oidValue) {
        // 从配置中获取OID描述
        return oidAnalysisConfig.getOrDefault(oidValue, "Unknown OID: " + oidValue);
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }
}
