package com.geeksec.certificateanalyzer.operator.analysis.postvalidation;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.MapFunction;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书特征分析器
 *
 * 专注于证书特征分析，包括域名特征、证书字段、时间特征、加密算法等
 * 威胁检测功能已移至CertificateThreatDetectionCoordinator
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateFeatureAnalyzer implements MapFunction<X509Certificate, X509Certificate> {

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书特征分析，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 域名特征分析
        analyzeDomainFeatures(certificate, labels);

        // 证书字段分析
        analyzeCertificateFields(certificate, labels);

        // 时间特征分析
        analyzeTemporalFeatures(certificate, labels);

        // 加密算法分析
        analyzeCryptographicFeatures(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 域名特征分析
     */
    private void analyzeDomainFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertDomains();

        for (String domain : domains) {
            // 检测域名生成算法(DGA)
            if (isDGADomain(domain)) {
                labels.add(CertificateLabel.DGA_DOMAIN_CERT);
            }

            // 检测域名抢注
            if (isTyposquattingDomain(domain)) {
                labels.add(CertificateLabel.TYPOSQUATTING);
            }

            // 检测最近注册的域名
            if (isRecentlyRegisteredDomain(domain)) {
                labels.add(CertificateLabel.RECENTLY_REGISTERED);
            }
        }
    }

    /**
     * 证书字段分析
     */
    private void analyzeCertificateFields(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 分析Subject字段
        Map<String, String> subject = certificate.getSubject();
        if (subject != null) {
            // 检测可疑的组织名称
            String organization = subject.get("O");
            if (organization != null && containsSuspiciousOrganization(organization)) {
                labels.add(CertificateLabel.SUSPICIOUS_ORGANIZATION);
            }
        }

        // 分析Issuer字段
        Map<String, String> issuer = certificate.getIssuer();
        if (issuer != null) {
            // 检测可疑的颁发者
            String issuerOrg = issuer.get("O");
            if (issuerOrg != null && containsSuspiciousIssuer(issuerOrg)) {
                labels.add(CertificateLabel.SUSPICIOUS_ISSUER);
            }
        }
    }

    /**
     * 时间特征分析
     */
    private void analyzeTemporalFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();

        if (notBefore != null && notAfter != null) {
            Duration validityPeriod = Duration.between(notBefore, notAfter);
            long validityDays = validityPeriod.toDays();

            // 检测短有效期证书（少于30天）
            if (validityDays < 30) {
                labels.add(CertificateLabel.SHORT_VALIDITY_PERIOD);
            }

            // 检测长有效期证书（超过5年）
            if (validityDays > 1825) { // 5年 = 5 * 365天
                labels.add(CertificateLabel.LONG_VALIDITY_PERIOD);
            }
        }
    }

    /**
     * 加密算法分析
     */
    private void analyzeCryptographicFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        String signatureAlgorithm = certificate.getSignatureAlgorithm();

        if (signatureAlgorithm != null) {
            // 检测弱加密算法
            if (isWeakSignatureAlgorithm(signatureAlgorithm)) {
                labels.add(CertificateLabel.WEAK_SIGNATURE_ALGORITHM);
            }
        }
    }

    // 辅助方法
    private boolean isDGADomain(String domain) {
        // 简化的DGA检测逻辑
        // 检测特征：长度超过15个字符，不包含连字符，包含3个以上数字
        return domain.length() > 15 && !domain.contains("-") &&
               domain.chars().filter(ch -> Character.isDigit(ch)).count() > 3;
    }

    private boolean isTyposquattingDomain(String domain) {
        // 简化的域名抢注检测逻辑
        // 实际实现需要与知名域名进行相似度比较
        // 这里只是一个占位实现
        return false;
    }

    private boolean isRecentlyRegisteredDomain(String domain) {
        // 简化的最近注册检测逻辑
        // 实际实现需要查询域名注册时间
        // 这里只是一个占位实现
        return false;
    }

    private boolean containsSuspiciousOrganization(String organization) {
        // 简化的可疑组织检测逻辑
        String[] suspiciousKeywords = {"test", "temp", "fake", "malware", "unknown"};
        String lowerOrg = organization.toLowerCase();
        for (String keyword : suspiciousKeywords) {
            if (lowerOrg.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private boolean containsSuspiciousIssuer(String issuer) {
        // 简化的可疑颁发者检测逻辑
        return issuer.toLowerCase().contains("self") ||
               issuer.toLowerCase().contains("unknown") ||
               issuer.toLowerCase().contains("test");
    }

    private boolean isWeakSignatureAlgorithm(String algorithm) {
        // 检测弱签名算法
        String lowerAlgorithm = algorithm.toLowerCase();
        return lowerAlgorithm.contains("md5") ||
               lowerAlgorithm.contains("sha1") ||
               lowerAlgorithm.contains("md2");
    }
}