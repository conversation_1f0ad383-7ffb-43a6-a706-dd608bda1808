package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.List;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 威胁情报检测器
 * 使用懒加载模式，通过KnowledgeBaseClient检测证书中的域名和IP是否在IOC威胁情报列表中
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class ThreatIntelligenceDetector extends BaseCertificateDetector {

    /** 知识库客户端 */
    private final KnowledgeBaseClient knowledgeBaseClient;

    public ThreatIntelligenceDetector() {
        super("Threat Intelligence Detector");
        this.knowledgeBaseClient = new KnowledgeBaseClient();
        log.info("威胁情报检测器初始化完成，使用懒加载模式");
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        List<String> domains = cert.getCertDomains();
        List<String> ips = cert.getCertIps();

        // 懒加载检查IOC域名
        for (String domain : domains) {
            if (knowledgeBaseClient.isIocDomain(domain)) {
                cert.getLabels().add(CertificateLabel.IOC_DOMAIN_CERT);
                log.debug("检测到IOC威胁域名: {}", domain);
                break;
            }
        }

        // 懒加载检查IOC IP
        for (String ip : ips) {
            if (knowledgeBaseClient.isIocIp(ip)) {
                cert.getLabels().add(CertificateLabel.IOC_IP_CERT);
                log.debug("检测到IOC威胁IP: {}", ip);
                break;
            }
        }
    }
}
