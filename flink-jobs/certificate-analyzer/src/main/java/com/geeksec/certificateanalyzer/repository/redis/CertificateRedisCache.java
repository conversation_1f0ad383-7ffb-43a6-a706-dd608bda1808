package com.geeksec.certificateanalyzer.repository.redis;

import static com.geeksec.common.core.constants.ConfigConstants.REDIS_HOST;
import static com.geeksec.common.core.constants.ConfigConstants.REDIS_KEY_TTL;
import static com.geeksec.common.core.constants.ConfigConstants.REDIS_POOL_MAX_TOTAL;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.flink.api.java.utils.ParameterTool;


import com.geeksec.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.params.SetParams;
import redis.clients.jedis.resps.ScanResult;

/**
 * 证书Redis缓存操作类
 * 提供证书相关的Redis缓存和去重功能
 *
 * <AUTHOR>
 * @Date 2022/10/26
 */
@Slf4j
public class CertificateRedisCache {

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    public static final String REDIS_ADDR = CONFIG.get(REDIS_HOST, "localhost");
    public static final Integer REDIS_PORT = CONFIG.getInt("redis.port", 6379);
    public static final Integer REDIS_TIMEOUT = CONFIG.getInt("redis.timeout", 10000);
    public static final Integer REDIS_MAX_TOTAL = CONFIG.getInt(REDIS_POOL_MAX_TOTAL, 200);

    private static Integer REDIS_EXPIRE_SECOND = CONFIG.getInt(REDIS_KEY_TTL, 86400);
    private static String REDIS_PASSWORD = CONFIG.get("redis.password", "");

    public static JedisPool initJedisPool() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(REDIS_MAX_TOTAL); // 最大可用连接数
        jedisPoolConfig.setBlockWhenExhausted(true); // 连接耗尽是否等待
        jedisPoolConfig.setMaxWaitMillis(60); // 等待时间
        jedisPoolConfig.setMaxIdle(50); // 最大闲置连接数
        jedisPoolConfig.setMinIdle(50); // 最小闲置连接数
        jedisPoolConfig.setTestOnBorrow(false); // 取连接的时候进行一下测试 ping pong
        jedisPoolConfig.setTestOnReturn(false);
        jedisPoolConfig.setTestWhileIdle(true);
        JedisPool jedisPool = new JedisPool(jedisPoolConfig, REDIS_ADDR, REDIS_PORT, REDIS_TIMEOUT);// , REDIS_PASSWORD
        return jedisPool;
    }

    public static Jedis getJedis(JedisPool jedisPool) {
        // log.info("活跃连接数——{}——，空闲数——{}——，等待数——{}——",jedisPool.getNumActive(),jedisPool.getNumIdle(),jedisPool.getNumWaiters());
        if (jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }
        Jedis jedis = jedisPool.getResource();
        // 设置连接的redis的db号数
        jedis.select(2);
        // 设置redis的连接密码
        // jedis.auth(REDIS_PASSWORD);
        // log.info("从jedis连接池获取jedis成功！,jedisPool is {},jedis is {}", jedisPool,
        // jedis);
        return jedis;
    }

    private static SetParams params = new SetParams();
    // 设置过期时间为10分钟
    static {
        params.ex(2 * 60);
    }

    // 设置redis中分段hash的键
    public static void setRedisCertHashList(LinkedList<String> PositiveHash, LinkedList<String> NegativeHash,
            String SHA1, Jedis jedis) {
        if (jedis.exists(String.format("%s_PositiveHash", SHA1))) {
            jedis.ltrim(String.format("%s_PositiveHash", SHA1), 1, 0);
        }
        if (jedis.exists(String.format("%s_NegativeHash", SHA1))) {
            jedis.ltrim(String.format("%s_NegativeHash", SHA1), 1, 0);
        }
        for (String hash : PositiveHash) {
            jedis.rpush(String.format("%s_PositiveHash", SHA1), hash);
        }
        jedis.expire(String.format("%s_PositiveHash", SHA1), 10 * 60);
        for (String hash : NegativeHash) {
            jedis.rpush(String.format("%s_NegativeHash", SHA1), hash);
        }
        jedis.expire(String.format("%s_NegativeHash", SHA1), 10 * 60);
    }

    // 全量查询并根据列表长度进行过滤,查询的是比特反转
    public static String scanRedisReverse(int listLength, LinkedList<String> PositiveHash, Jedis jedis) {
        String cursor = ScanParams.SCAN_POINTER_START;
        ScanParams scanParams = new ScanParams().count(100); // 每次迭代返回100个键

        do {
            ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
            cursor = scanResult.getCursor();
            List<String> keys = scanResult.getResult();

            for (String key : keys) {
                long length = jedis.llen(key); // 获取列表长度
                if (length == listLength && key.contains("Positive")) {
                    int wrong_count = 0;
                    for (int i = 0; i < length; i++) {
                        if (!PositiveHash.get(i).equals(jedis.lindex(key, i))) {
                            wrong_count += 1;
                        }
                        if (wrong_count > 1) {
                            break;
                        }
                        if (wrong_count == 1 && i == length - 1) {
                            return key.split(":")[0];
                        }
                    }
                }
            }
        } while (!cursor.equals("0"));
        return null;
    }

    // 全量查询并根据列表长度进行过滤,查询的是Positive的比特冗余或者缺失
    public static String scanRedisNum(int listLength, LinkedList<String> Hash, Jedis jedis, String HashType) {
        String cursor = ScanParams.SCAN_POINTER_START;
        ScanParams scanParams = new ScanParams().count(100); // 每次迭代返回100个键

        do {
            ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
            cursor = scanResult.getCursor();
            List<String> keys = scanResult.getResult();

            for (String key : keys) {
                long length = jedis.llen(key); // 获取列表长度
                if (length == listLength && key.contains(HashType)) {
                    boolean status = true;
                    int changeCount = 0;
                    for (int i = 0; i < length; i++) {
                        if (i == 0) {
                            status = Hash.get(i).equals(jedis.lindex(key, i));
                        }
                        if (Hash.get(i).equals(jedis.lindex(key, i)) != status) {
                            changeCount += 1;
                            status = !status;
                        }
                        if (changeCount > 1) {
                            break;
                        }
                        if (changeCount == 1 && i == length - 1) {
                            return key.split(":")[0];
                        }
                    }
                }
            }
        } while (!cursor.equals("0"));
        return null;
    }

    public static Map<String, String> getSourceCert(JedisPool jedisPooDb1, String SHA1) {
        Jedis redisClientDb1 = null;
        Map<String, String> certMap = new HashMap<>();
        try {
            redisClientDb1 = CertificateRedisCache.getJedis(jedisPooDb1);
            redisClientDb1.select(4);
            // 直接往redis里面写base64编码后的数据
            String byteString = redisClientDb1.get(SHA1);
            certMap.put(SHA1, byteString);
            return certMap;
        } catch (Exception e) {
            log.error("redis原文件查询设置失败，error--->{},SHA1 is--->{}", e, SHA1);
            return certMap;
        } finally {
            if (redisClientDb1 != null) {
                redisClientDb1.close();
            }
        }
    }


}
