package com.geeksec.certificateanalyzer.operator.analysis;

import java.util.Map;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.enums.BusinessCategory;
import com.geeksec.certificateanalyzer.enums.IndustryCategory;
import com.geeksec.certificateanalyzer.enums.IssuerCategory;
import com.geeksec.certificateanalyzer.enums.UserCategory;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

/**
 * 证书类别分类器
 * 负责根据不同维度（用户、业务、CA、行业）对证书进行分类。
 * 该类通过调用相应的枚举类来完成分类逻辑，实现了高内聚和类型安全。
 */
public class CertificateCategoryClassifier {

    /**
     * 对证书进行分类。
     *
     * @param certificate 待分类的证书
     */
    public void classify(X509Certificate certificate) {
        Map<String, String> subjectInfo = certificate.getSubject();
        String commonName = subjectInfo != null ? subjectInfo.getOrDefault(CertificateConstants.FIELD_CN, "") : "";
        Map<String, String> issuerInfo = certificate.getIssuer();

        // 使用枚举进行分类
        certificate.setUserCategory(UserCategory.findByText(commonName));
        certificate.setBusinessCategory(BusinessCategory.findByText(commonName));
        String issuerCommonName = issuerInfo != null ? issuerInfo.getOrDefault(CertificateConstants.FIELD_CN, "") : "";
        certificate.setIssuerCategory(IssuerCategory.findByIssuer(issuerCommonName));
        certificate.setIndustryCategory(IndustryCategory.findByCommonName(commonName));
    }
}
