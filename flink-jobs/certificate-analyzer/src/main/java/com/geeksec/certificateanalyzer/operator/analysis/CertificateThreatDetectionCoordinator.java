package com.geeksec.certificateanalyzer.operator.analysis;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.CertificateDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.C2ThreatDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.DistributedServicesDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.MaliciousDomainDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.MiningThreatDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.OpenSSLSelfSignDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.ThreatIntelligenceDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.TorDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.TyposquattingDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.apt.APT28Detector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.apt.APT29Detector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.apt.PatchWorkDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.botnet.DanaBotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.botnet.QuakbotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detector.impl.botnet.StealcDetector;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书威胁检测协调器
 *
 * 职责：
 * 1. 管理和初始化所有威胁检测器
 * 2. 协调检测器的执行顺序
 * 3. 管理共享资源（如KnowledgeBaseClient）
 * 4. 处理检测器异常和错误隔离
 * 5. 提供统一的检测入口点
 *
 * 支持的威胁检测器：
 * - APT威胁检测器
 * - Botnet威胁检测器（DanaBot、Quakbot、Stealc等）
 * - Tor网络检测器
 * - C2威胁检测器
 * - 恶意域名检测器
 * - 域名仿冒检测器
 *
 * 架构优势：
 * - 模块化设计，每个检测器职责单一
 * - 易于扩展新的检测器
 * - 良好的错误隔离
 * - 统一的资源管理
 * - 使用单例模式的KnowledgeBaseClient提高性能
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateThreatDetectionCoordinator extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 所有威胁检测器列表 */
    private List<CertificateDetector> detectors;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化所有威胁检测器
        initializeDetectors();

        log.info("证书威胁检测器协调器初始化完成，共加载 {} 个检测器", detectors.size());
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("分析证书威胁特征，证书ID: {}", certificate.getDerSha1());

        // 依次调用所有检测器进行威胁检测
        for (CertificateDetector detector : detectors) {
            try {
                detector.detect(certificate);
            } catch (Exception e) {
                log.error("检测器 {} 执行失败: {}", detector.getName(), e.getMessage(), e);
                // 继续执行其他检测器，不因单个检测器失败而中断整个流程
            }
        }

        return certificate;
    }

    /**
     * 初始化所有威胁检测器
     */
    private void initializeDetectors() {
        detectors = Arrays.asList(
            // Botnet检测器
            new DanaBotDetector(),
            new StealcDetector(),
            new QuakbotDetector(),

            // APT检测器
            new APT28Detector(),
            new APT29Detector(),
            new PatchWorkDetector(),

            // 网络渗透检测器
            new TorDetector(),

            // 威胁情报检测器
            new C2ThreatDetector(),
            new MaliciousDomainDetector(),
            new ThreatIntelligenceDetector(),
            new MiningThreatDetector(),

            // 域名相关检测器
            new TyposquattingDetector(),
            new DistributedServicesDetector(),

            // 特殊签名检测器
            new OpenSSLSelfSignDetector()
        );

        log.info("初始化威胁检测器: {}",
            detectors.stream().map(CertificateDetector::getName).toArray());
    }

    @Override
    public void close() throws Exception {
        // 检测器会自己管理知识库客户端的生命周期
        log.debug("证书威胁检测器协调器关闭完成");
        super.close();
    }
}
