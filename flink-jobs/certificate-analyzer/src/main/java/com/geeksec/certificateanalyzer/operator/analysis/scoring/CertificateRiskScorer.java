package com.geeksec.certificateanalyzer.operator.analysis.scoring;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.util.KnowledgeBaseUtils;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 证书风险评分器
 *
 * 重构说明：
 * - 移除了对FileUtil和CSV文件的依赖
 * - 改为使用KnowledgeBaseClient从knowledge-base服务获取数据
 * - 保持原有的业务逻辑不变
 *
 * <AUTHOR>
 * @Date 2022/11/8
 * @Modified hufengkai - 重构为使用知识库服务
 * @Date 2024/12/22
 */

@Slf4j
public class CertificateRiskScorer extends RichMapFunction<X509Certificate, X509Certificate> {

    // 知识库客户端
    private transient KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书评分映射函数初始化开始");

        // 创建知识库客户端实例
        knowledgeBaseClient = KnowledgeBaseUtils.createInstance(parameters);

        log.info("证书评分映射函数初始化完成，使用懒加载模式");
    }



    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        // 将CertificateLabel枚举转换为ID列表用于评分计算
        ArrayList<Integer> labelIds = cert.getLabels().stream()
                .map(CertificateLabel::getId)
                .collect(Collectors.toCollection(ArrayList::new));

        // 计算威胁评分和信任评分（按需查询）
        cert.setThreatScore(getBlackScore(labelIds));
        cert.setTrustScore(getWhiteScore(labelIds));
        cert.setImportTime(LocalDateTime.now());
        return cert;
    }



    /**
     * 计算黑名单评分（按需查询）
     *
     * @param labelIds 标签ID列表
     * @return 计算得出的威胁评分
     */
    private int getBlackScore(ArrayList<Integer> labelIds) {
        int score = 0;

        for (Integer labelId : labelIds) {
            try {
                Integer labelScore = knowledgeBaseClient.getCertificateBlackScoreByLabelId(labelId);
                score += labelScore;
            } catch (Exception e) {
                log.warn("查询黑名单评分失败，标签ID: {}", labelId, e);
            }
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 计算白名单评分（按需查询）
     *
     * @param labelIds 标签ID列表
     * @return 计算得出的信任评分
     */
    private int getWhiteScore(ArrayList<Integer> labelIds) {
        int score = 0;

        for (Integer labelId : labelIds) {
            try {
                // 按需从知识库查询白名单评分
                Integer labelScore = knowledgeBaseClient.getCertificateWhiteScoreByLabelId(labelId);
                score += labelScore;
            } catch (Exception e) {
                log.warn("查询白名单评分失败，标签ID: {}", labelId, e);
            }
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            try {
                knowledgeBaseClient.close();
            } catch (Exception e) {
                log.error("关闭知识库客户端失败: {}", e.getMessage(), e);
            }
        }
        log.debug("证书评分映射函数关闭完成");
        super.close();
    }
}
