package com.geeksec.certificateanalyzer.operator.analysis.scoring;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import org.apache.flink.api.common.functions.MapFunction;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 证书风险评分器
 *
 * 重构说明：
 * - 移除了对知识库服务的依赖
 * - 改为直接使用CertificateLabel枚举中定义的评分
 * - 简化了架构，提高了性能
 *
 * <AUTHOR>
 * @Date 2022/11/8
 * @Modified hufengkai - 重构为使用枚举评分
 * @Date 2025/01/15
 */

@Slf4j
public class CertificateRiskScorer implements MapFunction<X509Certificate, X509Certificate> {



    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        // 直接使用标签枚举计算评分
        cert.setThreatScore(calculateThreatScore(cert.getLabels()));
        cert.setTrustScore(calculateTrustScore(cert.getLabels()));
        cert.setImportTime(LocalDateTime.now());
        return cert;
    }

    /**
     * 计算威胁评分
     *
     * @param labels 证书标签集合
     * @return 计算得出的威胁评分
     */
    private int calculateThreatScore(java.util.Set<CertificateLabel> labels) {
        int score = 0;

        for (CertificateLabel label : labels) {
            score += label.getThreatScore();
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 计算信任评分
     *
     * @param labels 证书标签集合
     * @return 计算得出的信任评分
     */
    private int calculateTrustScore(java.util.Set<CertificateLabel> labels) {
        int score = 0;

        for (CertificateLabel label : labels) {
            score += label.getTrustScore();
        }

        // 超过100取100
        return Math.min(score, 100);
    }
}
