package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet;

import java.util.Map;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.certificateanalyzer.util.nlp.MultiWordNounChecker;

import lombok.extern.slf4j.Slf4j;

/**
 * Quakbot 恶意软件证书检测器
 * <p>
 * 检测 Quakbot 恶意软件使用的证书特征：
 * - 主题字段结构特定（CN, OU, C）
 * - 颁发者字段结构特定（CN, L, O, ST, C）
 * - CN 字段相同但不是自签名
 * - 序列号为4位16进制
 * - 使用 NLP 技术进行多词名词检测
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class QuakbotDetector extends BaseCertificateDetector {

    public QuakbotDetector() {
        super("Quakbot Detector");
    }

    protected boolean hasAllRequiredFields(Map<String, String> map, String... keys) {
        if (map == null)
            return false;
        for (String key : keys) {
            if (!map.containsKey(key)) {
                return false;
            }
        }
        return true;
    }

    protected String getStringValue(Map<String, String> map, String key) {
        if (map == null || key == null)
            return "";
        String value = map.get(key);
        return value != null ? value.toLowerCase() : "";
    }

    protected boolean isReadableText(String text, boolean allowNumbers) {
        if (text == null || text.isEmpty()) {
            return false;
        }

        // 检查是否只包含可打印ASCII字符
        if (!text.matches("^[\\x20-\\x7E]+$")) {
            return false;
        }

        // 检查是否包含至少一个字母
        if (!text.matches(".*[a-zA-Z].*")) {
            return false;
        }

        // 如果不允许数字，检查是否包含数字
        if (!allowNumbers && text.matches(".*\\d.*")) {
            return false;
        }

        return true;
    }

    protected boolean isSelfSigned(X509Certificate cert) {
        if (cert == null) {
            return false;
        }

        Map<String, String> subject = cert.getSubject();
        Map<String, String> issuer = cert.getIssuer();

        if (subject == null || issuer == null) {
            return false;
        }

        // 比较主题和颁发者的所有字段是否相同
        return subject.equals(issuer);
    }

    @Override
    public void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        // 使用 NLP 多词名词检测器进行 Quakbot 检测
        if (MultiWordNounChecker.isQuakbotCertificate(cert)) {
            // 额外检查序列号长度是否为4位16进制（Quakbot特征）
            String serialNumber = cert.getSerialNumber();
            try {
                if (serialNumber != null && serialNumber.matches("[0-9a-fA-F]{4}")) {
                    cert.getLabels().add(CertificateLabel.BOTNET_QUAKBOT);
                    log.debug("检测到潜在的 Quakbot 证书: {}", cert.getCommonName());
                }
            } catch (Exception e) {
                log.debug("Quakbot 序列号检查失败", e);
            }
        }
    }
}
