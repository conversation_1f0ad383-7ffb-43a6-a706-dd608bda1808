package com.geeksec.certificateanalyzer.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 证书信任状态枚举
 * <p>
 * 该状态描述了证书基于系统配置的可信CA库的验证结果，与证书的来源无关。
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Getter
@ToString(of = "displayName")
public enum CertificateTrustStatus {
    /**
     * 未知 - 尚未进行信任验证
     */
    UNKNOWN("未知"),

    /**
     * 可信 - 证书链能够成功验证到系统配置的可信根CA
     */
    TRUSTED("可信"),

    /**
     * 不可信 - 证书链无法验证到任何可信根CA
     */
    UNTRUSTED("不可信"),

    /**
     * 自签名 - 证书是自签名的
     */
    SELF_SIGNED("自签名"),

    /**
     * 已吊销 - 证书在吊销列表中
     */
    REVOKED("已吊销"),

    /**
     * 已过期 - 证书已过有效期
     */
    EXPIRED("已过期");

    private final String displayName;

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     */
    CertificateTrustStatus(String displayName) {
        this.displayName = displayName;
    }
}
