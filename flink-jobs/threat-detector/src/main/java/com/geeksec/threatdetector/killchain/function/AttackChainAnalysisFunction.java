package com.geeksec.threatdetector.killchain.function;

import com.geeksec.threatdetector.killchain.analyzer.AttackChainAnalyzer;
import com.geeksec.threatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import com.geeksec.threatdetector.model.output.Alarm;
import com.geeksec.threatdetector.state.CoreStateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 攻击链分析Flink函数
 * 在Flink数据流中对告警进行攻击链分析
 * 
 * <AUTHOR>
 */
@Slf4j
public class AttackChainAnalysisFunction extends RichMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 攻击链分析器
     */
    private transient AttackChainAnalyzer attackChainAnalyzer;

    /**
     * 核心状态管理器
     */
    private transient CoreStateManager coreStateManager;
    
    /**
     * 是否启用攻击链分析
     */
    private final boolean analysisEnabled;
    
    /**
     * 是否将分析结果附加到告警中
     */
    private final boolean attachAnalysisResult;
    
    /**
     * 事件关联窗口（分钟）
     */
    private final int correlationWindowMinutes;
    
    /**
     * 统计信息输出间隔（秒）
     */
    private final int statisticsIntervalSeconds;
    
    /**
     * 攻击链清理间隔（小时）
     */
    private final int cleanupIntervalHours;
    
    /**
     * 定时任务执行器
     */
    private transient ScheduledExecutorService scheduledExecutor;
    
    /**
     * 统计信息
     */
    private transient long processedCount = 0;
    private transient long analyzedCount = 0;
    private transient long chainCreatedCount = 0;
    private transient long chainUpdatedCount = 0;
    private transient long errorCount = 0;
    private transient long startTime = 0;
    
    /**
     * 构造函数
     * 
     * @param analysisEnabled 是否启用攻击链分析
     * @param attachAnalysisResult 是否将分析结果附加到告警中
     * @param correlationWindowMinutes 事件关联窗口（分钟）
     * @param statisticsIntervalSeconds 统计信息输出间隔（秒）
     * @param cleanupIntervalHours 攻击链清理间隔（小时）
     */
    public AttackChainAnalysisFunction(boolean analysisEnabled,
                                     boolean attachAnalysisResult,
                                     int correlationWindowMinutes,
                                     int statisticsIntervalSeconds,
                                     int cleanupIntervalHours) {
        this.analysisEnabled = analysisEnabled;
        this.attachAnalysisResult = attachAnalysisResult;
        this.correlationWindowMinutes = correlationWindowMinutes;
        this.statisticsIntervalSeconds = statisticsIntervalSeconds;
        this.cleanupIntervalHours = cleanupIntervalHours;
    }
    
    /**
     * 默认构造函数
     */
    public AttackChainAnalysisFunction() {
        this(true, true, 60, 300, 24);
    }
    
    /**
     * 创建快速分析函数（短关联窗口）
     * 
     * @return 快速分析函数
     */
    public static AttackChainAnalysisFunction createFastAnalysis() {
        return new AttackChainAnalysisFunction(true, true, 30, 300, 12);
    }
    
    /**
     * 创建深度分析函数（长关联窗口）
     * 
     * @return 深度分析函数
     */
    public static AttackChainAnalysisFunction createDeepAnalysis() {
        return new AttackChainAnalysisFunction(true, true, 120, 300, 48);
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        if (!analysisEnabled) {
            log.info("攻击链分析功能已禁用");
            return;
        }
        
        // 初始化攻击链分析器
        attackChainAnalyzer = new AttackChainAnalyzer(correlationWindowMinutes);

        // 初始化核心状态管理器
        coreStateManager = CoreStateManager.getInstance();
        
        // 记录开始时间
        startTime = System.currentTimeMillis();
        
        // 启动定时任务
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "AttackChainAnalysis-Timer");
            t.setDaemon(true);
            return t;
        });
        
        // 定时输出统计信息
        if (statisticsIntervalSeconds > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::printStatistics,
                    statisticsIntervalSeconds,
                    statisticsIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        // 定时清理过期攻击链
        if (cleanupIntervalHours > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::cleanupExpiredChains,
                    cleanupIntervalHours,
                    cleanupIntervalHours,
                    TimeUnit.HOURS
            );
        }
        
        log.info("攻击链分析函数初始化完成，关联窗口: {}分钟, 清理间隔: {}小时", 
                correlationWindowMinutes, cleanupIntervalHours);
    }
    
    @Override
    public Alarm map(Alarm alarm) throws Exception {
        if (alarm == null) {
            return null;
        }
        
        processedCount++;
        
        // 如果未启用分析，直接返回原告警
        if (!analysisEnabled || attackChainAnalyzer == null) {
            return alarm;
        }
        
        try {
            // 检查检测器是否启用
            if (!coreStateManager.isDetectorEnabled("AttackChainAnalyzer")) {
                log.debug("攻击链分析器已禁用，跳过分析");
                return alarm;
            }

            // 执行攻击链分析
            AttackChainAnalyzer.AttackChainAnalysisResult analysisResult =
                    attackChainAnalyzer.analyzeAlarm(alarm);

            if (analysisResult.isSuccess()) {
                analyzedCount++;

                if (analysisResult.isNewChainCreated()) {
                    chainCreatedCount++;
                } else {
                    chainUpdatedCount++;
                }

                // 如果需要将分析结果附加到告警中
                if (attachAnalysisResult) {
                    alarm = attachAnalysisResultToAlarm(alarm, analysisResult);
                }

                log.debug("攻击链分析完成: 告警={}, 攻击链={}, 阶段={}",
                        alarm.getAlarmId(), analysisResult.getAttackChainId(),
                        analysisResult.getKillChainStage());
            } else {
                errorCount++;
                log.warn("攻击链分析失败: 告警={}, 错误={}",
                        alarm.getAlarmId(), analysisResult.getErrorMessage());
            }

        } catch (Exception e) {
            errorCount++;
            log.error("攻击链分析异常: 告警={}, 错误={}", alarm.getAlarmId(), e.getMessage(), e);
        }
        
        return alarm;
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        
        log.info("关闭攻击链分析函数");
        
        // 关闭定时任务
        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 打印最终统计信息
        printFinalStatistics();
    }
    
    /**
     * 将分析结果附加到告警中
     * 
     * @param alarm 原始告警
     * @param analysisResult 分析结果
     * @return 附加了分析结果的告警
     */
    private Alarm attachAnalysisResultToAlarm(Alarm alarm,
                                            AttackChainAnalyzer.AttackChainAnalysisResult analysisResult) {
        // 直接修改告警对象（简化处理）
        Alarm enrichedAlarm = alarm;

        // 添加攻击链信息到扩展属性
        if (enrichedAlarm.getAttributes() == null) {
            enrichedAlarm.setAttributes(new java.util.HashMap<>());
        }

        // 添加攻击链ID
        enrichedAlarm.getAttributes().put("attack_chain_id", analysisResult.getAttackChainId());
        
        // 添加Kill Chain阶段
        if (analysisResult.getKillChainStage() != null) {
            CyberKillChainStage stage = analysisResult.getKillChainStage();
            enrichedAlarm.getAttributes().put("kill_chain_stage", stage.getStageCode());
            enrichedAlarm.getAttributes().put("kill_chain_stage_name", stage.getChineseName());
            enrichedAlarm.getAttributes().put("kill_chain_stage_number", stage.getStageNumber());
            enrichedAlarm.getAttributes().put("kill_chain_stage_description", stage.getDescription());
            enrichedAlarm.getAttributes().put("kill_chain_urgency", stage.getUrgencyDescription());
            enrichedAlarm.getAttributes().put("kill_chain_severity_level", stage.getSeverityLevel());
        }
        
        // 添加攻击链分析结果
        if (analysisResult.getAttackChainAnalysis() != null) {
            AttackChainAnalysis analysis = analysisResult.getAttackChainAnalysis();
            
            // 添加基本信息
            enrichedAlarm.getAttributes().put("attack_campaign_name", analysis.getCampaignName());
            enrichedAlarm.getAttributes().put("attack_chain_confidence", analysis.getConfidence());
            enrichedAlarm.getAttributes().put("attack_chain_event_count",
                    analysis.getEvents() != null ? analysis.getEvents().size() : 0);

            // 添加进展评估
            if (analysis.getProgressAssessment() != null) {
                AttackChainAnalysis.AttackProgressAssessment progress = analysis.getProgressAssessment();
                enrichedAlarm.getAttributes().put("attack_progress_percentage", progress.getProgressPercentage());
                enrichedAlarm.getAttributes().put("attack_success_probability", progress.getSuccessProbability());
                enrichedAlarm.getAttributes().put("attack_complexity",
                        progress.getComplexity() != null ? progress.getComplexity().getDescription() : "未知");
                enrichedAlarm.getAttributes().put("attack_persistence",
                        progress.getPersistence() != null ? progress.getPersistence().getDescription() : "未知");
                enrichedAlarm.getAttributes().put("next_possible_actions", progress.getNextPossibleActions());
            }
            
            // 添加威胁评估
            if (analysis.getThreatAssessment() != null) {
                AttackChainAnalysis.ThreatAssessment threat = analysis.getThreatAssessment();
                enrichedAlarm.getAttributes().put("threat_level",
                        threat.getThreatLevel() != null ? threat.getThreatLevel().getDescription() : "未知");
                enrichedAlarm.getAttributes().put("threat_risk_score", threat.getRiskScore());
                enrichedAlarm.getAttributes().put("threat_types", threat.getThreatTypes());
                enrichedAlarm.getAttributes().put("threat_source",
                        threat.getThreatSource() != null ? threat.getThreatSource().getDescription() : "未知");
            }

            // 添加时间线信息
            if (analysis.getTimeline() != null) {
                AttackChainAnalysis.AttackChainTimeline timeline = analysis.getTimeline();
                enrichedAlarm.getAttributes().put("attack_start_time", timeline.getStartTime());
                enrichedAlarm.getAttributes().put("attack_duration_seconds", timeline.getDurationSeconds());
                enrichedAlarm.getAttributes().put("attack_is_active", timeline.getEndTime() == null);
            }
        }

        // 添加分析元信息
        enrichedAlarm.getAttributes().put("attack_chain_analysis_time",
                java.time.LocalDateTime.now().toString());
        enrichedAlarm.getAttributes().put("attack_chain_new_created", analysisResult.isNewChainCreated());
        
        return enrichedAlarm;
    }
    
    /**
     * 定时打印统计信息
     */
    private void printStatistics() {
        try {
            long currentTime = System.currentTimeMillis();
            long runningTime = currentTime - startTime;
            
            log.info("=== 攻击链分析统计信息 ===");
            log.info("运行时间: {}秒", runningTime / 1000);
            log.info("处理告警数: {}", processedCount);
            log.info("分析成功数: {}", analyzedCount);
            log.info("创建攻击链数: {}", chainCreatedCount);
            log.info("更新攻击链数: {}", chainUpdatedCount);
            log.info("分析失败数: {}", errorCount);
            
            if (processedCount > 0) {
                double successRate = (double) analyzedCount / processedCount * 100;
                double errorRate = (double) errorCount / processedCount * 100;
                log.info("分析成功率: {:.2f}%", successRate);
                log.info("分析失败率: {:.2f}%", errorRate);
            }
            
            if (runningTime > 0) {
                double throughput = (double) processedCount / (runningTime / 1000.0);
                log.info("处理吞吐量: {:.2f} 告警/秒", throughput);
            }
            
            // 攻击链分析器统计信息
            if (attackChainAnalyzer != null) {
                AttackChainAnalyzer.AnalysisStatistics analyzerStats = attackChainAnalyzer.getStatistics();
                log.info("分析器统计:");
                log.info("  总分析数: {}", analyzerStats.getTotalAnalyzed());
                log.info("  攻击链创建数: {}", analyzerStats.getChainsCreated());
                log.info("  攻击链更新数: {}", analyzerStats.getChainsUpdated());
                log.info("  活跃攻击链数: {}", analyzerStats.getActiveChains());
                log.info("  攻击链创建率: {:.2f}%", analyzerStats.getChainCreationRate() * 100);
                log.info("  攻击链更新率: {:.2f}%", analyzerStats.getChainUpdateRate() * 100);
            }
            
            log.info("=== 统计信息完成 ===");
            
        } catch (Exception e) {
            log.error("打印统计信息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理过期攻击链
     */
    private void cleanupExpiredChains() {
        try {
            if (attackChainAnalyzer != null) {
                int beforeCount = attackChainAnalyzer.getStatistics().getActiveChains();
                attackChainAnalyzer.cleanupExpiredChains(cleanupIntervalHours * 2); // 清理超过2倍间隔的攻击链
                int afterCount = attackChainAnalyzer.getStatistics().getActiveChains();
                
                log.info("攻击链清理完成: 清理前={}, 清理后={}, 清理数量={}", 
                        beforeCount, afterCount, beforeCount - afterCount);
            }
        } catch (Exception e) {
            log.error("攻击链清理失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 打印最终统计信息
     */
    private void printFinalStatistics() {
        long currentTime = System.currentTimeMillis();
        long totalRunningTime = currentTime - startTime;
        
        log.info("=== 攻击链分析最终统计 ===");
        log.info("总运行时间: {}秒", totalRunningTime / 1000);
        log.info("总处理告警数: {}", processedCount);
        log.info("分析成功数: {}", analyzedCount);
        log.info("创建攻击链数: {}", chainCreatedCount);
        log.info("更新攻击链数: {}", chainUpdatedCount);
        log.info("分析失败数: {}", errorCount);
        
        if (processedCount > 0) {
            double successRate = (double) analyzedCount / processedCount * 100;
            double errorRate = (double) errorCount / processedCount * 100;
            log.info("分析成功率: {:.2f}%", successRate);
            log.info("分析失败率: {:.2f}%", errorRate);
        }
        
        if (totalRunningTime > 0) {
            double avgThroughput = (double) processedCount / (totalRunningTime / 1000.0);
            log.info("平均吞吐量: {:.2f} 告警/秒", avgThroughput);
        }
        
        if (attackChainAnalyzer != null) {
            AttackChainAnalyzer.AnalysisStatistics finalStats = attackChainAnalyzer.getStatistics();
            log.info("分析器最终统计:");
            log.info("  总分析数: {}", finalStats.getTotalAnalyzed());
            log.info("  攻击链创建率: {:.2f}%", finalStats.getChainCreationRate() * 100);
            log.info("  攻击链更新率: {:.2f}%", finalStats.getChainUpdateRate() * 100);
            log.info("  最终活跃攻击链数: {}", finalStats.getActiveChains());
        }
        
        log.info("=== 最终统计完成 ===");
    }
    
    /**
     * 获取当前统计信息
     * 
     * @return 统计信息
     */
    public AnalysisFunctionStatistics getCurrentStatistics() {
        long currentTime = System.currentTimeMillis();
        long runningTime = currentTime - startTime;
        
        return new AnalysisFunctionStatistics(
                processedCount,
                analyzedCount,
                chainCreatedCount,
                chainUpdatedCount,
                errorCount,
                runningTime,
                correlationWindowMinutes,
                cleanupIntervalHours
        );
    }
    
    /**
     * 分析函数统计信息
     */
    @lombok.Getter
    @lombok.AllArgsConstructor
    public static class AnalysisFunctionStatistics {
        private final long processedCount;
        private final long analyzedCount;
        private final long chainCreatedCount;
        private final long chainUpdatedCount;
        private final long errorCount;
        private final long runningTimeMs;
        private final int correlationWindowMinutes;
        private final int cleanupIntervalHours;
        
        public double getSuccessRate() {
            return processedCount > 0 ? (double) analyzedCount / processedCount : 0.0;
        }
        
        public double getErrorRate() {
            return processedCount > 0 ? (double) errorCount / processedCount : 0.0;
        }
        
        public double getThroughput() {
            return runningTimeMs > 0 ? (double) processedCount / (runningTimeMs / 1000.0) : 0.0;
        }
        
        public double getChainCreationRate() {
            return analyzedCount > 0 ? (double) chainCreatedCount / analyzedCount : 0.0;
        }
        
        public double getChainUpdateRate() {
            return analyzedCount > 0 ? (double) chainUpdatedCount / analyzedCount : 0.0;
        }
    }
}
