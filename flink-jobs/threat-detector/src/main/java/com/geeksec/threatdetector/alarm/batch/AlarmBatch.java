package com.geeksec.threatdetector.alarm.batch;

import com.geeksec.threatdetector.model.output.Alarm;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 告警批量处理模型
 * 用于缓存和批量处理告警数据
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class AlarmBatch implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 批次ID
     */
    private final String batchId;
    
    /**
     * 告警列表
     */
    private final ConcurrentLinkedQueue<Alarm> alarms;
    
    /**
     * 批次创建时间
     */
    private final LocalDateTime createTime;
    
    /**
     * 最后更新时间
     */
    private volatile LocalDateTime lastUpdateTime;
    
    /**
     * 告警数量
     */
    private final AtomicInteger alarmCount;
    
    /**
     * 批次大小（字节）
     */
    private final AtomicLong batchSize;
    
    /**
     * 批次状态
     */
    private volatile BatchStatus status;
    
    /**
     * 最大批次大小
     */
    private final int maxBatchSize;
    
    /**
     * 最大等待时间（毫秒）
     */
    private final long maxWaitTimeMs;
    
    /**
     * 批次状态枚举
     */
    public enum BatchStatus {
        /** 活跃状态，可以继续添加告警 */
        ACTIVE,
        /** 已满状态，不能再添加告警 */
        FULL,
        /** 超时状态，需要立即处理 */
        TIMEOUT,
        /** 处理中状态 */
        PROCESSING,
        /** 已完成状态 */
        COMPLETED,
        /** 失败状态 */
        FAILED
    }
    
    /**
     * 构造函数
     * 
     * @param batchId 批次ID
     * @param maxBatchSize 最大批次大小
     * @param maxWaitTimeMs 最大等待时间
     */
    public AlarmBatch(String batchId, int maxBatchSize, long maxWaitTimeMs) {
        this.batchId = batchId;
        this.maxBatchSize = maxBatchSize;
        this.maxWaitTimeMs = maxWaitTimeMs;
        this.alarms = new ConcurrentLinkedQueue<>();
        this.createTime = LocalDateTime.now();
        this.lastUpdateTime = this.createTime;
        this.alarmCount = new AtomicInteger(0);
        this.batchSize = new AtomicLong(0);
        this.status = BatchStatus.ACTIVE;
    }
    
    /**
     * 添加告警到批次
     * 
     * @param alarm 告警对象
     * @return 是否添加成功
     */
    public boolean addAlarm(Alarm alarm) {
        if (status != BatchStatus.ACTIVE) {
            log.warn("批次 {} 状态为 {}，无法添加告警", batchId, status);
            return false;
        }
        
        // 检查是否会超过最大批次大小
        if (alarmCount.get() >= maxBatchSize) {
            status = BatchStatus.FULL;
            log.info("批次 {} 已达到最大大小 {}，状态变更为 FULL", batchId, maxBatchSize);
            return false;
        }
        
        // 添加告警
        alarms.offer(alarm);
        alarmCount.incrementAndGet();
        batchSize.addAndGet(estimateAlarmSize(alarm));
        lastUpdateTime = LocalDateTime.now();
        
        // 检查是否达到批次大小限制
        if (alarmCount.get() >= maxBatchSize) {
            status = BatchStatus.FULL;
            log.info("批次 {} 添加告警后达到最大大小，状态变更为 FULL", batchId);
        }
        
        log.debug("告警已添加到批次 {}，当前数量: {}", batchId, alarmCount.get());
        return true;
    }
    
    /**
     * 检查批次是否应该被处理
     * 
     * @return 是否应该处理
     */
    public boolean shouldProcess() {
        if (status == BatchStatus.FULL) {
            return true;
        }
        
        if (status == BatchStatus.ACTIVE && isTimeout()) {
            status = BatchStatus.TIMEOUT;
            log.info("批次 {} 超时，状态变更为 TIMEOUT", batchId);
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查批次是否超时
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        if (alarmCount.get() == 0) {
            return false;
        }
        
        long elapsedTime = java.time.Duration.between(createTime, LocalDateTime.now()).toMillis();
        return elapsedTime >= maxWaitTimeMs;
    }
    
    /**
     * 获取批次中的所有告警
     * 
     * @return 告警列表
     */
    public List<Alarm> getAlarms() {
        return new ArrayList<>(alarms);
    }
    
    /**
     * 清空批次
     */
    public void clear() {
        alarms.clear();
        alarmCount.set(0);
        batchSize.set(0);
        status = BatchStatus.COMPLETED;
        log.info("批次 {} 已清空", batchId);
    }
    
    /**
     * 标记批次为处理中
     */
    public void markProcessing() {
        status = BatchStatus.PROCESSING;
        log.info("批次 {} 状态变更为 PROCESSING", batchId);
    }
    
    /**
     * 标记批次为已完成
     */
    public void markCompleted() {
        status = BatchStatus.COMPLETED;
        log.info("批次 {} 状态变更为 COMPLETED", batchId);
    }
    
    /**
     * 标记批次为失败
     */
    public void markFailed() {
        status = BatchStatus.FAILED;
        log.error("批次 {} 状态变更为 FAILED", batchId);
    }
    
    /**
     * 获取批次统计信息
     * 
     * @return 统计信息
     */
    public BatchStatistics getStatistics() {
        long elapsedTime = java.time.Duration.between(createTime, LocalDateTime.now()).toMillis();
        long waitTime = java.time.Duration.between(lastUpdateTime, LocalDateTime.now()).toMillis();
        
        return BatchStatistics.builder()
                .batchId(batchId)
                .alarmCount(alarmCount.get())
                .batchSizeBytes(batchSize.get())
                .elapsedTimeMs(elapsedTime)
                .waitTimeMs(waitTime)
                .status(status)
                .createTime(createTime)
                .lastUpdateTime(lastUpdateTime)
                .build();
    }
    
    /**
     * 估算告警对象的大小
     * 
     * @param alarm 告警对象
     * @return 估算大小（字节）
     */
    private long estimateAlarmSize(Alarm alarm) {
        // 简单估算，实际可以根据需要进行更精确的计算
        long size = 0;
        
        if (alarm.getAlarmName() != null) {
            size += alarm.getAlarmName().length() * 2; // UTF-8字符
        }
        if (alarm.getDescription() != null) {
            size += alarm.getDescription().length() * 2;
        }
        if (alarm.getSrcIp() != null) {
            size += alarm.getSrcIp().length() * 2;
        }
        if (alarm.getDstIp() != null) {
            size += alarm.getDstIp().length() * 2;
        }
        
        // 添加固定开销
        size += 200; // 对象头、基本字段等
        
        return size;
    }
    
    /**
     * 检查批次是否为空
     * 
     * @return 是否为空
     */
    public boolean isEmpty() {
        return alarmCount.get() == 0;
    }
    
    /**
     * 检查批次是否已满
     * 
     * @return 是否已满
     */
    public boolean isFull() {
        return alarmCount.get() >= maxBatchSize;
    }
    
    /**
     * 获取批次使用率
     * 
     * @return 使用率（0.0-1.0）
     */
    public double getUsageRatio() {
        return (double) alarmCount.get() / maxBatchSize;
    }
    

}
