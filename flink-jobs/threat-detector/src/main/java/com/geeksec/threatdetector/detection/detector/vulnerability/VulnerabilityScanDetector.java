package com.geeksec.threatdetector.detection.detector.vulnerability;

import com.geeksec.threatdetector.detection.DetectorType;
import com.geeksec.threatdetector.detection.ThreatDetector;
import com.geeksec.threatdetector.model.detection.DetectionResult;
import com.geeksec.threatdetector.model.input.HttpInfo;
import com.geeksec.threatdetector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 漏洞扫描检测器
 * 检测各种漏洞扫描工具，如X-Ray等
 *
 * <AUTHOR>
 */
@Slf4j
public class VulnerabilityScanDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // X-Ray扫描器特征
    private static final Set<String> XRAY_USER_AGENTS = new HashSet<>();
    private static final Set<String> XRAY_HEADERS = new HashSet<>();
    private static final Set<Pattern> XRAY_URL_PATTERNS = new HashSet<>();

    // 其他扫描器特征
    private static final Set<String> SCANNER_USER_AGENTS = new HashSet<>();
    private static final Set<String> SCANNER_HEADERS = new HashSet<>();

    // 扫描特征关键词
    private static final Set<String> SCAN_KEYWORDS = new HashSet<>();

    static {
        initializeXraySignatures();
        initializeScannerSignatures();
        initializeScanKeywords();
    }

    private static void initializeXraySignatures() {
        // X-Ray用户代理特征
        XRAY_USER_AGENTS.add("Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0)");
        XRAY_USER_AGENTS.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        // X-Ray请求头特征
        XRAY_HEADERS.add("X-Forwarded-For");
        XRAY_HEADERS.add("X-Real-IP");
        XRAY_HEADERS.add("X-Originating-IP");
        
        // X-Ray URL模式
        XRAY_URL_PATTERNS.add(Pattern.compile(".*\\.(php|jsp|asp|aspx)\\?.*=.*"));
        XRAY_URL_PATTERNS.add(Pattern.compile(".*/admin/.*"));
        XRAY_URL_PATTERNS.add(Pattern.compile(".*/test/.*"));
        
        log.info("初始化X-Ray扫描器特征库");
    }

    private static void initializeScannerSignatures() {
        // 常见扫描器用户代理
        SCANNER_USER_AGENTS.add("sqlmap");
        SCANNER_USER_AGENTS.add("Nmap");
        SCANNER_USER_AGENTS.add("Nikto");
        SCANNER_USER_AGENTS.add("dirb");
        SCANNER_USER_AGENTS.add("gobuster");
        SCANNER_USER_AGENTS.add("dirbuster");
        SCANNER_USER_AGENTS.add("wfuzz");
        SCANNER_USER_AGENTS.add("burp");
        SCANNER_USER_AGENTS.add("ZAP");
        SCANNER_USER_AGENTS.add("w3af");
        SCANNER_USER_AGENTS.add("acunetix");
        SCANNER_USER_AGENTS.add("nessus");
        SCANNER_USER_AGENTS.add("openvas");
        
        // 扫描器特征头
        SCANNER_HEADERS.add("X-Scanner");
        SCANNER_HEADERS.add("X-Forwarded-Host");
        SCANNER_HEADERS.add("X-Injection-Test");
        
        log.info("初始化扫描器特征库，共 {} 个用户代理", SCANNER_USER_AGENTS.size());
    }

    private static void initializeScanKeywords() {
        // 扫描相关关键词
        SCAN_KEYWORDS.add("union");
        SCAN_KEYWORDS.add("select");
        SCAN_KEYWORDS.add("script");
        SCAN_KEYWORDS.add("alert");
        SCAN_KEYWORDS.add("eval");
        SCAN_KEYWORDS.add("exec");
        SCAN_KEYWORDS.add("system");
        SCAN_KEYWORDS.add("cmd");
        SCAN_KEYWORDS.add("shell");
        SCAN_KEYWORDS.add("passwd");
        SCAN_KEYWORDS.add("etc/passwd");
        SCAN_KEYWORDS.add("../");
        SCAN_KEYWORDS.add("..\\");
        
        log.info("初始化扫描关键词库，共 {} 个关键词", SCAN_KEYWORDS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.VULNERABILITY_SCAN;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 只处理HTTP事件
            if (event.getEventType() != NetworkEvent.EventType.HTTP || event.getHttpInfo() == null) {
                return results;
            }

            HttpInfo httpInfo = event.getHttpInfo();

            // 检测X-Ray扫描器
            DetectionResult xrayResult = detectXrayScanner(event, httpInfo);
            if (xrayResult != null) {
                results.add(xrayResult);
            }

            // 检测其他扫描器
            DetectionResult scannerResult = detectGenericScanner(event, httpInfo);
            if (scannerResult != null) {
                results.add(scannerResult);
            }

            // 检测SQL注入扫描
            DetectionResult sqlResult = detectSqlInjectionScan(event, httpInfo);
            if (sqlResult != null) {
                results.add(sqlResult);
            }

            // 检测XSS扫描
            DetectionResult xssResult = detectXssScan(event, httpInfo);
            if (xssResult != null) {
                results.add(xssResult);
            }

            // 检测目录扫描
            DetectionResult dirResult = detectDirectoryScan(event, httpInfo);
            if (dirResult != null) {
                results.add(dirResult);
            }

        } catch (Exception e) {
            log.error("漏洞扫描检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测X-Ray扫描器
     */
    private DetectionResult detectXrayScanner(NetworkEvent event, HttpInfo httpInfo) {
        Map<String, String> requestHeaders = httpInfo.getRequestHeaders();
        if (requestHeaders == null) {
            return null;
        }

        String userAgent = requestHeaders.get("User-Agent");
        String url = httpInfo.getUrl();

        // 检查用户代理
        if (userAgent != null) {
            for (String xrayUA : XRAY_USER_AGENTS) {
                if (userAgent.contains(xrayUA)) {
                    return createDetectionResult(event, "XRAY_USER_AGENT", 
                            "X-Ray漏洞扫描器",
                            DetectionResult.ThreatLevel.HIGH, 0.9,
                            "检测到X-Ray扫描器用户代理: " + userAgent);
                }
            }
        }

        // 检查特殊请求头
        for (String header : XRAY_HEADERS) {
            if (requestHeaders.containsKey(header)) {
                return createDetectionResult(event, "XRAY_HEADER", 
                        "X-Ray漏洞扫描器",
                        DetectionResult.ThreatLevel.HIGH, 0.8,
                        "检测到X-Ray扫描器特征头: " + header);
            }
        }

        // 检查URL模式
        if (url != null) {
            for (Pattern pattern : XRAY_URL_PATTERNS) {
                if (pattern.matcher(url).matches()) {
                    return createDetectionResult(event, "XRAY_URL_PATTERN", 
                            "X-Ray漏洞扫描器",
                            DetectionResult.ThreatLevel.MEDIUM, 0.7,
                            "检测到X-Ray扫描器URL模式: " + url);
                }
            }
        }

        return null;
    }

    /**
     * 检测通用扫描器
     */
    private DetectionResult detectGenericScanner(NetworkEvent event, HttpInfo httpInfo) {
        Map<String, String> requestHeaders = httpInfo.getRequestHeaders();
        if (requestHeaders == null) {
            return null;
        }

        String userAgent = requestHeaders.get("User-Agent");
        if (userAgent == null) {
            return null;
        }

        String lowerUA = userAgent.toLowerCase();
        for (String scannerUA : SCANNER_USER_AGENTS) {
            if (lowerUA.contains(scannerUA.toLowerCase())) {
                return createDetectionResult(event, "SCANNER_USER_AGENT", 
                        "漏洞扫描工具",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        "检测到扫描器用户代理: " + scannerUA);
            }
        }

        return null;
    }

    /**
     * 检测SQL注入扫描
     */
    private DetectionResult detectSqlInjectionScan(NetworkEvent event, HttpInfo httpInfo) {
        String url = httpInfo.getUrl();
        String requestBody = httpInfo.getRequestBody();

        if (url != null && containsSqlInjectionPayload(url)) {
            return createDetectionResult(event, "SQL_INJECTION_SCAN", 
                    "SQL注入扫描",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    "检测到SQL注入扫描载荷");
        }

        if (requestBody != null && containsSqlInjectionPayload(requestBody)) {
            return createDetectionResult(event, "SQL_INJECTION_SCAN", 
                    "SQL注入扫描",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    "检测到SQL注入扫描载荷");
        }

        return null;
    }

    /**
     * 检测XSS扫描
     */
    private DetectionResult detectXssScan(NetworkEvent event, HttpInfo httpInfo) {
        String url = httpInfo.getUrl();
        String requestBody = httpInfo.getRequestBody();

        if (url != null && containsXssPayload(url)) {
            return createDetectionResult(event, "XSS_SCAN", 
                    "XSS跨站脚本扫描",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    "检测到XSS扫描载荷");
        }

        if (requestBody != null && containsXssPayload(requestBody)) {
            return createDetectionResult(event, "XSS_SCAN", 
                    "XSS跨站脚本扫描",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    "检测到XSS扫描载荷");
        }

        return null;
    }

    /**
     * 检测目录扫描
     */
    private DetectionResult detectDirectoryScan(NetworkEvent event, HttpInfo httpInfo) {
        String url = httpInfo.getUrl();
        if (url == null) {
            return null;
        }

        // 检查是否为常见的目录扫描路径
        String[] commonPaths = {
                "/admin", "/test", "/backup", "/config", "/upload", 
                "/temp", "/tmp", "/log", "/logs", "/debug"
        };

        for (String path : commonPaths) {
            if (url.contains(path)) {
                return createDetectionResult(event, "DIRECTORY_SCAN", 
                        "目录扫描",
                        DetectionResult.ThreatLevel.MEDIUM, 0.6,
                        "检测到目录扫描行为: " + path);
            }
        }

        return null;
    }

    /**
     * 检查是否包含SQL注入载荷
     */
    private boolean containsSqlInjectionPayload(String content) {
        String lowerContent = content.toLowerCase();
        return lowerContent.contains("union") && lowerContent.contains("select") ||
               lowerContent.contains("' or ") ||
               lowerContent.contains("\" or ") ||
               lowerContent.contains("1=1") ||
               lowerContent.contains("1' or '1'='1");
    }

    /**
     * 检查是否包含XSS载荷
     */
    private boolean containsXssPayload(String content) {
        String lowerContent = content.toLowerCase();
        return lowerContent.contains("<script") ||
               lowerContent.contains("javascript:") ||
               lowerContent.contains("alert(") ||
               lowerContent.contains("eval(") ||
               lowerContent.contains("onload=") ||
               lowerContent.contains("onerror=");
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("VULNERABILITY_SCAN")
                .assetLabel("SCAN_TARGET")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 30; // 中等优先级
    }
}
