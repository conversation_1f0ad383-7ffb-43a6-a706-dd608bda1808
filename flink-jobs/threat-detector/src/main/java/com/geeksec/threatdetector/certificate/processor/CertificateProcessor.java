package com.geeksec.threatdetector.certificate.processor;

import com.geeksec.threatdetector.certificate.model.Certificate;
import com.geeksec.threatdetector.certificate.model.CertificateAnalysisResult;

import java.io.Serializable;
import java.util.List;

/**
 * 证书处理器接口
 * 定义证书处理的标准接口
 * 
 * <AUTHOR>
 */
public interface CertificateProcessor extends Serializable {
    
    /**
     * 处理单个证书
     * 
     * @param certificateData 证书原始数据
     * @return 处理结果
     */
    CertificateProcessResult processCertificate(byte[] certificateData);
    
    /**
     * 处理证书对象
     * 
     * @param certificate 证书对象
     * @return 处理结果
     */
    CertificateProcessResult processCertificate(Certificate certificate);
    
    /**
     * 批量处理证书
     * 
     * @param certificates 证书列表
     * @return 处理结果列表
     */
    List<CertificateProcessResult> processCertificates(List<Certificate> certificates);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    String getProcessorName();
    
    /**
     * 获取处理器版本
     * 
     * @return 处理器版本
     */
    String getProcessorVersion();
    
    /**
     * 获取处理器描述
     * 
     * @return 处理器描述
     */
    String getDescription();
    
    /**
     * 检查处理器是否启用
     * 
     * @return 是否启用
     */
    boolean isEnabled();
    
    /**
     * 获取处理器优先级
     * 
     * @return 优先级（数值越小优先级越高）
     */
    int getPriority();
    
    /**
     * 获取处理器统计信息
     * 
     * @return 统计信息
     */
    CertificateProcessorStatistics getStatistics();
    
    /**
     * 重置处理器状态
     */
    void reset();
    
    /**
     * 证书处理结果
     */
    @lombok.Getter
    @lombok.AllArgsConstructor
    class CertificateProcessResult implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 是否处理成功
         */
        private final boolean success;
        
        /**
         * 处理后的证书对象
         */
        private final Certificate certificate;
        
        /**
         * 分析结果
         */
        private final CertificateAnalysisResult analysisResult;
        
        /**
         * 错误信息
         */
        private final String errorMessage;
        
        /**
         * 处理时间（毫秒）
         */
        private final long processingTimeMs;
        
        /**
         * 处理器名称
         */
        private final String processorName;
        
        /**
         * 创建成功结果
         */
        public static CertificateProcessResult success(Certificate certificate, 
                                                     CertificateAnalysisResult analysisResult,
                                                     long processingTimeMs, String processorName) {
            return new CertificateProcessResult(true, certificate, analysisResult, 
                    null, processingTimeMs, processorName);
        }
        
        /**
         * 创建失败结果
         */
        public static CertificateProcessResult failure(String errorMessage, 
                                                     long processingTimeMs, String processorName) {
            return new CertificateProcessResult(false, null, null, 
                    errorMessage, processingTimeMs, processorName);
        }
    }
    
    /**
     * 证书处理器统计信息
     */
    @lombok.Getter
    class CertificateProcessorStatistics implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 处理器名称
         */
        private final String processorName;
        
        /**
         * 总处理数量
         */
        private long totalProcessed;
        
        /**
         * 成功处理数量
         */
        private long successfullyProcessed;
        
        /**
         * 失败处理数量
         */
        private long failedProcessed;
        
        /**
         * 平均处理时间（毫秒）
         */
        private double averageProcessingTimeMs;
        
        /**
         * 最大处理时间（毫秒）
         */
        private long maxProcessingTimeMs;
        
        /**
         * 最小处理时间（毫秒）
         */
        private long minProcessingTimeMs;
        
        /**
         * 最后处理时间
         */
        private java.time.LocalDateTime lastProcessTime;
        
        /**
         * 统计开始时间
         */
        private java.time.LocalDateTime startTime;
        
        public CertificateProcessorStatistics(String processorName) {
            this.processorName = processorName;
            this.startTime = java.time.LocalDateTime.now();
            this.totalProcessed = 0;
            this.successfullyProcessed = 0;
            this.failedProcessed = 0;
            this.averageProcessingTimeMs = 0.0;
            this.maxProcessingTimeMs = 0;
            this.minProcessingTimeMs = Long.MAX_VALUE;
        }
        
        /**
         * 更新统计信息
         */
        public void updateStatistics(boolean success, long processingTimeMs) {
            totalProcessed++;
            
            if (success) {
                successfullyProcessed++;
            } else {
                failedProcessed++;
            }
            
            // 更新处理时间统计
            if (totalProcessed == 1) {
                averageProcessingTimeMs = processingTimeMs;
                maxProcessingTimeMs = processingTimeMs;
                minProcessingTimeMs = processingTimeMs;
            } else {
                averageProcessingTimeMs = (averageProcessingTimeMs * (totalProcessed - 1) + processingTimeMs) / totalProcessed;
                maxProcessingTimeMs = Math.max(maxProcessingTimeMs, processingTimeMs);
                minProcessingTimeMs = Math.min(minProcessingTimeMs, processingTimeMs);
            }
            
            lastProcessTime = java.time.LocalDateTime.now();
        }
        
        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            if (totalProcessed == 0) {
                return 0.0;
            }
            return (double) successfullyProcessed / totalProcessed;
        }
        
        /**
         * 获取吞吐量（每秒处理数）
         */
        public double getThroughput() {
            if (startTime == null || lastProcessTime == null) {
                return 0.0;
            }
            
            long durationSeconds = java.time.Duration.between(startTime, lastProcessTime).getSeconds();
            if (durationSeconds <= 0) {
                return 0.0;
            }
            
            return (double) totalProcessed / durationSeconds;
        }
        
        /**
         * 重置统计信息
         */
        public void reset() {
            totalProcessed = 0;
            successfullyProcessed = 0;
            failedProcessed = 0;
            averageProcessingTimeMs = 0.0;
            maxProcessingTimeMs = 0;
            minProcessingTimeMs = Long.MAX_VALUE;
            startTime = java.time.LocalDateTime.now();
            lastProcessTime = null;
        }
        
        /**
         * 获取格式化的统计信息
         */
        public String getFormattedStatistics() {
            return String.format(
                    "Processor[%s]: processed=%d, success=%.1f%%, avg_time=%.1fms, throughput=%.1f/s",
                    processorName,
                    totalProcessed,
                    getSuccessRate() * 100,
                    averageProcessingTimeMs,
                    getThroughput()
            );
        }
        

    }
}
