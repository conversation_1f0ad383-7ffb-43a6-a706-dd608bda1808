package com.geeksec.threatdetector.job.pipeline;

import com.geeksec.threatdetector.control.DetectorSwitchBroadcastFunction;
import com.geeksec.threatdetector.control.DetectorSwitchManager;
import com.geeksec.threatdetector.detection.DetectionEngine;
import com.geeksec.threatdetector.detection.detector.bruteforce.BruteForceDetector;
import com.geeksec.threatdetector.detection.detector.c2.C2FrameworkDetector;
import com.geeksec.threatdetector.detection.detector.geo.GeoAnomalyDetector;
import com.geeksec.threatdetector.detection.detector.mining.MiningDetector;
import com.geeksec.threatdetector.detection.detector.portscan.PortScanDetector;
import com.geeksec.threatdetector.detection.detector.remote.RemoteControlProtocolDetector;
import com.geeksec.threatdetector.detection.detector.tunnel.TunnelDetector;
import com.geeksec.threatdetector.detection.detector.vulnerability.VulnerabilityScanDetector;
import com.geeksec.threatdetector.detection.detector.webshell.WebShellDetector;
import com.geeksec.threatdetector.model.detection.DetectionResult;
import com.geeksec.threatdetector.model.input.NetworkEvent;
import com.geeksec.threatdetector.model.output.Alarm;
import com.geeksec.threatdetector.model.output.AssetLabel;
import com.geeksec.threatdetector.model.output.SessionLabel;
import com.geeksec.threatdetector.output.OutputRouter;
import com.geeksec.threatdetector.source.NetworkEventSourceConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;

import java.util.Map;

/**
 * 威胁检测流水线
 * 统一的数据处理流水线，清晰地表达业务流程：
 * 输入数据 -> 威胁检测 -> 输出路由 -> 四种输出
 *
 * <AUTHOR>
 */
@Slf4j
public class ThreatDetectionPipeline {

        /**
         * 构建威胁检测流水线
         *
         * @param sessionStream  会话元数据流
         * @param protocolStream 协议元数据流
         * @param config         配置参数
         * @return 处理后的数据流
         */
        public static PipelineResult build(
                        DataStream<Map<String, Object>> sessionStream,
                        DataStream<Map<String, Object>> protocolStream,
                        ParameterTool config) {

                return build(sessionStream, protocolStream, null, config);
        }

        /**
         * 构建威胁检测流水线（支持检测器开关控制）
         *
         * @param sessionStream        会话元数据流
         * @param protocolStream       协议元数据流
         * @param detectorSwitchStream 检测器开关配置流（可选）
         * @param config               配置参数
         * @return 处理后的数据流
         */
        public static PipelineResult build(
                        DataStream<Map<String, Object>> sessionStream,
                        DataStream<Map<String, Object>> protocolStream,
                        DataStream<Map<Integer, Integer>> detectorSwitchStream,
                        ParameterTool config) {

                log.info("开始构建威胁检测流水线（支持检测器开关控制）");

                // 1. 数据源处理：将原始数据转换为统一的网络事件格式
                DataStream<NetworkEvent> networkEventStream = buildDataSource(sessionStream, protocolStream);

                // 2. 威胁检测：使用检测引擎进行威胁检测（集成检测器开关控制）
                SingleOutputStreamOperator<DetectionResult> detectionResultStream = buildDetectionEngineWithSwitchControl(
                                networkEventStream, detectorSwitchStream, config);

                // 3. 输出路由：根据检测结果生成四种输出
                PipelineResult result = buildOutputRouter(detectionResultStream, config);

                log.info("威胁检测流水线构建完成");
                return result;
        }

        /**
         * 构建数据源处理阶段
         *
         * @param sessionStream  会话元数据流
         * @param protocolStream 协议元数据流
         * @return 统一的网络事件流
         */
        private static DataStream<NetworkEvent> buildDataSource(
                        DataStream<Map<String, Object>> sessionStream,
                        DataStream<Map<String, Object>> protocolStream) {

                log.info("构建数据源处理阶段");

                // 将会话数据转换为网络事件
                DataStream<NetworkEvent> sessionEventStream = sessionStream
                                .flatMap(new NetworkEventSourceConverter(NetworkEvent.EventType.SESSION));

                // 将协议数据转换为网络事件
                DataStream<NetworkEvent> protocolEventStream = protocolStream
                                .flatMap(new NetworkEventSourceConverter(NetworkEvent.EventType.OTHER));

                // 合并两个数据流
                return sessionEventStream
                                .union(protocolEventStream);
        }

        /**
         * 构建威胁检测阶段（支持检测器开关控制）
         *
         * @param networkEventStream   网络事件流
         * @param detectorSwitchStream 检测器开关配置流（可选）
         * @param config               配置参数
         * @return 检测结果流
         */
        private static SingleOutputStreamOperator<DetectionResult> buildDetectionEngineWithSwitchControl(
                        DataStream<NetworkEvent> networkEventStream,
                        DataStream<Map<Integer, Integer>> detectorSwitchStream,
                        ParameterTool config) {

                log.info("构建威胁检测阶段（支持检测器开关控制）");

                // 如果没有检测器开关流，使用传统方式
                if (detectorSwitchStream == null) {
                        return buildDetectionEngine(networkEventStream, config);
                }

                // 创建检测器开关广播流
                BroadcastStream<Map<Integer, Integer>> switchBroadcastStream = detectorSwitchStream
                                .broadcast(DetectorSwitchManager.DETECTOR_SWITCH_STATE_DESCRIPTOR);

                // 使用检测器开关广播函数处理网络事件
                SingleOutputStreamOperator<NetworkEvent> filteredEventStream = networkEventStream
                                .connect(switchBroadcastStream)
                                .process(new DetectorSwitchBroadcastFunction());

                // 创建检测引擎
                DetectionEngine detectionEngine = new DetectionEngine();

                // 注册检测器
                registerDetectors(detectionEngine, config);

                // 应用检测引擎到过滤后的事件流
                return filteredEventStream
                                .flatMap(detectionEngine);
        }

        /**
         * 构建威胁检测阶段（传统方式）
         *
         * @param networkEventStream 网络事件流
         * @param config             配置参数
         * @return 检测结果流
         */
        private static SingleOutputStreamOperator<DetectionResult> buildDetectionEngine(
                        DataStream<NetworkEvent> networkEventStream,
                        ParameterTool config) {

                log.info("构建威胁检测阶段（传统方式）");

                // 创建检测引擎
                DetectionEngine detectionEngine = new DetectionEngine();

                // 注册检测器（这里可以根据配置动态注册）
                registerDetectors(detectionEngine, config);

                // 应用检测引擎
                return networkEventStream
                                .flatMap(detectionEngine);
        }

        /**
         * 构建输出路由阶段
         *
         * @param detectionResultStream 检测结果流
         * @param config                配置参数
         * @return 流水线结果
         */
        private static PipelineResult buildOutputRouter(
                        SingleOutputStreamOperator<DetectionResult> detectionResultStream,
                        ParameterTool config) {

                log.info("构建输出路由阶段");

                // 应用输出路由器
                SingleOutputStreamOperator<DetectionResult> routedStream = detectionResultStream
                                .flatMap(new OutputRouter());

                // 获取四种侧输出流
                DataStream<SessionLabel> sessionLabelStream = routedStream
                                .getSideOutput(OutputRouter.SESSION_LABEL_TAG);

                DataStream<AssetLabel> assetLabelStream = routedStream
                                .getSideOutput(OutputRouter.ASSET_LABEL_TAG);

                DataStream<Alarm> alarmStream = routedStream
                                .getSideOutput(OutputRouter.ALARM_TAG);

                DataStream<String> notificationStream = routedStream
                                .getSideOutput(OutputRouter.NOTIFICATION_TAG);

                return PipelineResult.builder()
                                .mainStream(routedStream)
                                .sessionLabelStream(sessionLabelStream)
                                .assetLabelStream(assetLabelStream)
                                .alarmStream(alarmStream)
                                .notificationStream(notificationStream)
                                .build();
        }

        /**
         * 注册威胁检测器
         *
         * @param detectionEngine 检测引擎
         * @param config          配置参数
         */
        private static void registerDetectors(DetectionEngine detectionEngine, ParameterTool config) {
                log.info("注册威胁检测器");

                // 注册C2框架检测器
                detectionEngine.registerDetector(new C2FrameworkDetector());

                // 注册WebShell检测器
                detectionEngine.registerDetector(new WebShellDetector());

                // 注册隧道检测器
                detectionEngine.registerDetector(new TunnelDetector());

                // 注册暴力破解检测器
                detectionEngine.registerDetector(new BruteForceDetector());

                // 注册端口扫描检测器
                detectionEngine.registerDetector(new PortScanDetector());

                // 注册地理位置异常检测器
                detectionEngine.registerDetector(new GeoAnomalyDetector());

                // 注册挖矿检测器
                detectionEngine.registerDetector(new MiningDetector());

                // 注册漏洞扫描检测器
                detectionEngine.registerDetector(new VulnerabilityScanDetector());

                // 注册远程控制协议检测器
                detectionEngine.registerDetector(new RemoteControlProtocolDetector());

                log.info("威胁检测器注册完成，共注册 {} 个检测器", detectionEngine.getTotalDetectorCount());
        }

        /**
         * 流水线结果封装类
         */
        @lombok.Data
        @lombok.Builder
        @lombok.NoArgsConstructor
        @lombok.AllArgsConstructor
        public static class PipelineResult {
                /**
                 * 主数据流
                 */
                private SingleOutputStreamOperator<DetectionResult> mainStream;

                /**
                 * 会话标签流（输出到Doris会话表）
                 */
                private DataStream<SessionLabel> sessionLabelStream;

                /**
                 * 资产标签流（输出到Nebula图数据库）
                 */
                private DataStream<AssetLabel> assetLabelStream;

                /**
                 * 告警事件流（输出到Doris告警表）
                 */
                private DataStream<Alarm> alarmStream;

                /**
                 * 通知消息流（输出到Kafka）
                 */
                private DataStream<String> notificationStream;
        }
}
