package com.geeksec.threatdetector.certificate.analyzer;

import com.geeksec.threatdetector.certificate.model.Certificate;
import com.geeksec.threatdetector.certificate.model.CertificateAnalysisResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 综合证书安全分析器
 * 对证书进行全面的安全性分析和风险评估，返回详细的分析结果对象
 *
 * <AUTHOR>
 */
@Slf4j
public class ComprehensiveCertificateSecurityAnalyzer {
    
    private static final String ANALYZER_VERSION = "1.0.0";
    
    // 弱密钥长度阈值
    private static final int MIN_RSA_KEY_LENGTH = 2048;
    private static final int MIN_EC_KEY_LENGTH = 256;
    
    // 不安全的签名算法
    private static final List<String> INSECURE_ALGORITHMS = Arrays.asList(
            "MD5withRSA", "SHA1withRSA", "MD2withRSA", "MD4withRSA"
    );
    
    // 过期警告天数
    private static final int EXPIRY_WARNING_DAYS = 30;
    
    /**
     * 分析证书安全性
     * 
     * @param certificate 证书对象
     * @return 分析结果
     */
    public CertificateAnalysisResult analyzeCertificate(Certificate certificate) {
        if (certificate == null) {
            throw new IllegalArgumentException("证书对象不能为空");
        }
        
        log.debug("开始分析证书: {}", certificate.getCertId());
        
        CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder = 
                CertificateAnalysisResult.builder()
                        .certId(certificate.getCertId())
                        .analysisTime(LocalDateTime.now())
                        .analyzerVersion(ANALYZER_VERSION)
                        .securityChecks(new ArrayList<>())
                        .issues(new ArrayList<>())
                        .recommendations(new ArrayList<>());
        
        // 执行各项安全检查
        performKeyStrengthCheck(certificate, resultBuilder);
        performAlgorithmSecurityCheck(certificate, resultBuilder);
        performValidityPeriodCheck(certificate, resultBuilder);
        performCertificateChainCheck(certificate, resultBuilder);
        performExtensionsCheck(certificate, resultBuilder);
        performAnomalyDetection(certificate, resultBuilder);
        
        // 计算总体风险评分
        CertificateAnalysisResult result = resultBuilder.build();
        calculateOverallRisk(result);
        
        // 生成建议
        generateRecommendations(result);
        
        log.debug("证书分析完成: {}, 风险等级: {}, 评分: {}", 
                certificate.getCertId(), result.getOverallRiskLevel(), result.getOverallRiskScore());
        
        return result;
    }
    
    /**
     * 密钥强度检查
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performKeyStrengthCheck(Certificate certificate, 
                                       CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("密钥强度检查")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.KEY_STRENGTH)
                        .checkTime(LocalDateTime.now());
        
        String algorithm = certificate.getPublicKeyAlgorithm();
        Integer keyLength = certificate.getKeyLength();
        
        if (algorithm == null || keyLength == null) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.SKIP)
                    .description("无法获取密钥信息")
                    .riskScore(0.0);
        } else if ("RSA".equals(algorithm)) {
            if (keyLength < MIN_RSA_KEY_LENGTH) {
                checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.FAIL)
                        .description("RSA密钥长度过短")
                        .details(String.format("当前密钥长度: %d位, 建议最小长度: %d位", 
                                keyLength, MIN_RSA_KEY_LENGTH))
                        .riskScore(80.0);
                
                // 添加问题
                resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                        CertificateAnalysisResult.CertificateIssue.builder()
                                .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.WEAK_KEY)
                                .severity(CertificateAnalysisResult.CertificateIssue.Severity.HIGH)
                                .title("RSA密钥长度不足")
                                .description(String.format("RSA密钥长度仅为%d位，低于安全标准", keyLength))
                                .impact("密钥可能被暴力破解，存在安全风险")
                                .remediation("使用至少2048位的RSA密钥")
                                .relatedField("publicKey")
                                .discoveredTime(LocalDateTime.now())
                                .build()));
            } else {
                checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                        .description("RSA密钥长度符合安全要求")
                        .details(String.format("密钥长度: %d位", keyLength))
                        .riskScore(0.0);
            }
        } else if ("EC".equals(algorithm)) {
            if (keyLength < MIN_EC_KEY_LENGTH) {
                checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.FAIL)
                        .description("EC密钥长度过短")
                        .details(String.format("当前密钥长度: %d位, 建议最小长度: %d位", 
                                keyLength, MIN_EC_KEY_LENGTH))
                        .riskScore(70.0);
                
                // 添加问题
                resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                        CertificateAnalysisResult.CertificateIssue.builder()
                                .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.WEAK_KEY)
                                .severity(CertificateAnalysisResult.CertificateIssue.Severity.HIGH)
                                .title("EC密钥长度不足")
                                .description(String.format("EC密钥长度仅为%d位，低于安全标准", keyLength))
                                .impact("密钥强度不足，存在安全风险")
                                .remediation("使用至少256位的EC密钥")
                                .relatedField("publicKey")
                                .discoveredTime(LocalDateTime.now())
                                .build()));
            } else {
                checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                        .description("EC密钥长度符合安全要求")
                        .details(String.format("密钥长度: %d位", keyLength))
                        .riskScore(0.0);
            }
        } else {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.WARNING)
                    .description("未知的密钥算法")
                    .details(String.format("算法: %s", algorithm))
                    .riskScore(30.0);
        }
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 算法安全性检查
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performAlgorithmSecurityCheck(Certificate certificate, 
                                             CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("签名算法安全性检查")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.ALGORITHM_SECURITY)
                        .checkTime(LocalDateTime.now());
        
        String signatureAlgorithm = certificate.getSignatureAlgorithm();
        
        if (signatureAlgorithm == null) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.SKIP)
                    .description("无法获取签名算法信息")
                    .riskScore(0.0);
        } else if (INSECURE_ALGORITHMS.contains(signatureAlgorithm)) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.FAIL)
                    .description("使用了不安全的签名算法")
                    .details(String.format("签名算法: %s", signatureAlgorithm))
                    .riskScore(90.0);
            
            // 添加问题
            resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                    CertificateAnalysisResult.CertificateIssue.builder()
                            .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.INSECURE_ALGORITHM)
                            .severity(CertificateAnalysisResult.CertificateIssue.Severity.CRITICAL)
                            .title("不安全的签名算法")
                            .description(String.format("证书使用了不安全的签名算法: %s", signatureAlgorithm))
                            .impact("签名可能被伪造，证书不可信")
                            .remediation("使用SHA-256或更强的签名算法")
                            .relatedField("signatureAlgorithm")
                            .discoveredTime(LocalDateTime.now())
                            .build()));
        } else {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                    .description("签名算法安全")
                    .details(String.format("签名算法: %s", signatureAlgorithm))
                    .riskScore(0.0);
        }
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 有效期检查
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performValidityPeriodCheck(Certificate certificate, 
                                          CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("证书有效期检查")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.VALIDITY_PERIOD)
                        .checkTime(LocalDateTime.now());
        
        LocalDateTime notAfter = certificate.getNotAfter();
        LocalDateTime now = LocalDateTime.now();
        
        if (notAfter == null) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.SKIP)
                    .description("无法获取证书有效期信息")
                    .riskScore(0.0);
        } else if (now.isAfter(notAfter)) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.FAIL)
                    .description("证书已过期")
                    .details(String.format("过期时间: %s", notAfter))
                    .riskScore(100.0);
            
            // 添加问题
            resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                    CertificateAnalysisResult.CertificateIssue.builder()
                            .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.EXPIRED_CERTIFICATE)
                            .severity(CertificateAnalysisResult.CertificateIssue.Severity.CRITICAL)
                            .title("证书已过期")
                            .description(String.format("证书于%s过期", notAfter))
                            .impact("证书不再有效，可能导致连接失败")
                            .remediation("更新证书")
                            .relatedField("notAfter")
                            .discoveredTime(LocalDateTime.now())
                            .build()));
        } else if (now.plusDays(EXPIRY_WARNING_DAYS).isAfter(notAfter)) {
            long remainingDays = java.time.Duration.between(now, notAfter).toDays();
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.WARNING)
                    .description("证书即将过期")
                    .details(String.format("剩余有效期: %d天", remainingDays))
                    .riskScore(50.0);
            
            // 添加问题
            resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                    CertificateAnalysisResult.CertificateIssue.builder()
                            .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.EXPIRING_SOON)
                            .severity(CertificateAnalysisResult.CertificateIssue.Severity.MEDIUM)
                            .title("证书即将过期")
                            .description(String.format("证书将在%d天后过期", remainingDays))
                            .impact("证书即将失效")
                            .remediation("及时更新证书")
                            .relatedField("notAfter")
                            .discoveredTime(LocalDateTime.now())
                            .build()));
        } else {
            long remainingDays = java.time.Duration.between(now, notAfter).toDays();
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                    .description("证书有效期正常")
                    .details(String.format("剩余有效期: %d天", remainingDays))
                    .riskScore(0.0);
        }
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 证书链检查
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performCertificateChainCheck(Certificate certificate, 
                                            CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("证书链检查")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.CERTIFICATE_CHAIN)
                        .checkTime(LocalDateTime.now());
        
        Boolean isSelfSigned = certificate.getIsSelfSigned();
        
        if (isSelfSigned == null) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.SKIP)
                    .description("无法确定证书签名状态")
                    .riskScore(0.0);
        } else if (isSelfSigned) {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.WARNING)
                    .description("自签名证书")
                    .details("证书由自己签名，未经第三方CA验证")
                    .riskScore(60.0);
            
            // 添加问题
            resultBuilder.issues(addIssue(resultBuilder.build().getIssues(),
                    CertificateAnalysisResult.CertificateIssue.builder()
                            .issueType(CertificateAnalysisResult.CertificateIssue.IssueType.SELF_SIGNED)
                            .severity(CertificateAnalysisResult.CertificateIssue.Severity.MEDIUM)
                            .title("自签名证书")
                            .description("证书为自签名，未经权威CA验证")
                            .impact("可能存在信任问题")
                            .remediation("使用权威CA签发的证书")
                            .relatedField("issuer")
                            .discoveredTime(LocalDateTime.now())
                            .build()));
        } else {
            checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                    .description("证书由CA签发")
                    .details("证书经过第三方CA验证")
                    .riskScore(0.0);
        }
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 扩展字段检查
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performExtensionsCheck(Certificate certificate, 
                                      CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("扩展字段检查")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.EXTENSIONS)
                        .checkTime(LocalDateTime.now());
        
        // 简单的扩展字段检查
        checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                .description("扩展字段检查通过")
                .details("未发现异常扩展字段")
                .riskScore(0.0);
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 异常检测
     * 
     * @param certificate 证书
     * @param resultBuilder 结果构建器
     */
    private void performAnomalyDetection(Certificate certificate, 
                                       CertificateAnalysisResult.CertificateAnalysisResultBuilder resultBuilder) {
        
        CertificateAnalysisResult.SecurityCheck.SecurityCheckBuilder checkBuilder = 
                CertificateAnalysisResult.SecurityCheck.builder()
                        .checkName("异常检测")
                        .checkType(CertificateAnalysisResult.SecurityCheck.CheckType.ANOMALY_DETECTION)
                        .checkTime(LocalDateTime.now());
        
        // 简单的异常检测
        checkBuilder.result(CertificateAnalysisResult.SecurityCheck.CheckResult.PASS)
                .description("未发现异常")
                .details("证书结构正常")
                .riskScore(0.0);
        
        resultBuilder.securityChecks(addSecurityCheck(resultBuilder.build().getSecurityChecks(), 
                checkBuilder.build()));
    }
    
    /**
     * 计算总体风险评分
     * 
     * @param result 分析结果
     */
    private void calculateOverallRisk(CertificateAnalysisResult result) {
        if (result.getSecurityChecks() == null || result.getSecurityChecks().isEmpty()) {
            result.setOverallRiskScore(0.0);
            result.setOverallRiskLevel(CertificateAnalysisResult.RiskLevel.LOW);
            return;
        }
        
        // 计算平均风险评分
        double totalRisk = result.getSecurityChecks().stream()
                .mapToDouble(check -> check.getRiskScore() != null ? check.getRiskScore() : 0.0)
                .average()
                .orElse(0.0);
        
        result.setOverallRiskScore(totalRisk);
        
        // 确定风险等级
        if (totalRisk >= 80.0) {
            result.setOverallRiskLevel(CertificateAnalysisResult.RiskLevel.CRITICAL);
        } else if (totalRisk >= 60.0) {
            result.setOverallRiskLevel(CertificateAnalysisResult.RiskLevel.HIGH);
        } else if (totalRisk >= 30.0) {
            result.setOverallRiskLevel(CertificateAnalysisResult.RiskLevel.MEDIUM);
        } else {
            result.setOverallRiskLevel(CertificateAnalysisResult.RiskLevel.LOW);
        }
        
        // 计算信任评分（与风险评分相反）
        result.setTrustScore(100.0 - totalRisk);
    }
    
    /**
     * 生成建议
     * 
     * @param result 分析结果
     */
    private void generateRecommendations(CertificateAnalysisResult result) {
        if (result.getIssues() == null) {
            return;
        }
        
        List<String> recommendations = new ArrayList<>();
        
        for (CertificateAnalysisResult.CertificateIssue issue : result.getIssues()) {
            if (issue.getRemediation() != null && !recommendations.contains(issue.getRemediation())) {
                recommendations.add(issue.getRemediation());
            }
        }
        
        result.setRecommendations(recommendations);
    }
    
    /**
     * 添加安全检查结果
     * 
     * @param checks 现有检查列表
     * @param newCheck 新检查结果
     * @return 更新后的检查列表
     */
    private List<CertificateAnalysisResult.SecurityCheck> addSecurityCheck(
            List<CertificateAnalysisResult.SecurityCheck> checks, 
            CertificateAnalysisResult.SecurityCheck newCheck) {
        if (checks == null) {
            checks = new ArrayList<>();
        }
        checks.add(newCheck);
        return checks;
    }
    
    /**
     * 添加问题
     * 
     * @param issues 现有问题列表
     * @param newIssue 新问题
     * @return 更新后的问题列表
     */
    private List<CertificateAnalysisResult.CertificateIssue> addIssue(
            List<CertificateAnalysisResult.CertificateIssue> issues, 
            CertificateAnalysisResult.CertificateIssue newIssue) {
        if (issues == null) {
            issues = new ArrayList<>();
        }
        issues.add(newIssue);
        return issues;
    }
}
