package com.geeksec.threatdetector.formatting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 格式化上下文模型
 * 提供格式化过程中需要的上下文信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormattingContext implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 目标受众
     */
    private TargetAudience targetAudience;
    
    /**
     * 输出渠道
     */
    private OutputChannel outputChannel;
    
    /**
     * 语言设置
     */
    private String language;
    
    /**
     * 格式化策略
     */
    private AlarmFormattedContent.FormattingStrategy strategy;
    
    /**
     * 用户偏好
     */
    private UserPreferences userPreferences;
    
    /**
     * 环境信息
     */
    private EnvironmentInfo environmentInfo;
    
    /**
     * 历史上下文
     */
    private HistoricalContext historicalContext;
    
    /**
     * 业务上下文
     */
    private BusinessContext businessContext;
    
    /**
     * 技术上下文
     */
    private TechnicalContext technicalContext;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;
    
    /**
     * 目标受众枚举
     */
    @lombok.Getter
    public enum TargetAudience {
        /** 安全分析师 */
        SECURITY_ANALYST("security_analyst", "安全分析师"),
        /** 系统管理员 */
        SYSTEM_ADMIN("system_admin", "系统管理员"),
        /** 网络管理员 */
        NETWORK_ADMIN("network_admin", "网络管理员"),
        /** 安全管理员 */
        SECURITY_MANAGER("security_manager", "安全管理员"),
        /** 业务用户 */
        BUSINESS_USER("business_user", "业务用户"),
        /** 高级管理层 */
        EXECUTIVE("executive", "高级管理层"),
        /** 技术支持 */
        TECHNICAL_SUPPORT("technical_support", "技术支持"),
        /** 合规人员 */
        COMPLIANCE_OFFICER("compliance_officer", "合规人员");
        
        private final String code;
        private final String description;
        
        TargetAudience(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 输出渠道枚举
     */
    @lombok.Getter
    public enum OutputChannel {
        /** 邮件 */
        EMAIL("email", "邮件"),
        /** 短信 */
        SMS("sms", "短信"),
        /** 即时消息 */
        INSTANT_MESSAGE("instant_message", "即时消息"),
        /** 控制台 */
        CONSOLE("console", "控制台"),
        /** 报告 */
        REPORT("report", "报告"),
        /** 仪表板 */
        DASHBOARD("dashboard", "仪表板"),
        /** API */
        API("api", "API"),
        /** 日志 */
        LOG("log", "日志");
        
        private final String code;
        private final String description;
        
        OutputChannel(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 用户偏好
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPreferences implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 详细程度偏好
         */
        private DetailLevel detailLevel;
        
        /**
         * 技术术语偏好
         */
        private TechnicalTermLevel technicalTermLevel;
        
        /**
         * 包含建议
         */
        private Boolean includeSuggestions;
        
        /**
         * 包含原理说明
         */
        private Boolean includePrinciple;
        
        /**
         * 包含影响分析
         */
        private Boolean includeImpactAnalysis;
        
        /**
         * 包含相关信息
         */
        private Boolean includeRelatedInfo;
        
        /**
         * 最大长度限制
         */
        private Integer maxLength;
        
        /**
         * 自定义字段
         */
        private List<String> customFields;
    }
    
    /**
     * 环境信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnvironmentInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 组织名称
         */
        private String organizationName;
        
        /**
         * 部门名称
         */
        private String departmentName;
        
        /**
         * 环境类型
         */
        private EnvironmentType environmentType;
        
        /**
         * 时区
         */
        private String timezone;
        
        /**
         * 地区
         */
        private String region;
        
        /**
         * 合规要求
         */
        private List<String> complianceRequirements;
        
        /**
         * 安全策略
         */
        private Map<String, String> securityPolicies;
    }
    
    /**
     * 历史上下文
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalContext implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 相似告警历史
         */
        private List<String> similarAlarmHistory;
        
        /**
         * 处理历史
         */
        private List<HandlingHistory> handlingHistory;
        
        /**
         * 趋势信息
         */
        private TrendInfo trendInfo;
        
        /**
         * 频率信息
         */
        private FrequencyInfo frequencyInfo;
    }
    
    /**
     * 业务上下文
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessContext implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 业务重要性
         */
        private BusinessImportance businessImportance;
        
        /**
         * 影响的业务系统
         */
        private List<String> affectedBusinessSystems;
        
        /**
         * 业务时间窗口
         */
        private BusinessTimeWindow businessTimeWindow;
        
        /**
         * SLA要求
         */
        private Map<String, String> slaRequirements;
        
        /**
         * 业务联系人
         */
        private List<String> businessContacts;
    }
    
    /**
     * 技术上下文
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalContext implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 网络拓扑信息
         */
        private String networkTopology;
        
        /**
         * 系统架构信息
         */
        private String systemArchitecture;
        
        /**
         * 安全工具信息
         */
        private List<String> securityTools;
        
        /**
         * 技术栈信息
         */
        private List<String> technologyStack;
        
        /**
         * 配置信息
         */
        private Map<String, String> configurations;
    }
    
    /**
     * 处理历史
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HandlingHistory implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 处理时间
         */
        private LocalDateTime handlingTime;
        
        /**
         * 处理动作
         */
        private String action;
        
        /**
         * 处理结果
         */
        private String result;
        
        /**
         * 处理人员
         */
        private String handler;
        
        /**
         * 有效性评估
         */
        private String effectiveness;
    }
    
    /**
     * 趋势信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 趋势方向
         */
        private TrendDirection direction;
        
        /**
         * 变化幅度
         */
        private Double changeRate;
        
        /**
         * 时间范围
         */
        private String timeRange;
        
        /**
         * 趋势描述
         */
        private String description;
    }
    
    /**
     * 频率信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FrequencyInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 发生频率
         */
        private String frequency;
        
        /**
         * 时间模式
         */
        private String timePattern;
        
        /**
         * 峰值时间
         */
        private String peakTime;
        
        /**
         * 频率变化
         */
        private String frequencyChange;
    }
    
    /**
     * 详细程度枚举
     */
    @lombok.Getter
    public enum DetailLevel {
        MINIMAL("minimal", "最简"),
        BASIC("basic", "基础"),
        STANDARD("standard", "标准"),
        DETAILED("detailed", "详细"),
        COMPREHENSIVE("comprehensive", "全面");
        
        private final String code;
        private final String description;
        
        DetailLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 技术术语级别枚举
     */
    @lombok.Getter
    public enum TechnicalTermLevel {
        NONE("none", "无技术术语"),
        BASIC("basic", "基础术语"),
        INTERMEDIATE("intermediate", "中级术语"),
        ADVANCED("advanced", "高级术语"),
        EXPERT("expert", "专家术语");
        
        private final String code;
        private final String description;
        
        TechnicalTermLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 环境类型枚举
     */
    @lombok.Getter
    public enum EnvironmentType {
        PRODUCTION("production", "生产环境"),
        STAGING("staging", "预发布环境"),
        TESTING("testing", "测试环境"),
        DEVELOPMENT("development", "开发环境"),
        DEMO("demo", "演示环境");
        
        private final String code;
        private final String description;
        
        EnvironmentType(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 业务重要性枚举
     */
    @lombok.Getter
    public enum BusinessImportance {
        LOW("low", "低重要性"),
        MEDIUM("medium", "中等重要性"),
        HIGH("high", "高重要性"),
        CRITICAL("critical", "关键重要性");
        
        private final String code;
        private final String description;
        
        BusinessImportance(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 业务时间窗口
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessTimeWindow implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 是否在业务时间内
         */
        private Boolean inBusinessHours;
        
        /**
         * 业务时间描述
         */
        private String businessHoursDescription;
        
        /**
         * 维护窗口
         */
        private String maintenanceWindow;
        
        /**
         * 关键业务时间
         */
        private String criticalBusinessTime;
    }
    
    /**
     * 趋势方向枚举
     */
    @lombok.Getter
    public enum TrendDirection {
        INCREASING("increasing", "上升"),
        DECREASING("decreasing", "下降"),
        STABLE("stable", "稳定"),
        FLUCTUATING("fluctuating", "波动");
        
        private final String code;
        private final String description;
        
        TrendDirection(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 创建默认上下文
     * 
     * @return 默认格式化上下文
     */
    public static FormattingContext createDefault() {
        return FormattingContext.builder()
                .targetAudience(TargetAudience.SECURITY_ANALYST)
                .outputChannel(OutputChannel.CONSOLE)
                .language("zh-CN")
                .strategy(AlarmFormattedContent.FormattingStrategy.STANDARD)
                .userPreferences(UserPreferences.builder()
                        .detailLevel(DetailLevel.STANDARD)
                        .technicalTermLevel(TechnicalTermLevel.INTERMEDIATE)
                        .includeSuggestions(true)
                        .includePrinciple(true)
                        .includeImpactAnalysis(true)
                        .includeRelatedInfo(false)
                        .build())
                .build();
    }
    
    /**
     * 创建简洁上下文
     * 
     * @return 简洁格式化上下文
     */
    public static FormattingContext createBrief() {
        return FormattingContext.builder()
                .targetAudience(TargetAudience.BUSINESS_USER)
                .outputChannel(OutputChannel.SMS)
                .language("zh-CN")
                .strategy(AlarmFormattedContent.FormattingStrategy.BRIEF)
                .userPreferences(UserPreferences.builder()
                        .detailLevel(DetailLevel.MINIMAL)
                        .technicalTermLevel(TechnicalTermLevel.NONE)
                        .includeSuggestions(true)
                        .includePrinciple(false)
                        .includeImpactAnalysis(false)
                        .includeRelatedInfo(false)
                        .maxLength(160)
                        .build())
                .build();
    }
    
    /**
     * 创建技术上下文
     * 
     * @return 技术格式化上下文
     */
    public static FormattingContext createTechnical() {
        return FormattingContext.builder()
                .targetAudience(TargetAudience.SECURITY_ANALYST)
                .outputChannel(OutputChannel.EMAIL)
                .language("zh-CN")
                .strategy(AlarmFormattedContent.FormattingStrategy.TECHNICAL)
                .userPreferences(UserPreferences.builder()
                        .detailLevel(DetailLevel.COMPREHENSIVE)
                        .technicalTermLevel(TechnicalTermLevel.EXPERT)
                        .includeSuggestions(true)
                        .includePrinciple(true)
                        .includeImpactAnalysis(true)
                        .includeRelatedInfo(true)
                        .build())
                .build();
    }
}
