package com.geeksec.threatdetector.killchain.analyzer;

import com.geeksec.threatdetector.killchain.knowledge.AttackChainKnowledgeBase;
import com.geeksec.threatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.threatdetector.killchain.model.AttackChainEvent;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import com.geeksec.threatdetector.model.output.Alarm;
import com.geeksec.threatdetector.state.CoreStateManager;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 攻击链分析器
 * 负责分析告警并构建攻击链
 * 
 * <AUTHOR>
 */
@Slf4j
public class AttackChainAnalyzer implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 攻击链知识库
     */
    private final AttackChainKnowledgeBase knowledgeBase;
    
    /**
     * 阶段分类器
     */
    private final StageClassifier stageClassifier;
    
    /**
     * 攻击链重建器
     */
    private final ChainReconstructor chainReconstructor;
    
    /**
     * 进展评估器
     */
    private final ProgressEvaluator progressEvaluator;
    
    /**
     * 核心状态管理器（用于跨作业状态共享）
     */
    private final CoreStateManager coreStateManager;
    
    /**
     * 事件关联窗口（分钟）
     */
    private final int correlationWindowMinutes;
    
    /**
     * 统计信息
     */
    private final AtomicLong totalAnalyzed = new AtomicLong(0);
    private final AtomicLong chainsCreated = new AtomicLong(0);
    private final AtomicLong chainsUpdated = new AtomicLong(0);
    
    /**
     * 构造函数
     *
     * @param correlationWindowMinutes 事件关联窗口（分钟）
     */
    public AttackChainAnalyzer(int correlationWindowMinutes) {
        this.knowledgeBase = new AttackChainKnowledgeBase();
        this.stageClassifier = new StageClassifier(knowledgeBase);
        this.chainReconstructor = new ChainReconstructor();
        this.progressEvaluator = new ProgressEvaluator();
        this.coreStateManager = CoreStateManager.getInstance();
        this.correlationWindowMinutes = correlationWindowMinutes;

        log.info("攻击链分析器初始化完成，关联窗口: {}分钟", correlationWindowMinutes);
    }
    
    /**
     * 默认构造函数
     */
    public AttackChainAnalyzer() {
        this(60); // 默认60分钟关联窗口
    }
    
    /**
     * 分析告警并更新攻击链
     * 
     * @param alarm 告警信息
     * @return 攻击链分析结果
     */
    public AttackChainAnalysisResult analyzeAlarm(Alarm alarm) {
        if (alarm == null) {
            return AttackChainAnalysisResult.createEmpty();
        }
        
        totalAnalyzed.incrementAndGet();
        
        try {
            // 1. 将告警转换为攻击链事件
            AttackChainEvent event = convertAlarmToEvent(alarm);
            
            // 2. 分类事件到Kill Chain阶段
            CyberKillChainStage stage = stageClassifier.classifyEvent(event);
            event.setKillChainStage(stage);
            
            // 3. 查找或创建攻击链
            AttackChainAnalysis attackChain = findOrCreateAttackChain(event);
            
            // 4. 更新攻击链
            updateAttackChain(attackChain, event);

            // 5. 重建攻击链结构
            chainReconstructor.reconstructChain(attackChain);

            // 6. 评估攻击进展
            progressEvaluator.evaluateProgress(attackChain);

            // 7. 更新分析时间
            attackChain.setAnalysisTime(LocalDateTime.now());

            // 8. 保存攻击链摘要到核心状态管理器（用于跨作业关联）
            saveAttackChainSummary(attackChain);
            
            log.debug("攻击链分析完成: 告警={}, 攻击链={}, 阶段={}", 
                    alarm.getAlarmId(), attackChain.getAttackChainId(), stage);
            
            return AttackChainAnalysisResult.builder()
                    .success(true)
                    .attackChainId(attackChain.getAttackChainId())
                    .killChainStage(stage)
                    .attackChainAnalysis(attackChain)
                    .newChainCreated(false) // 这里需要根据实际情况设置
                    .build();
            
        } catch (Exception e) {
            log.error("攻击链分析失败: 告警={}, 错误={}", alarm.getAlarmId(), e.getMessage(), e);
            return AttackChainAnalysisResult.createError(e.getMessage());
        }
    }
    
    /**
     * 将告警转换为攻击链事件
     * 
     * @param alarm 告警信息
     * @return 攻击链事件
     */
    private AttackChainEvent convertAlarmToEvent(Alarm alarm) {
        AttackChainEvent.AttackChainEventBuilder builder = AttackChainEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .alarmId(alarm.getAlarmId())
                .timestamp(alarm.getTimestamp() != null ? alarm.getTimestamp() : LocalDateTime.now())
                .eventName(alarm.getAlarmName())
                .description(alarm.getDescription())
                .threatType(alarm.getAlarmType())
                .sourceIp(alarm.getSrcIp())
                .targetIp(alarm.getDstIp())
                .sourcePort(alarm.getSrcPort())
                .targetPort(alarm.getDstPort())
                .protocol(alarm.getProtocol())
                .confidence(alarm.getConfidence())
                .createTime(LocalDateTime.now());
        
        // 设置事件严重程度
        AttackChainEvent.EventSeverity severity = determineSeverity(alarm);
        builder.severity(severity);
        
        // 添加威胁指标
        List<AttackChainEvent.ThreatIndicator> indicators = extractThreatIndicators(alarm);
        builder.indicators(indicators);
        
        // 设置攻击者信息
        AttackChainEvent.AttackerInfo attackerInfo = extractAttackerInfo(alarm);
        builder.attackerInfo(attackerInfo);
        
        // 设置受害者信息
        AttackChainEvent.VictimInfo victimInfo = extractVictimInfo(alarm);
        builder.victimInfo(victimInfo);
        
        return builder.build();
    }
    
    /**
     * 确定事件严重程度
     * 
     * @param alarm 告警信息
     * @return 事件严重程度
     */
    private AttackChainEvent.EventSeverity determineSeverity(Alarm alarm) {
        String alarmType = alarm.getAlarmType();
        Double confidence = alarm.getConfidence();
        
        // 基于告警类型判断
        if (alarmType != null) {
            if (alarmType.contains("关键") || alarmType.contains("严重")) {
                return AttackChainEvent.EventSeverity.CRITICAL;
            } else if (alarmType.contains("高危")) {
                return AttackChainEvent.EventSeverity.HIGH;
            } else if (alarmType.contains("中危")) {
                return AttackChainEvent.EventSeverity.MEDIUM;
            } else if (alarmType.contains("低危")) {
                return AttackChainEvent.EventSeverity.LOW;
            }
        }
        
        // 基于置信度判断
        if (confidence != null) {
            if (confidence >= 0.9) {
                return AttackChainEvent.EventSeverity.CRITICAL;
            } else if (confidence >= 0.7) {
                return AttackChainEvent.EventSeverity.HIGH;
            } else if (confidence >= 0.5) {
                return AttackChainEvent.EventSeverity.MEDIUM;
            } else {
                return AttackChainEvent.EventSeverity.LOW;
            }
        }
        
        return AttackChainEvent.EventSeverity.MEDIUM;
    }
    
    /**
     * 提取威胁指标
     * 
     * @param alarm 告警信息
     * @return 威胁指标列表
     */
    private List<AttackChainEvent.ThreatIndicator> extractThreatIndicators(Alarm alarm) {
        List<AttackChainEvent.ThreatIndicator> indicators = new ArrayList<>();
        
        // 添加IP地址指标
        if (alarm.getSrcIp() != null) {
            indicators.add(AttackChainEvent.ThreatIndicator.builder()
                    .type(AttackChainEvent.ThreatIndicator.IndicatorType.IP_ADDRESS)
                    .value(alarm.getSrcIp())
                    .description("源IP地址")
                    .confidence(0.8)
                    .firstSeen(LocalDateTime.now())
                    .lastSeen(LocalDateTime.now())
                    .build());
        }
        
        if (alarm.getDstIp() != null) {
            indicators.add(AttackChainEvent.ThreatIndicator.builder()
                    .type(AttackChainEvent.ThreatIndicator.IndicatorType.IP_ADDRESS)
                    .value(alarm.getDstIp())
                    .description("目标IP地址")
                    .confidence(0.8)
                    .firstSeen(LocalDateTime.now())
                    .lastSeen(LocalDateTime.now())
                    .build());
        }
        
        // 可以根据告警内容提取更多指标
        // 如域名、文件哈希、URL等
        
        return indicators;
    }
    
    /**
     * 提取攻击者信息
     * 
     * @param alarm 告警信息
     * @return 攻击者信息
     */
    private AttackChainEvent.AttackerInfo extractAttackerInfo(Alarm alarm) {
        return AttackChainEvent.AttackerInfo.builder()
                .attackerIp(alarm.getSrcIp())
                .geolocation("未知") // 可以通过IP地理位置服务获取
                .organization("未知")
                .asn("未知")
                .threatGroup("未知")
                .campaignName("未知")
                .build();
    }
    
    /**
     * 提取受害者信息
     * 
     * @param alarm 告警信息
     * @return 受害者信息
     */
    private AttackChainEvent.VictimInfo extractVictimInfo(Alarm alarm) {
        return AttackChainEvent.VictimInfo.builder()
                .victimIp(alarm.getDstIp())
                .hostname("未知")
                .operatingSystem("未知")
                .assetType("未知")
                .businessCriticality("未知")
                .department("未知")
                .build();
    }
    
    /**
     * 查找或创建攻击链
     * 
     * @param event 攻击链事件
     * @return 攻击链分析结果
     */
    private AttackChainAnalysis findOrCreateAttackChain(AttackChainEvent event) {
        // 尝试关联到现有攻击链
        String correlatedChainId = correlateToExistingChain(event);
        
        if (correlatedChainId != null) {
            // 从核心状态管理器获取攻击链摘要
            CoreStateManager.AttackChainSummary summary = coreStateManager.getActiveAttackChain(correlatedChainId);
            if (summary != null) {
                event.setAttackChainId(correlatedChainId);
                chainsUpdated.incrementAndGet();
                // 基于摘要重建攻击链分析对象（简化版本）
                return buildAttackChainFromSummary(summary, event);
            }
        }
        
        // 创建新的攻击链
        String newChainId = generateAttackChainId(event);
        event.setAttackChainId(newChainId);
        
        AttackChainAnalysis newChain = AttackChainAnalysis.builder()
                .attackChainId(newChainId)
                .campaignName("攻击活动-" + newChainId.substring(0, 8))
                .events(new ArrayList<>())
                .analysisTime(LocalDateTime.now())
                .analysisVersion("1.0")
                .confidence(0.5)
                .build();
        
        chainsCreated.incrementAndGet();

        log.info("创建新攻击链: {}", newChainId);
        return newChain;
    }
    
    /**
     * 关联到现有攻击链
     *
     * @param event 攻击链事件
     * @return 关联的攻击链ID，如果没有关联则返回null
     */
    private String correlateToExistingChain(AttackChainEvent event) {
        LocalDateTime eventTime = event.getTimestamp();
        LocalDateTime windowStart = eventTime.minus(correlationWindowMinutes, ChronoUnit.MINUTES);

        // 从核心状态管理器获取活跃攻击链
        Set<String> activeChainIds = coreStateManager.getActiveAttackChainIds();
        for (String chainId : activeChainIds) {
            CoreStateManager.AttackChainSummary summary = coreStateManager.getActiveAttackChain(chainId);
            if (summary != null && isEventCorrelatedToSummary(event, summary, windowStart, eventTime)) {
                return summary.getAttackChainId();
            }
        }

        return null;
    }
    
    /**
     * 判断事件是否与攻击链相关
     * 
     * @param event 攻击链事件
     * @param chain 攻击链
     * @param windowStart 时间窗口开始
     * @param windowEnd 时间窗口结束
     * @return 是否相关
     */
    private boolean isEventCorrelatedToChain(AttackChainEvent event, AttackChainAnalysis chain,
                                           LocalDateTime windowStart, LocalDateTime windowEnd) {
        if (chain.getEvents() == null || chain.getEvents().isEmpty()) {
            return false;
        }
        
        // 检查时间窗口内的事件
        List<AttackChainEvent> recentEvents = chain.getEvents().stream()
                .filter(e -> e.getTimestamp().isAfter(windowStart) && e.getTimestamp().isBefore(windowEnd))
                .collect(Collectors.toList());
        
        if (recentEvents.isEmpty()) {
            return false;
        }
        
        // 基于IP地址关联
        for (AttackChainEvent recentEvent : recentEvents) {
            if (isSameAttacker(event, recentEvent) || isSameTarget(event, recentEvent)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为同一攻击者
     * 
     * @param event1 事件1
     * @param event2 事件2
     * @return 是否为同一攻击者
     */
    private boolean isSameAttacker(AttackChainEvent event1, AttackChainEvent event2) {
        return event1.getSourceIp() != null && 
               event1.getSourceIp().equals(event2.getSourceIp());
    }
    
    /**
     * 判断是否为同一目标
     * 
     * @param event1 事件1
     * @param event2 事件2
     * @return 是否为同一目标
     */
    private boolean isSameTarget(AttackChainEvent event1, AttackChainEvent event2) {
        return event1.getTargetIp() != null && 
               event1.getTargetIp().equals(event2.getTargetIp());
    }
    
    /**
     * 生成攻击链ID
     * 
     * @param event 攻击链事件
     * @return 攻击链ID
     */
    private String generateAttackChainId(AttackChainEvent event) {
        return "chain-" + UUID.randomUUID().toString();
    }
    
    /**
     * 更新攻击链
     * 
     * @param attackChain 攻击链
     * @param event 新事件
     */
    private void updateAttackChain(AttackChainAnalysis attackChain, AttackChainEvent event) {
        if (attackChain.getEvents() == null) {
            attackChain.setEvents(new ArrayList<>());
        }
        
        attackChain.getEvents().add(event);
        
        // 更新攻击链的基本信息
        updateAttackChainMetadata(attackChain);
    }
    
    /**
     * 更新攻击链元数据
     * 
     * @param attackChain 攻击链
     */
    private void updateAttackChainMetadata(AttackChainAnalysis attackChain) {
        List<AttackChainEvent> events = attackChain.getEvents();
        if (events == null || events.isEmpty()) {
            return;
        }
        
        // 更新时间线
        updateTimeline(attackChain, events);
        
        // 更新威胁评估
        updateThreatAssessment(attackChain, events);
        
        // 更新置信度
        updateConfidence(attackChain, events);
    }
    
    /**
     * 更新时间线
     * 
     * @param attackChain 攻击链
     * @param events 事件列表
     */
    private void updateTimeline(AttackChainAnalysis attackChain, List<AttackChainEvent> events) {
        LocalDateTime startTime = events.stream()
                .map(AttackChainEvent::getTimestamp)
                .min(LocalDateTime::compareTo)
                .orElse(LocalDateTime.now());
        
        LocalDateTime endTime = events.stream()
                .map(AttackChainEvent::getTimestamp)
                .max(LocalDateTime::compareTo)
                .orElse(null);
        
        long durationSeconds = startTime.until(endTime != null ? endTime : LocalDateTime.now(), ChronoUnit.SECONDS);
        
        AttackChainAnalysis.AttackChainTimeline timeline = AttackChainAnalysis.AttackChainTimeline.builder()
                .startTime(startTime)
                .endTime(endTime)
                .durationSeconds(durationSeconds)
                .build();
        
        attackChain.setTimeline(timeline);
    }
    
    /**
     * 更新威胁评估
     * 
     * @param attackChain 攻击链
     * @param events 事件列表
     */
    private void updateThreatAssessment(AttackChainAnalysis attackChain, List<AttackChainEvent> events) {
        // 计算平均风险评分
        int avgRiskScore = (int) events.stream()
                .mapToInt(AttackChainEvent::getRiskScore)
                .average()
                .orElse(0);
        
        // 确定威胁等级
        AttackChainAnalysis.ThreatAssessment.ThreatLevel threatLevel;
        if (avgRiskScore >= 80) {
            threatLevel = AttackChainAnalysis.ThreatAssessment.ThreatLevel.CRITICAL;
        } else if (avgRiskScore >= 60) {
            threatLevel = AttackChainAnalysis.ThreatAssessment.ThreatLevel.HIGH;
        } else if (avgRiskScore >= 40) {
            threatLevel = AttackChainAnalysis.ThreatAssessment.ThreatLevel.MEDIUM;
        } else {
            threatLevel = AttackChainAnalysis.ThreatAssessment.ThreatLevel.LOW;
        }
        
        // 收集威胁类型
        List<String> threatTypes = events.stream()
                .map(AttackChainEvent::getThreatType)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        
        AttackChainAnalysis.ThreatAssessment threatAssessment = AttackChainAnalysis.ThreatAssessment.builder()
                .threatLevel(threatLevel)
                .riskScore(avgRiskScore)
                .threatTypes(threatTypes)
                .threatSource(AttackChainAnalysis.ThreatAssessment.ThreatSource.EXTERNAL)
                .build();
        
        attackChain.setThreatAssessment(threatAssessment);
    }
    
    /**
     * 更新置信度
     * 
     * @param attackChain 攻击链
     * @param events 事件列表
     */
    private void updateConfidence(AttackChainAnalysis attackChain, List<AttackChainEvent> events) {
        double avgConfidence = events.stream()
                .filter(e -> e.getConfidence() != null)
                .mapToDouble(AttackChainEvent::getConfidence)
                .average()
                .orElse(0.5);
        
        // 基于事件数量调整置信度
        double eventCountFactor = Math.min(events.size() * 0.1, 0.3);
        double finalConfidence = Math.min(avgConfidence + eventCountFactor, 1.0);
        
        attackChain.setConfidence(finalConfidence);
    }
    
    /**
     * 获取活跃攻击链摘要
     *
     * @return 活跃攻击链摘要列表
     */
    public List<CoreStateManager.AttackChainSummary> getActiveAttackChainSummaries() {
        Set<String> activeChainIds = coreStateManager.getActiveAttackChainIds();
        List<CoreStateManager.AttackChainSummary> summaries = new ArrayList<>();

        for (String chainId : activeChainIds) {
            CoreStateManager.AttackChainSummary summary = coreStateManager.getActiveAttackChain(chainId);
            if (summary != null) {
                summaries.add(summary);
            }
        }

        return summaries;
    }

    /**
     * 获取指定攻击链摘要
     *
     * @param attackChainId 攻击链ID
     * @return 攻击链摘要
     */
    public CoreStateManager.AttackChainSummary getAttackChainSummary(String attackChainId) {
        return coreStateManager.getActiveAttackChain(attackChainId);
    }
    
    /**
     * 清理过期的攻击链
     *
     * @param maxAgeHours 最大年龄（小时）
     */
    public void cleanupExpiredChains(int maxAgeHours) {
        // 简化清理逻辑，由核心状态管理器的TTL自动处理
        log.info("攻击链清理由Redis TTL自动处理，最大年龄: {}小时", maxAgeHours);
    }

    /**
     * 获取分析统计信息
     *
     * @return 统计信息
     */
    public AnalysisStatistics getStatistics() {
        Set<String> activeChainIds = coreStateManager.getActiveAttackChainIds();
        return new AnalysisStatistics(
                totalAnalyzed.get(),
                chainsCreated.get(),
                chainsUpdated.get(),
                activeChainIds.size()
        );
    }
    
    /**
     * 攻击链分析结果
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class AttackChainAnalysisResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean success;
        private String attackChainId;
        private CyberKillChainStage killChainStage;
        private AttackChainAnalysis attackChainAnalysis;
        private boolean newChainCreated;
        private String errorMessage;
        
        public static AttackChainAnalysisResult createEmpty() {
            return AttackChainAnalysisResult.builder()
                    .success(false)
                    .errorMessage("空告警")
                    .build();
        }
        
        public static AttackChainAnalysisResult createError(String errorMessage) {
            return AttackChainAnalysisResult.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .build();
        }
    }

    /**
     * 保存攻击链摘要到核心状态管理器
     *
     * @param attackChain 攻击链分析结果
     */
    private void saveAttackChainSummary(AttackChainAnalysis attackChain) {
        try {
            CoreStateManager.AttackChainSummary summary = CoreStateManager.AttackChainSummary.builder()
                    .attackChainId(attackChain.getAttackChainId())
                    .attackerIp(extractAttackerIp(attackChain))
                    .victimIp(extractVictimIp(attackChain))
                    .currentStage(attackChain.getProgressAssessment() != null ?
                            attackChain.getProgressAssessment().getCurrentStage().getStageCode() : "unknown")
                    .confidence(attackChain.getConfidence())
                    .startTime(attackChain.getTimeline() != null ?
                            attackChain.getTimeline().getStartTime() : LocalDateTime.now())
                    .lastUpdateTime(LocalDateTime.now())
                    .active(true)
                    .metadata(buildMetadata(attackChain))
                    .build();

            coreStateManager.saveActiveAttackChain(attackChain.getAttackChainId(), summary);

        } catch (Exception e) {
            log.error("保存攻击链摘要失败: {}", attackChain.getAttackChainId(), e);
        }
    }

    /**
     * 基于摘要重建攻击链分析对象（简化版本）
     *
     * @param summary 攻击链摘要
     * @param event 当前事件
     * @return 攻击链分析对象
     */
    private AttackChainAnalysis buildAttackChainFromSummary(CoreStateManager.AttackChainSummary summary,
                                                          AttackChainEvent event) {
        AttackChainAnalysis.AttackChainAnalysisBuilder builder = AttackChainAnalysis.builder()
                .attackChainId(summary.getAttackChainId())
                .confidence(summary.getConfidence())
                .analysisTime(LocalDateTime.now())
                .events(new ArrayList<>(Arrays.asList(event)));

        // 设置时间线
        if (summary.getStartTime() != null) {
            AttackChainAnalysis.AttackChainTimeline timeline = AttackChainAnalysis.AttackChainTimeline.builder()
                    .startTime(summary.getStartTime())
                    .lastEventTime(LocalDateTime.now())
                    .build();
            builder.timeline(timeline);
        }

        return builder.build();
    }

    /**
     * 检查事件是否与攻击链摘要相关
     *
     * @param event 事件
     * @param summary 攻击链摘要
     * @param windowStart 时间窗口开始
     * @param windowEnd 时间窗口结束
     * @return 是否相关
     */
    private boolean isEventCorrelatedToSummary(AttackChainEvent event,
                                             CoreStateManager.AttackChainSummary summary,
                                             LocalDateTime windowStart,
                                             LocalDateTime windowEnd) {
        // 检查时间窗口
        if (summary.getLastUpdateTime() != null &&
            (summary.getLastUpdateTime().isBefore(windowStart) ||
             summary.getLastUpdateTime().isAfter(windowEnd))) {
            return false;
        }

        // 检查IP关联
        if (event.getSourceIp() != null && summary.getAttackerIp() != null) {
            if (event.getSourceIp().equals(summary.getAttackerIp())) {
                return true;
            }
        }

        if (event.getTargetIp() != null && summary.getVictimIp() != null) {
            if (event.getTargetIp().equals(summary.getVictimIp())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取攻击者IP
     */
    private String extractAttackerIp(AttackChainAnalysis attackChain) {
        if (attackChain.getEvents() != null && !attackChain.getEvents().isEmpty()) {
            return attackChain.getEvents().get(0).getSourceIp();
        }
        return null;
    }

    /**
     * 提取受害者IP
     */
    private String extractVictimIp(AttackChainAnalysis attackChain) {
        if (attackChain.getEvents() != null && !attackChain.getEvents().isEmpty()) {
            return attackChain.getEvents().get(0).getTargetIp();
        }
        return null;
    }

    /**
     * 构建元数据
     */
    private Map<String, Object> buildMetadata(AttackChainAnalysis attackChain) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("campaign_name", attackChain.getCampaignName());
        metadata.put("event_count", attackChain.getEvents() != null ? attackChain.getEvents().size() : 0);

        if (attackChain.getProgressAssessment() != null) {
            metadata.put("progress_percentage", attackChain.getProgressAssessment().getProgressPercentage());
            metadata.put("success_probability", attackChain.getProgressAssessment().getSuccessProbability());
        }

        return metadata;
    }

    /**
     * 分析统计信息
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class AnalysisStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final long totalAnalyzed;
        private final long chainsCreated;
        private final long chainsUpdated;
        private final int activeChains;
        
        public double getChainCreationRate() {
            return totalAnalyzed > 0 ? (double) chainsCreated / totalAnalyzed : 0.0;
        }
        
        public double getChainUpdateRate() {
            return totalAnalyzed > 0 ? (double) chainsUpdated / totalAnalyzed : 0.0;
        }
    }
}
