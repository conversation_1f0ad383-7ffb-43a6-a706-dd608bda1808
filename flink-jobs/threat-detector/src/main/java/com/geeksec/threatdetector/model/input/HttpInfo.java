package com.geeksec.threatdetector.model.input;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * HTTP协议相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HttpInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * HTTP方法
     */
    private String method;

    /**
     * 请求URI
     */
    private String uri;

    /**
     * 完整URL
     */
    private String url;

    /**
     * HTTP版本
     */
    private String version;

    /**
     * 主机名
     */
    private String host;

    /**
     * User-Agent
     */
    private String userAgent;

    /**
     * Referer
     */
    private String referer;

    /**
     * Content-Type
     */
    private String contentType;

    /**
     * Content-Length
     */
    private Long contentLength;

    /**
     * HTTP状态码
     */
    private Integer statusCode;

    /**
     * 请求头
     */
    private Map<String, String> requestHeaders;

    /**
     * 响应头
     */
    private Map<String, String> responseHeaders;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 请求体大小
     */
    private Long requestBodySize;

    /**
     * 响应体大小
     */
    private Long responseBodySize;
}
