package com.geeksec.threatdetector.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 威胁类型枚举
 * 包含各种安全威胁、工具、指纹和行为的分类
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
@ToString(of = {"name", "code"})
public enum ThreatTypeEnum {
    // ========================== WebShell相关 ==========================
    /** WebShell管理工具 */
    GODZILLA(29090, "哥斯拉", "跨平台WebShell管理工具"),
    SHARPYSHELL(29091, "SharPyShell", "基于C#的WebShell管理工具"),
    WEEVELY(29092, "Weevely", "PHP WebShell管理工具"),
    TIANXIE(29093, "天蝎", "WebShell管理工具"),
    JSPMASTER(29094, "jspmaster", "JSP WebShell管理工具"),
    B374K(29095, "b374k", "PHP WebShell管理工具"),
    
    /** 中国菜刀系列 */
    CNKNIFE(29096, "中国菜刀", "经典WebShell管理工具"),
    XISE(29097, "Xise", "中国菜刀WebShell管理工具"),
    CKNIFE(29105, "cknife", "中国菜刀WebShell管理工具"),
    
    /** 蚁剑系列 */
    ANT_SWORD(29098, "蚁剑", "开源WebShell管理工具"),
    ANT_SWORD_2(29105, "蚁剑2", "蚁剑WebShell管理工具升级版"),
    ANT_SWORD_PHP_MID(27077, "antsword中间标签", "蚁剑PHP中间件WebShell特征"),
    ANT_SWORD_PHP_REQ(27078, "antSword_PHP自定义编码请求", "蚁剑PHP请求WebShell特征"),
    ANT_SWORD_PHP_RES(27079, "antSword_PHP自定义编码响应", "蚁剑PHP响应WebShell特征"),
    
    /** 冰蝎系列 */
    BEHINDER3(27074, "冰蝎3", "基于Java的WebShell管理工具"),
    BEHINDER4(27075, "冰蝎4", "冰蝎WebShell管理工具升级版"),
    
    /** 其他WebShell工具 */
    WEBACOO(29099, "WeBacoo", "PHP WebShell管理工具"),
    WEBSHELL_SNIPER(29100, "Webshell-Sniper", "WebShell管理工具"),
    KAISHANFU(29101, "开山斧", "WebShell管理工具"),
    ALTMAN(29102, "Altman", "跨平台WebShell管理工具"),
    QUASIBOT(29103, "QuasiBot", "WebShell管理工具"),
    WEBSHELL_MANAGER(29104, "WebshellManager", "WebShell管理工具"),
    WEBKNIFE(29106, "WebKnife", "WebShell管理工具"),
    K8KNIFE(29107, "K8飞刀", "WebShell管理工具"),
    HATCHET(29108, "Hatchet", "WebShell管理工具"),
    XIAOLIKNIFE(29109, "小李飞刀", "WebShell管理工具"),
    W8AY(27120, "w8ay", "WebShell管理工具"),
    
    /** WebShell连接类型 */
    ENCRYPTED_WEBSHELL(23501, "加密WebShell连接", "加密的WebShell通信"),
    PLAIN_TEXT_WEBSHELL(23502, "非加密WebShell连接", "未加密的WebShell通信"),
    
    // ========================== 远程控制工具 (RAT) ==========================
    /** 开源RAT */
    PUPY(29110, "Pupy", "跨平台RAT，支持Python"),
    QUASAR(29117, "Quasar", "Windows RAT，开源"),
    
    /** 高级RAT/C2框架 */
    COBALT_STRIKE(29113, "CobaltStrike", "商业渗透测试工具，常被APT组织使用"),
    METASPLOIT(29114, "Metasploit", "渗透测试框架，包含多种攻击载荷"),
    EMPIRE(29112, "Empire", "后渗透框架，PowerShell实现"),
    
    /** 其他RAT */
    KOADIC(29111, "Koadic", "Windows RAT，使用JavaScript"),
    MERLIN(29115, "merlin", "跨平台RAT，Go语言编写"),
    PYFUD(29116, "PyFUD", "Python RAT"),
    
    /** RAT相关通用标签 */
    KNOWN_REMOTE_CONTROL_TOOL(24331, "已知远程控制工具", "已知的远程控制工具"),
    UNKNOWN_REMOTE_CONTROL_TOOL(24332, "未知远程控制工具", "未知的远程控制工具"),
    REMOTE_CONTROL_ATTACK(24334, "远程控制攻击", "远程控制攻击行为"),
    
    // ========================== 远程管理工具 ==========================
    /** SSH客户端 */
    FINAL_SHELL(24231, "FinalShell", "国产SSH客户端"),
    MOBAXTERM(24232, "Mobaxterm", "增强型SSH客户端"),
    PUTTY(24233, "Putty", "轻量级SSH客户端"),
    SECURE_CRT(24234, "SecureCRT", "商业SSH客户端"),
    XSHELL(24235, "Xshell", "商业SSH客户端"),
    
    // ========================== 远程协助工具 ==========================
    SUNLOGIN_FULL(24122, "向日葵完全版", "向日葵远程控制"),
    SUNLOGIN_SOS_MASTER(24124, "向日葵-SOS 主控", "向日葵远程协助主控端"),
    SUNLOGIN_SOS_CLIENT(24125, "向日葵-SOS 被控", "向日葵远程协助被控端"),
    TODESK_LITE(24127, "ToDesk精简版", "ToDesk远程控制"),
    TEAMVIEWER(24128, "TeamViewer", "TeamViewer远程控制"),
    TODESK_ASSISTANT(24129, "ToDesk辅助规则", "ToDesk辅助功能"),
    TODESK_SMALL_IP_MASTER(24130, "ToDesk正式版小IP主控", "ToDesk主控端"),
    TODESK_LARGE_IP_MASTER(24131, "ToDesk正式版大IP主控", "ToDesk主控端"),
    ANYDESK(24132, "AnyDesk", "AnyDesk远程控制"),
    
    // ========================== 恶意软件 ==========================
    /** 后门木马 */
    REMCOS(26313, "Remcos", "Windows后门木马"),
    GHOST(27127, "Ghost", "Ghost RAT"),
    NANOCORE(27128, "NanoCore", "RAT木马"),
    
    /** 银行木马 */
    DRIDEX(27008, "Dridex", "银行木马"),
    EMOTET(20031, "Emotet", "银行木马"),
    QAKBOT(20032, "Qakbot", "银行木马"),
    ZLOADER(28004, "Zloader", "银行木马"),
    
    /** 僵尸网络 */
    HANCITOR(20033, "Hancitor", "恶意软件分发器"),
    TRICKBOT(20034, "Trickbot", "银行木马和僵尸网络"),
    PHORPIEX(28010, "Phorpiex", "僵尸网络"),
    
    /** 其他恶意软件 */
    THCSSLDOS(27121, "THC-SSL-DOS", "SSL DoS工具"),
    RINFO(28001, "Rinfo", "信息窃取木马"),
    SMOKELOADER(28002, "SmokeLoader", "恶意软件加载器"),
    TOFSEE(28003, "Tofsee", "僵尸网络"),
    QUKART(28005, "Qukart", "窃密木马"),
    
    // ========================== APT组织 ==========================
    APT29(28006, "APT29", "高级持续威胁组织，与俄罗斯有关"),
    APT32(28007, "APT32", "越南背景的APT组织"),
    APT28(28008, "APT28", "俄罗斯背景的APT组织"),
    APTC09(28009, "APTC09", "中国背景的APT组织"),
    
    // ========================== 网络扫描与攻击 ==========================
    /** 扫描行为 */
    PORT_SCAN(3248, "端口扫描行为", "端口扫描行为"),
    XRAY_SACN(27063, "Xray扫描器", "Xray漏洞扫描器"),
    APP_SCAN(27062, "AppScan扫描器", "IBM AppScan漏洞扫描器"),
    
    /** 暴力破解 */
    WEB_LOGIN_BRUTE(21001, "Web登录暴破", "Web登录暴力破解"),
    
    // ========================== 登录相关 ==========================
    RDP_LOGIN_REQ(27064, "RDP登录请求", "RDP协议登录请求"),
    RDP_LOGIN_RETURN(27065, "RDP登录返回", "RDP协议登录响应"),
    SMB_LOGIN_REQ(27066, "SMB登录请求", "SMB协议登录请求"),
    SMB_LOGIN_FAIL(27067, "SMB登录失败", "SMB协议登录失败"),
    ORACLE_CONNECT_REQ(27068, "Oracle连接请求", "Oracle数据库连接请求"),
    ORACLE_LOGIN_FAIL(27069, "Oracle登录失败", "Oracle数据库登录失败"),
    MYSQL_LOGIN_FAIL(27071, "MySQL登录失败", "MySQL数据库登录失败"),
    
    // ========================== 隧道与代理 ==========================
    /** 隧道工具 */
    NEOREG(26258, "Neo-reGeorg", "HTTP隧道工具"),
    TUNN_PROXY_SUO5(27073, "suo5代理隧道", "suo5代理隧道"),
    
    /** 协议隧道 */
    TUNN_ICMP(27123, "ICMP隐蔽隧道", "ICMP协议隧道"),
    TUNN_TLS(27124, "TLS隐蔽隧道", "TLS协议隧道"),
    TUNN_NTP(27125, "NTP隐蔽隧道", "NTP协议隧道"),
    TUNN_TCP(27126, "TCP隐蔽隧道", "TCP协议隧道"),
    TUNN_HTTP(20016, "HTTP隐蔽隧道会话", "HTTP协议隧道"),
    TUNN_DNS(29118, "DNS隐蔽隧道", "DNS协议隧道"),
    
    // ========================== 协议伪造 ==========================
    SPOOF_ICMP(27122, "伪造ICMP协议", "伪造的ICMP协议数据"),
    SPOOF_DNS(29119, "伪造DNS协议", "伪造的DNS协议数据"),
    SPOOF_HTTP(27082, "伪造HTTP协议", "伪造的HTTP协议数据"),
    SPOOF_NTP(27083, "伪造NTP协议", "伪造的NTP协议数据"),
    SPOOF_TLS(27084, "伪造TLS协议", "伪造的TLS协议数据"),
    SPOOF_TCP_TUNN(27080, "疑似伪造TCP隐蔽信道", "可疑的TCP隐蔽信道"),
    SPOOF_TLS_TUNN(27081, "疑似伪造TLS隐蔽信道", "可疑的TLS隐蔽信道"),
    
    // ========================== 可疑行为 ==========================
    KNOWN_REMOTE_CONTROL_TOOL_C2(27089, "已知远程控制工具下的C2行为", "已知远控工具的C2通信"),
    SUS_ACTIVATION(27088, "疑似激活行为", "可疑的激活行为"),
    SUS_CONTROL(27087, "疑似控制行为", "可疑的控制行为"),
    SUS_SERVER_HEARTBEAT(27086, "疑似服务端心跳", "可疑的服务端心跳"),
    SUS_CLIENT_HEARTBEAT(27085, "疑似客户端心跳", "可疑的客户端心跳"),
    PIVOT(24333, "跳板节点", "用作跳板的节点"),
    
    // ========================== 网络基础设施 ==========================
    CDN_DOMAIN(9005, "CDN Domain", "CDN域名"),
    CDN_IP(1008, "CDN IP", "CDN IP地址"),
    DNS_TUNN_SERVER(1006, "DNS隧道服务器", "DNS隧道服务器"),
    DNS_LEG_SERVER(10061, "合法DNS服务器", "合法的DNS服务器"),
    DNS_ILLEG_SERVER(10062, "非法DNS服务器", "非法的DNS服务器"),
    
    // ========================== 指纹相关 ==========================
    TROJAN_FINGERPRINT(14005, "木马程序指纹", "木马程序指纹"),
    RAT_FINGERPRINT(14006, "远程控制软件指纹", "远程控制软件指纹"),
    PT_FINGERPRINT(14007, "渗透测试工具指纹", "渗透测试工具指纹"),
    TOR_FINGERPRINT(14008, "Tor网络指纹", "Tor网络指纹"),
    CRAWL_FINGERPRINT(14009, "网络爬虫指纹", "网络爬虫指纹"),
    TUNNEL_PROXY_FINGERPRINT(14014, "隧道代理工具指纹", "隧道代理工具指纹"),
    HACK_TOOL_FINGERPRINT(14015, "黑客工具指纹", "黑客工具指纹"),
    
    // ========================== 随机性检测 ==========================
    RANDOM_FINGERPRINT(1007, "指纹随机化服务端", "指纹随机化的服务端"),
    RANDOM_PASS_EXT(20056, "密码套件随机性", "密码套件随机性检测"),
    RANDOM_ENCRYPT_EXT(20057, "加密扩展随机性", "加密扩展随机性检测"),
    
    // ========================== 指纹对抗技术 ==========================
    ALG_RANDOM(29120, "随机数对抗", "通过修改随机数生成方式来规避指纹识别"),
    ALG_COMPRESS(29121, "压缩函数对抗", "通过修改压缩函数参数来规避指纹识别"),
    ALG_ENCODE(29122, "编码对抗", "通过修改编码方式来规避指纹识别"),
    ALG_STEGANOGRAPHY(29123, "隐写对抗", "通过隐写技术隐藏真实指纹特征"),
    ALG_BEHAVIOR(29124, "行为对抗", "通过修改请求行为模式来规避检测"),
    
    // ========================== 挖矿相关 ==========================
    MINE_DOMAIN(9006, "Candidate Miner Domain", "可能的挖矿域名"),
    MINE_IP(9008, "MinePool IP", "矿池IP地址"),
    MINER_FINGERPRINT(14010, "挖矿软件指纹", "挖矿软件指纹"),
    
    // ========================== 其他 ==========================
    ICMP_PAYLOAD(27061, "ICMP白负载", "ICMP协议白名单负载"),
    UNK_FINGER(14011, "未知指纹", "未知类型的指纹");

    private final int code;
    private final String name;
    private final String description;
    
    private static final Map<Integer, ThreatTypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ThreatTypeEnum::getCode, Function.identity()));
    
    private static final Map<String, ThreatTypeEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ThreatTypeEnum::getName, Function.identity()));

    /**
     * 构造函数（兼容旧代码）
     * @param code 类型编码
     * @param name 类型名称
     */
    ThreatTypeEnum(int code, String name) {
        this(code, name, name);
    }
    
    /**
     * 根据编码获取枚举实例
     * @param code 类型编码
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static ThreatTypeEnum fromCode(int code) {
        return CODE_MAP.get(code);
    }
    
    /**
     * 根据名称获取枚举实例
     * @param name 类型名称
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static ThreatTypeEnum fromName(String name) {
        return NAME_MAP.get(name);
    }
}
