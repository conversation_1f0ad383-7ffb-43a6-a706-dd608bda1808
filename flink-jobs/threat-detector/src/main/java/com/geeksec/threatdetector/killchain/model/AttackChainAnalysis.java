package com.geeksec.threatdetector.killchain.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 攻击链分析结果模型
 * 包含完整的攻击链分析信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttackChainAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 攻击链ID
     */
    private String attackChainId;

    /**
     * 攻击活动名称
     */
    private String campaignName;

    /**
     * 攻击链事件列表
     */
    private List<AttackChainEvent> events;

    /**
     * 攻击链时间线
     */
    private AttackChainTimeline timeline;

    /**
     * 攻击进展评估
     */
    private AttackProgressAssessment progressAssessment;

    /**
     * 威胁评估
     */
    private ThreatAssessment threatAssessment;

    /**
     * 攻击者画像
     */
    private AttackerProfile attackerProfile;

    /**
     * 影响分析
     */
    private ImpactAnalysis impactAnalysis;

    /**
     * 响应建议
     */
    private ResponseRecommendations responseRecommendations;

    /**
     * 相关威胁情报
     */
    private List<ThreatIntelligence> relatedIntelligence;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析版本
     */
    private String analysisVersion;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 攻击链时间线
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackChainTimeline implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 攻击开始时间
         */
        private LocalDateTime startTime;

        /**
         * 攻击结束时间（如果已结束）
         */
        private LocalDateTime endTime;

        /**
         * 最后事件时间
         */
        private LocalDateTime lastEventTime;

        /**
         * 攻击持续时间（秒）
         */
        private Long durationSeconds;

        /**
         * 各阶段的时间分布
         */
        private Map<CyberKillChainStage, StageTimeInfo> stageTimeDistribution;

        /**
         * 时间线事件
         */
        private List<TimelineEvent> timelineEvents;

        /**
         * 阶段时间信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class StageTimeInfo implements Serializable {
            private static final long serialVersionUID = 1L;

            private LocalDateTime firstEventTime;
            private LocalDateTime lastEventTime;
            private Long durationSeconds;
            private Integer eventCount;
        }

        /**
         * 时间线事件
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TimelineEvent implements Serializable {
            private static final long serialVersionUID = 1L;

            private LocalDateTime timestamp;
            private String eventId;
            private CyberKillChainStage stage;
            private String description;
            private String severity;
        }
    }

    /**
     * 攻击进展评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackProgressAssessment implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 当前攻击阶段
         */
        private CyberKillChainStage currentStage;

        /**
         * 已完成的阶段
         */
        private Set<CyberKillChainStage> completedStages;

        /**
         * 攻击进展百分比
         */
        private Double progressPercentage;

        /**
         * 下一步可能的攻击行为
         */
        private List<String> nextPossibleActions;

        /**
         * 攻击成功概率
         */
        private Double successProbability;

        /**
         * 攻击复杂度
         */
        private AttackComplexity complexity;

        /**
         * 攻击持久性
         */
        private AttackPersistence persistence;

        /**
         * 攻击复杂度枚举
         */
        public enum AttackComplexity {
            LOW("低复杂度"),
            MEDIUM("中等复杂度"),
            HIGH("高复杂度"),
            ADVANCED("高级复杂度");

            private final String description;

            AttackComplexity(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }

        /**
         * 攻击持久性枚举
         */
        public enum AttackPersistence {
            TEMPORARY("临时性攻击"),
            SHORT_TERM("短期攻击"),
            LONG_TERM("长期攻击"),
            PERSISTENT("持久性攻击");

            private final String description;

            AttackPersistence(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }

    /**
     * 威胁评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatAssessment implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 威胁等级
         */
        private ThreatLevel threatLevel;

        /**
         * 风险评分
         */
        private Integer riskScore;

        /**
         * 威胁类型
         */
        private List<String> threatTypes;

        /**
         * 攻击向量
         */
        private List<String> attackVectors;

        /**
         * 威胁来源
         */
        private ThreatSource threatSource;

        /**
         * 威胁动机
         */
        private List<String> threatMotivations;

        /**
         * 威胁等级枚举
         */
        public enum ThreatLevel {
            LOW("低威胁", 1),
            MEDIUM("中等威胁", 2),
            HIGH("高威胁", 3),
            CRITICAL("关键威胁", 4);

            private final String description;
            private final int level;

            ThreatLevel(String description, int level) {
                this.description = description;
                this.level = level;
            }

            public String getDescription() {
                return description;
            }

            public int getLevel() {
                return level;
            }
        }

        /**
         * 威胁来源枚举
         */
        public enum ThreatSource {
            INTERNAL("内部威胁"),
            EXTERNAL("外部威胁"),
            NATION_STATE("国家级威胁"),
            CYBERCRIMINAL("网络犯罪"),
            HACKTIVIST("黑客主义"),
            UNKNOWN("未知来源");

            private final String description;

            ThreatSource(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }

    /**
     * 攻击者画像
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackerProfile implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 攻击者技能水平
         */
        private SkillLevel skillLevel;

        /**
         * 攻击者资源
         */
        private ResourceLevel resourceLevel;

        /**
         * 攻击者动机
         */
        private List<String> motivations;

        /**
         * 可能的威胁组织
         */
        private List<String> possibleThreatGroups;

        /**
         * 攻击者特征
         */
        private Map<String, String> characteristics;

        /**
         * 地理位置信息
         */
        private List<String> geolocations;

        /**
         * 技能水平枚举
         */
        public enum SkillLevel {
            SCRIPT_KIDDIE("脚本小子"),
            INTERMEDIATE("中级攻击者"),
            ADVANCED("高级攻击者"),
            EXPERT("专家级攻击者");

            private final String description;

            SkillLevel(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }

        /**
         * 资源水平枚举
         */
        public enum ResourceLevel {
            LIMITED("有限资源"),
            MODERATE("中等资源"),
            SUBSTANTIAL("充足资源"),
            UNLIMITED("无限资源");

            private final String description;

            ResourceLevel(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }

    /**
     * 影响分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpactAnalysis implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 受影响的资产
         */
        private List<String> affectedAssets;

        /**
         * 业务影响
         */
        private String businessImpact;

        /**
         * 数据影响
         */
        private String dataImpact;

        /**
         * 系统影响
         */
        private String systemImpact;

        /**
         * 财务影响
         */
        private String financialImpact;

        /**
         * 声誉影响
         */
        private String reputationImpact;

        /**
         * 合规影响
         */
        private String complianceImpact;

        /**
         * 影响范围
         */
        private ImpactScope impactScope;

        /**
         * 影响范围枚举
         */
        public enum ImpactScope {
            SINGLE_HOST("单个主机"),
            MULTIPLE_HOSTS("多个主机"),
            NETWORK_SEGMENT("网络段"),
            ENTIRE_NETWORK("整个网络"),
            ORGANIZATION_WIDE("组织范围");

            private final String description;

            ImpactScope(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }

    /**
     * 响应建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseRecommendations implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 立即响应措施
         */
        private List<String> immediateActions;

        /**
         * 短期响应措施
         */
        private List<String> shortTermActions;

        /**
         * 长期响应措施
         */
        private List<String> longTermActions;

        /**
         * 预防措施
         */
        private List<String> preventiveMeasures;

        /**
         * 监控建议
         */
        private List<String> monitoringRecommendations;

        /**
         * 优先级指导
         */
        private String priorityGuidance;
    }

    /**
     * 威胁情报
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatIntelligence implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 情报类型
         */
        private String intelligenceType;

        /**
         * 情报内容
         */
        private String content;

        /**
         * 情报来源
         */
        private String source;

        /**
         * 可信度
         */
        private String reliability;

        /**
         * 相关性评分
         */
        private Double relevanceScore;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }

    /**
     * 获取攻击链的总体风险评分
     * 
     * @return 风险评分（0-100）
     */
    public int getOverallRiskScore() {
        if (threatAssessment != null && threatAssessment.getRiskScore() != null) {
            return threatAssessment.getRiskScore();
        }

        // 基于事件计算风险评分
        if (events != null && !events.isEmpty()) {
            int totalScore = events.stream()
                    .mapToInt(AttackChainEvent::getRiskScore)
                    .sum();
            return Math.min(totalScore / events.size(), 100);
        }

        return 0;
    }

    /**
     * 判断是否为活跃攻击
     * 
     * @return 是否为活跃攻击
     */
    public boolean isActiveAttack() {
        if (timeline != null && timeline.getEndTime() == null) {
            return true; // 攻击尚未结束
        }

        if (progressAssessment != null) {
            return progressAssessment.getCurrentStage() != null &&
                    progressAssessment.getCurrentStage().isLateStage();
        }

        return false;
    }

    /**
     * 获取攻击链的简要描述
     * 
     * @return 简要描述
     */
    public String getBriefDescription() {
        StringBuilder desc = new StringBuilder();

        if (campaignName != null) {
            desc.append(campaignName).append(" - ");
        }

        if (progressAssessment != null && progressAssessment.getCurrentStage() != null) {
            desc.append("当前阶段: ").append(progressAssessment.getCurrentStage().getChineseName());
        }

        if (events != null) {
            desc.append(", 事件数: ").append(events.size());
        }

        if (threatAssessment != null && threatAssessment.getThreatLevel() != null) {
            desc.append(", 威胁等级: ").append(threatAssessment.getThreatLevel().getDescription());
        }

        return desc.toString();
    }
}
