package com.geeksec.threatdetector.killchain.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

/**
 * Cyber Kill Chain阶段枚举
 * 基于Lockheed Martin的Cyber Kill Chain模型
 * 
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
@ToString(of = {"stageNumber", "chineseName", "englishName"})
public enum CyberKillChainStage {
    
    /**
     * 第1阶段：侦察 (Reconnaissance)
     * 攻击者收集目标信息，包括网络拓扑、系统信息、人员信息等
     */
    RECONNAISSANCE(1, "reconnaissance", "侦察", "Reconnaissance",
            "攻击者收集目标组织的信息，包括网络架构、系统配置、人员信息等",
            Arrays.asList("网络扫描", "端口扫描", "域名查询", "社会工程学", "开源情报收集")),
    
    /**
     * 第2阶段：武器化 (Weaponization)
     * 攻击者创建恶意载荷，将漏洞利用代码与后门程序结合
     */
    WEAPONIZATION(2, "weaponization", "武器化", "Weaponization",
            "攻击者创建恶意载荷，将漏洞利用代码与恶意软件结合形成武器",
            Arrays.asList("恶意软件开发", "漏洞利用工具", "载荷封装", "免杀处理", "木马制作")),
    
    /**
     * 第3阶段：投递 (Delivery)
     * 攻击者将武器化的载荷传输到目标环境
     */
    DELIVERY(3, "delivery", "投递", "Delivery",
            "攻击者通过各种方式将恶意载荷传输到目标环境",
            Arrays.asList("钓鱼邮件", "恶意附件", "水坑攻击", "USB投递", "供应链攻击")),
    
    /**
     * 第4阶段：利用 (Exploitation)
     * 攻击者利用漏洞执行恶意代码
     */
    EXPLOITATION(4, "exploitation", "利用", "Exploitation",
            "攻击者利用系统或应用程序漏洞执行恶意代码",
            Arrays.asList("漏洞利用", "缓冲区溢出", "代码注入", "权限提升", "零日攻击")),
    
    /**
     * 第5阶段：安装 (Installation)
     * 攻击者在目标系统上安装恶意软件或后门
     */
    INSTALLATION(5, "installation", "安装", "Installation",
            "攻击者在受害者系统上安装恶意软件、后门或其他持久化机制",
            Arrays.asList("后门安装", "Webshell植入", "服务安装", "注册表修改", "计划任务")),
    
    /**
     * 第6阶段：命令与控制 (Command and Control)
     * 攻击者建立与被感染系统的通信通道
     */
    COMMAND_AND_CONTROL(6, "command_and_control", "命令与控制", "Command and Control",
            "攻击者建立与被感染系统的通信通道，实现远程控制",
            Arrays.asList("C2通信", "DNS隧道", "HTTP/HTTPS通信", "加密通道", "代理通信")),
    
    /**
     * 第7阶段：目标行动 (Actions on Objectives)
     * 攻击者执行最终的攻击目标
     */
    ACTIONS_ON_OBJECTIVES(7, "actions_on_objectives", "目标行动", "Actions on Objectives",
            "攻击者执行最终的攻击目标，如数据窃取、系统破坏、横向移动等",
            Arrays.asList("数据窃取", "数据销毁", "横向移动", "权限维持", "勒索加密"));
    
    /**
     * 阶段序号
     */
    private final int stageNumber;
    
    /**
     * 阶段代码
     */
    private final String stageCode;
    
    /**
     * 中文名称
     */
    private final String chineseName;
    
    /**
     * 英文名称
     */
    private final String englishName;
    
    /**
     * 阶段描述
     */
    private final String description;
    
    /**
     * 典型攻击技术
     */
    private final List<String> typicalTechniques;
    

    
    /**
     * 根据阶段代码获取阶段
     * 
     * @param stageCode 阶段代码
     * @return Cyber Kill Chain阶段
     */
    public static CyberKillChainStage fromStageCode(String stageCode) {
        if (stageCode == null) {
            return null;
        }
        
        for (CyberKillChainStage stage : values()) {
            if (stage.stageCode.equalsIgnoreCase(stageCode)) {
                return stage;
            }
        }
        
        return null;
    }
    
    /**
     * 根据阶段序号获取阶段
     * 
     * @param stageNumber 阶段序号
     * @return Cyber Kill Chain阶段
     */
    public static CyberKillChainStage fromStageNumber(int stageNumber) {
        for (CyberKillChainStage stage : values()) {
            if (stage.stageNumber == stageNumber) {
                return stage;
            }
        }
        
        return null;
    }
    
    /**
     * 根据中文名称获取阶段
     * 
     * @param chineseName 中文名称
     * @return Cyber Kill Chain阶段
     */
    public static CyberKillChainStage fromChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        
        for (CyberKillChainStage stage : values()) {
            if (stage.chineseName.equals(chineseName)) {
                return stage;
            }
        }
        
        return null;
    }
    
    /**
     * 获取下一个阶段
     * 
     * @return 下一个阶段，如果是最后一个阶段则返回null
     */
    public CyberKillChainStage getNextStage() {
        if (stageNumber < 7) {
            return fromStageNumber(stageNumber + 1);
        }
        return null;
    }
    
    /**
     * 获取上一个阶段
     * 
     * @return 上一个阶段，如果是第一个阶段则返回null
     */
    public CyberKillChainStage getPreviousStage() {
        if (stageNumber > 1) {
            return fromStageNumber(stageNumber - 1);
        }
        return null;
    }
    
    /**
     * 判断是否为早期阶段（侦察、武器化、投递）
     * 
     * @return 是否为早期阶段
     */
    public boolean isEarlyStage() {
        return stageNumber <= 3;
    }
    
    /**
     * 判断是否为中期阶段（利用、安装）
     * 
     * @return 是否为中期阶段
     */
    public boolean isMidStage() {
        return stageNumber >= 4 && stageNumber <= 5;
    }
    
    /**
     * 判断是否为后期阶段（命令控制、目标行动）
     * 
     * @return 是否为后期阶段
     */
    public boolean isLateStage() {
        return stageNumber >= 6;
    }
    
    /**
     * 获取阶段的严重程度
     * 
     * @return 严重程度（1-10，数字越大越严重）
     */
    public int getSeverityLevel() {
        switch (this) {
            case RECONNAISSANCE:
                return 2;
            case WEAPONIZATION:
                return 3;
            case DELIVERY:
                return 4;
            case EXPLOITATION:
                return 6;
            case INSTALLATION:
                return 7;
            case COMMAND_AND_CONTROL:
                return 8;
            case ACTIONS_ON_OBJECTIVES:
                return 10;
            default:
                return 5;
        }
    }
    
    /**
     * 获取阶段的紧急程度描述
     * 
     * @return 紧急程度描述
     */
    public String getUrgencyDescription() {
        switch (this) {
            case RECONNAISSANCE:
                return "低紧急度 - 攻击准备阶段，建议加强监控";
            case WEAPONIZATION:
                return "低紧急度 - 武器准备阶段，暂无直接威胁";
            case DELIVERY:
                return "中等紧急度 - 攻击投递阶段，需要及时阻断";
            case EXPLOITATION:
                return "高紧急度 - 漏洞利用阶段，需要立即响应";
            case INSTALLATION:
                return "高紧急度 - 恶意软件安装，需要立即清除";
            case COMMAND_AND_CONTROL:
                return "极高紧急度 - 已建立控制通道，需要紧急处理";
            case ACTIONS_ON_OBJECTIVES:
                return "极高紧急度 - 正在执行攻击目标，需要立即阻止";
            default:
                return "未知紧急度";
        }
    }
    
    /**
     * 获取阶段的防护建议
     * 
     * @return 防护建议列表
     */
    public List<String> getDefenseRecommendations() {
        switch (this) {
            case RECONNAISSANCE:
                return Arrays.asList(
                    "加强网络边界监控",
                    "限制信息泄露",
                    "部署蜜罐系统",
                    "监控异常扫描活动"
                );
            case WEAPONIZATION:
                return Arrays.asList(
                    "威胁情报收集",
                    "恶意软件检测",
                    "沙箱分析",
                    "特征库更新"
                );
            case DELIVERY:
                return Arrays.asList(
                    "邮件安全网关",
                    "Web内容过滤",
                    "USB端口控制",
                    "用户安全培训"
                );
            case EXPLOITATION:
                return Arrays.asList(
                    "及时补丁管理",
                    "漏洞扫描评估",
                    "入侵检测系统",
                    "行为监控分析"
                );
            case INSTALLATION:
                return Arrays.asList(
                    "终端安全防护",
                    "文件完整性监控",
                    "权限管理控制",
                    "系统基线检查"
                );
            case COMMAND_AND_CONTROL:
                return Arrays.asList(
                    "网络流量监控",
                    "DNS安全防护",
                    "代理服务器控制",
                    "异常通信检测"
                );
            case ACTIONS_ON_OBJECTIVES:
                return Arrays.asList(
                    "数据丢失防护",
                    "特权账户监控",
                    "横向移动检测",
                    "应急响应处置"
                );
            default:
                return Arrays.asList("加强安全监控", "及时响应处置");
        }
    }
    
    /**
     * 获取所有阶段的列表
     * 
     * @return 所有阶段的列表
     */
    public static List<CyberKillChainStage> getAllStages() {
        return Arrays.asList(values());
    }
    
    /**
     * 获取早期阶段列表
     * 
     * @return 早期阶段列表
     */
    public static List<CyberKillChainStage> getEarlyStages() {
        return Arrays.asList(RECONNAISSANCE, WEAPONIZATION, DELIVERY);
    }
    
    /**
     * 获取中期阶段列表
     * 
     * @return 中期阶段列表
     */
    public static List<CyberKillChainStage> getMidStages() {
        return Arrays.asList(EXPLOITATION, INSTALLATION);
    }
    
    /**
     * 获取后期阶段列表
     * 
     * @return 后期阶段列表
     */
    public static List<CyberKillChainStage> getLateStages() {
        return Arrays.asList(COMMAND_AND_CONTROL, ACTIONS_ON_OBJECTIVES);
    }
    

}
