package com.geeksec.threatdetector.killchain.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 攻击链事件模型
 * 表示攻击链中的单个事件或告警
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttackChainEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 关联的告警ID
     */
    private String alarmId;

    /**
     * 攻击链ID（用于关联同一攻击活动的多个事件）
     */
    private String attackChainId;

    /**
     * Cyber Kill Chain阶段
     */
    private CyberKillChainStage killChainStage;

    /**
     * 事件时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件描述
     */
    private String description;

    /**
     * 威胁类型
     */
    private String threatType;

    /**
     * 攻击技术（基于MITRE ATT&CK）
     */
    private String attackTechnique;

    /**
     * 攻击战术
     */
    private String attackTactic;

    /**
     * 源IP地址
     */
    private String sourceIp;

    /**
     * 目标IP地址
     */
    private String targetIp;

    /**
     * 源端口
     */
    private Integer sourcePort;

    /**
     * 目标端口
     */
    private Integer targetPort;

    /**
     * 协议类型
     */
    private String protocol;

    /**
     * 检测置信度
     */
    private Double confidence;

    /**
     * 事件严重程度
     */
    private EventSeverity severity;

    /**
     * 攻击者信息
     */
    private AttackerInfo attackerInfo;

    /**
     * 受害者信息
     */
    private VictimInfo victimInfo;

    /**
     * 威胁指标 (IOCs)
     */
    private List<ThreatIndicator> indicators;

    /**
     * 相关文件信息
     */
    private List<FileInfo> relatedFiles;

    /**
     * 网络连接信息
     */
    private List<NetworkConnection> networkConnections;

    /**
     * 事件标签
     */
    private List<String> tags;

    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 事件严重程度枚举
     */
    @Getter
    public enum EventSeverity {
        LOW("低", 1),
        MEDIUM("中", 2),
        HIGH("高", 3),
        CRITICAL("关键", 4);

        private final String description;
        private final int level;

        EventSeverity(String description, int level) {
            this.description = description;
            this.level = level;
        }
    }

    /**
     * 攻击者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackerInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 攻击者IP
         */
        private String attackerIp;

        /**
         * 攻击者地理位置
         */
        private String geolocation;

        /**
         * 攻击者组织
         */
        private String organization;

        /**
         * 攻击者ASN
         */
        private String asn;

        /**
         * 威胁组织名称
         */
        private String threatGroup;

        /**
         * 攻击活动名称
         */
        private String campaignName;

        /**
         * 攻击者特征
         */
        private Map<String, String> characteristics;
    }

    /**
     * 受害者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VictimInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 受害者IP
         */
        private String victimIp;

        /**
         * 主机名
         */
        private String hostname;

        /**
         * 操作系统
         */
        private String operatingSystem;

        /**
         * 资产类型
         */
        private String assetType;

        /**
         * 业务重要性
         */
        private String businessCriticality;

        /**
         * 所属部门
         */
        private String department;

        /**
         * 资产标签
         */
        private List<String> assetTags;
    }

    /**
     * 威胁指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatIndicator implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 指标类型
         */
        private IndicatorType type;

        /**
         * 指标值
         */
        private String value;

        /**
         * 指标描述
         */
        private String description;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 首次发现时间
         */
        private LocalDateTime firstSeen;

        /**
         * 最后发现时间
         */
        private LocalDateTime lastSeen;

        /**
         * 指标类型枚举
         */
        @Getter
        public enum IndicatorType {
            IP_ADDRESS("IP地址"),
            DOMAIN("域名"),
            URL("URL"),
            FILE_HASH("文件哈希"),
            EMAIL("邮箱地址"),
            REGISTRY_KEY("注册表键"),
            MUTEX("互斥量"),
            USER_AGENT("用户代理"),
            CERTIFICATE("证书"),
            OTHER("其他");

            private final String description;

            IndicatorType(String description) {
                this.description = description;
            }
        }
    }

    /**
     * 文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 文件路径
         */
        private String filePath;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 文件大小
         */
        private Long fileSize;

        /**
         * MD5哈希
         */
        private String md5Hash;

        /**
         * SHA1哈希
         */
        private String sha1Hash;

        /**
         * SHA256哈希
         */
        private String sha256Hash;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 是否恶意
         */
        private Boolean isMalicious;

        /**
         * 检测结果
         */
        private String detectionResult;
    }

    /**
     * 网络连接信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NetworkConnection implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 源IP
         */
        private String sourceIp;

        /**
         * 目标IP
         */
        private String destinationIp;

        /**
         * 源端口
         */
        private Integer sourcePort;

        /**
         * 目标端口
         */
        private Integer destinationPort;

        /**
         * 协议
         */
        private String protocol;

        /**
         * 连接状态
         */
        private String connectionState;

        /**
         * 数据传输量
         */
        private Long bytesTransferred;

        /**
         * 连接持续时间
         */
        private Long durationSeconds;

        /**
         * 连接时间
         */
        private LocalDateTime connectionTime;
    }

    /**
     * 获取事件的风险评分
     * 
     * @return 风险评分（0-100）
     */
    public int getRiskScore() {
        int baseScore = 0;

        // 基于Kill Chain阶段的评分
        if (killChainStage != null) {
            baseScore += killChainStage.getSeverityLevel() * 5;
        }

        // 基于严重程度的评分
        if (severity != null) {
            baseScore += severity.getLevel() * 10;
        }

        // 基于置信度的评分
        if (confidence != null) {
            baseScore += (int) (confidence * 20);
        }

        // 基于威胁指标数量的评分
        if (indicators != null) {
            baseScore += Math.min(indicators.size() * 5, 20);
        }

        return Math.min(baseScore, 100);
    }

    /**
     * 判断是否为关键事件
     * 
     * @return 是否为关键事件
     */
    public boolean isCriticalEvent() {
        return severity == EventSeverity.CRITICAL ||
                (killChainStage != null && killChainStage.isLateStage()) ||
                (confidence != null && confidence >= 0.9);
    }

    /**
     * 获取事件的简短描述
     * 
     * @return 简短描述
     */
    public String getBriefDescription() {
        StringBuilder desc = new StringBuilder();

        if (killChainStage != null) {
            desc.append("[").append(killChainStage.getChineseName()).append("] ");
        }

        if (eventName != null) {
            desc.append(eventName);
        } else if (threatType != null) {
            desc.append(threatType).append("威胁");
        } else {
            desc.append("安全事件");
        }

        if (sourceIp != null && targetIp != null) {
            desc.append(" (").append(sourceIp).append(" -> ").append(targetIp).append(")");
        }

        return desc.toString();
    }

    /**
     * 添加威胁指标
     * 
     * @param indicator 威胁指标
     */
    public void addThreatIndicator(ThreatIndicator indicator) {
        if (indicators == null) {
            indicators = new java.util.ArrayList<>();
        }
        indicators.add(indicator);
    }

    /**
     * 添加相关文件
     * 
     * @param fileInfo 文件信息
     */
    public void addRelatedFile(FileInfo fileInfo) {
        if (relatedFiles == null) {
            relatedFiles = new java.util.ArrayList<>();
        }
        relatedFiles.add(fileInfo);
    }

    /**
     * 添加网络连接
     * 
     * @param connection 网络连接
     */
    public void addNetworkConnection(NetworkConnection connection) {
        if (networkConnections == null) {
            networkConnections = new java.util.ArrayList<>();
        }
        networkConnections.add(connection);
    }

    /**
     * 添加标签
     * 
     * @param tag 标签
     */
    public void addTag(String tag) {
        if (tags == null) {
            tags = new java.util.ArrayList<>();
        }
        if (!tags.contains(tag)) {
            tags.add(tag);
        }
    }
}
