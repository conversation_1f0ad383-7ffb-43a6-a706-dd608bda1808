package com.geeksec.threatdetector.formatting.knowledge;

import com.geeksec.common.knowledge.KnowledgeBaseClient;
import com.geeksec.threatdetector.formatting.model.ThreatInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于知识库服务的威胁信息提供者
 * 
 * 替代硬编码的威胁知识库，从知识库服务动态获取威胁信息
 * 
 * <AUTHOR>
 */
@Slf4j
public class KnowledgeBaseThreatInfoProvider implements Serializable {

    private static final long serialVersionUID = 1L;

    private final KnowledgeBaseClient knowledgeBaseClient;
    
    // 本地缓存
    private final Map<String, ThreatInfo> threatInfoCache = new ConcurrentHashMap<>();
    private volatile long lastUpdateTime = 0;
    private static final long CACHE_REFRESH_INTERVAL = 30 * 60 * 1000; // 30分钟

    public KnowledgeBaseThreatInfoProvider(String knowledgeBaseUrl) {
        this.knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);
        log.info("威胁信息提供者初始化完成，知识库服务: {}", knowledgeBaseUrl);
    }

    /**
     * 根据威胁类型获取威胁信息
     */
    public ThreatInfo getThreatInfo(String threatType) {
        refreshCacheIfNeeded();
        
        ThreatInfo threatInfo = threatInfoCache.get(threatType);
        if (threatInfo == null) {
            // 尝试从知识库获取
            threatInfo = fetchThreatInfoFromKnowledgeBase(threatType);
            if (threatInfo != null) {
                threatInfoCache.put(threatType, threatInfo);
            }
        }
        
        return threatInfo;
    }

    /**
     * 获取所有威胁信息
     */
    public Map<String, ThreatInfo> getAllThreatInfo() {
        refreshCacheIfNeeded();
        return new ConcurrentHashMap<>(threatInfoCache);
    }

    /**
     * 根据威胁名称搜索威胁信息
     */
    public ThreatInfo searchByThreatName(String threatName) {
        try {
            Map<String, Object> result = knowledgeBaseClient.getThreatIntelligenceByName(threatName);
            if (result != null) {
                return convertToThreatInfo(result);
            }
        } catch (Exception e) {
            log.warn("从知识库搜索威胁信息失败: {}", threatName, e);
        }
        return null;
    }

    /**
     * 获取高置信度威胁信息
     */
    public List<ThreatInfo> getHighConfidenceThreatInfo(int threshold) {
        try {
            List<Map<String, Object>> results = knowledgeBaseClient.getHighConfidenceThreatIntelligence(threshold);
            return results.stream()
                    .map(this::convertToThreatInfo)
                    .filter(info -> info != null)
                    .toList();
        } catch (Exception e) {
            log.warn("获取高置信度威胁信息失败", e);
            return List.of();
        }
    }

    /**
     * 刷新缓存（如果需要）
     */
    private void refreshCacheIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime > CACHE_REFRESH_INTERVAL) {
            refreshCache();
            lastUpdateTime = currentTime;
        }
    }

    /**
     * 刷新缓存
     */
    private void refreshCache() {
        try {
            // 获取所有威胁类型的威胁信息
            String[] threatTypes = {"MALWARE", "APT", "BOTNET", "PHISHING", "C2", "MINING", 
                                   "RANSOMWARE", "TROJAN", "BACKDOOR", "EXPLOIT", "OTHER"};
            
            for (String threatType : threatTypes) {
                List<Map<String, Object>> results = knowledgeBaseClient.getThreatIntelligenceByType(threatType);
                for (Map<String, Object> result : results) {
                    ThreatInfo threatInfo = convertToThreatInfo(result);
                    if (threatInfo != null) {
                        threatInfoCache.put(threatInfo.getThreatType(), threatInfo);
                    }
                }
            }
            
            log.info("威胁信息缓存刷新完成，共 {} 条记录", threatInfoCache.size());
        } catch (Exception e) {
            log.error("刷新威胁信息缓存失败", e);
        }
    }

    /**
     * 从知识库获取威胁信息
     */
    private ThreatInfo fetchThreatInfoFromKnowledgeBase(String threatType) {
        try {
            List<Map<String, Object>> results = knowledgeBaseClient.getThreatIntelligenceByType(threatType);
            if (!results.isEmpty()) {
                return convertToThreatInfo(results.get(0));
            }
        } catch (Exception e) {
            log.warn("从知识库获取威胁信息失败: {}", threatType, e);
        }
        return null;
    }

    /**
     * 将知识库返回的Map转换为ThreatInfo对象
     */
    private ThreatInfo convertToThreatInfo(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        
        try {
            return ThreatInfo.builder()
                    .threatType(getStringValue(data, "threatType"))
                    .threatName(getStringValue(data, "threatName"))
                    .description(getStringValue(data, "description"))
                    .category(getStringValue(data, "category"))
                    .severity(getStringValue(data, "severity"))
                    .attackVectors(getListValue(data, "attackVectors"))
                    .impactScope(getStringValue(data, "impactScope"))
                    .detectionPrinciple(getStringValue(data, "detectionPrinciple"))
                    .technicalBackground(getStringValue(data, "technicalBackground"))
                    .relatedCVEs(getListValue(data, "relatedCves"))
                    .updateTime(LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            log.warn("转换威胁信息失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取列表值
     */
    @SuppressWarnings("unchecked")
    private List<String> getListValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return List.of();
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("cacheSize", threatInfoCache.size());
        stats.put("lastUpdateTime", lastUpdateTime);
        stats.put("cacheRefreshInterval", CACHE_REFRESH_INTERVAL);
        return stats;
    }

    /**
     * 清理资源
     */
    public void close() {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        threatInfoCache.clear();
        log.info("威胁信息提供者已关闭");
    }
}
