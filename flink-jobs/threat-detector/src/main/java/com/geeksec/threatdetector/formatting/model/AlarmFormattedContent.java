package com.geeksec.threatdetector.formatting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警格式化内容模型
 * 包含告警的详细描述、处理建议、原理说明等格式化信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmFormattedContent implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 格式化策略
     */
    private FormattingStrategy strategy;
    
    /**
     * 格式化语言
     */
    private String language;
    
    /**
     * 告警摘要
     */
    private AlarmSummary summary;
    
    /**
     * 告警原因分析
     */
    private AlarmReasonAnalysis reasonAnalysis;
    
    /**
     * 处理建议
     */
    private HandlingSuggestions handlingSuggestions;
    
    /**
     * 原理说明
     */
    private PrincipleExplanation principleExplanation;
    
    /**
     * 影响分析
     */
    private ImpactAnalysis impactAnalysis;
    
    /**
     * 相关信息
     */
    private RelatedInformation relatedInformation;
    
    /**
     * 格式化时间
     */
    private LocalDateTime formattedTime;
    
    /**
     * 格式化版本
     */
    private String formattingVersion;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;
    
    /**
     * 格式化策略枚举
     */
    @lombok.Getter
    public enum FormattingStrategy {
        /** 简洁格式 - 适用于短信、即时通知 */
        BRIEF("brief", "简洁格式"),
        /** 标准格式 - 适用于一般通知 */
        STANDARD("standard", "标准格式"),
        /** 详细格式 - 适用于邮件、报告 */
        DETAILED("detailed", "详细格式"),
        /** 技术格式 - 适用于技术人员 */
        TECHNICAL("technical", "技术格式"),
        /** 管理格式 - 适用于管理层 */
        EXECUTIVE("executive", "管理格式");
        
        private final String code;
        private final String description;
        
        FormattingStrategy(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    
    /**
     * 告警摘要
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmSummary implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 简短描述
         */
        private String briefDescription;
        
        /**
         * 详细描述
         */
        private String detailedDescription;
        
        /**
         * 关键信息
         */
        private Map<String, String> keyInformation;
        
        /**
         * 严重程度描述
         */
        private String severityDescription;
        
        /**
         * 紧急程度描述
         */
        private String urgencyDescription;
    }
    
    /**
     * 告警原因分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmReasonAnalysis implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 检测原因列表
         */
        private List<DetectionReason> detectionReasons;
        
        /**
         * 技术分析
         */
        private String technicalAnalysis;
        
        /**
         * 行为分析
         */
        private String behaviorAnalysis;
        
        /**
         * 模式匹配结果
         */
        private List<PatternMatch> patternMatches;
        
        /**
         * 置信度分析
         */
        private String confidenceAnalysis;
    }
    
    /**
     * 检测原因
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectionReason implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 原因类型
         */
        private String reasonType;
        
        /**
         * 原因描述
         */
        private String description;
        
        /**
         * 检测到的特征
         */
        private String detectedFeature;
        
        /**
         * 实际值
         */
        private String actualValue;
        
        /**
         * 期望值
         */
        private String expectedValue;
        
        /**
         * 重要性级别
         */
        private ImportanceLevel importance;
    }
    
    /**
     * 模式匹配结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternMatch implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 模式名称
         */
        private String patternName;
        
        /**
         * 匹配内容
         */
        private String matchedContent;
        
        /**
         * 匹配位置
         */
        private String matchLocation;
        
        /**
         * 匹配置信度
         */
        private Double confidence;
    }
    
    /**
     * 处理建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HandlingSuggestions implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 立即处理建议
         */
        private List<ActionSuggestion> immediateActions;
        
        /**
         * 短期处理建议
         */
        private List<ActionSuggestion> shortTermActions;
        
        /**
         * 长期处理建议
         */
        private List<ActionSuggestion> longTermActions;
        
        /**
         * 预防措施
         */
        private List<ActionSuggestion> preventiveMeasures;
        
        /**
         * 优先级排序
         */
        private String priorityGuidance;
    }
    
    /**
     * 处理建议项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionSuggestion implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 建议标题
         */
        private String title;
        
        /**
         * 建议描述
         */
        private String description;
        
        /**
         * 执行步骤
         */
        private List<String> steps;
        
        /**
         * 优先级
         */
        private Priority priority;
        
        /**
         * 预估时间
         */
        private String estimatedTime;
        
        /**
         * 所需技能
         */
        private String requiredSkills;
        
        /**
         * 风险评估
         */
        private String riskAssessment;
    }
    
    /**
     * 原理说明
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrincipleExplanation implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 检测原理
         */
        private String detectionPrinciple;
        
        /**
         * 技术背景
         */
        private String technicalBackground;
        
        /**
         * 攻击原理
         */
        private String attackPrinciple;
        
        /**
         * 检测方法
         */
        private String detectionMethod;
        
        /**
         * 相关技术
         */
        private List<String> relatedTechnologies;
        
        /**
         * 参考资料
         */
        private List<Reference> references;
    }
    
    /**
     * 影响分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpactAnalysis implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 业务影响
         */
        private String businessImpact;
        
        /**
         * 技术影响
         */
        private String technicalImpact;
        
        /**
         * 安全影响
         */
        private String securityImpact;
        
        /**
         * 合规影响
         */
        private String complianceImpact;
        
        /**
         * 影响范围
         */
        private String impactScope;
        
        /**
         * 潜在损失
         */
        private String potentialLoss;
    }
    
    /**
     * 相关信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedInformation implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 相关告警
         */
        private List<String> relatedAlarms;
        
        /**
         * 历史事件
         */
        private List<String> historicalEvents;
        
        /**
         * 威胁情报
         */
        private List<ThreatIntelligence> threatIntelligence;
        
        /**
         * 相关资产
         */
        private List<String> relatedAssets;
        
        /**
         * 外部链接
         */
        private List<Reference> externalLinks;
    }
    
    /**
     * 威胁情报
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatIntelligence implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 情报类型
         */
        private String type;
        
        /**
         * 情报内容
         */
        private String content;
        
        /**
         * 情报来源
         */
        private String source;
        
        /**
         * 可信度
         */
        private String reliability;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
    
    /**
     * 参考资料
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Reference implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 标题
         */
        private String title;
        
        /**
         * URL
         */
        private String url;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 类型
         */
        private String type;
    }
    
    /**
     * 重要性级别枚举
     */
    @lombok.Getter
    public enum ImportanceLevel {
        LOW("低", 1),
        MEDIUM("中", 2),
        HIGH("高", 3),
        CRITICAL("关键", 4);
        
        private final String description;
        private final int level;
        
        ImportanceLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }
    }
    
    /**
     * 优先级枚举
     */
    @lombok.Getter
    public enum Priority {
        LOW("低优先级", 1),
        MEDIUM("中优先级", 2),
        HIGH("高优先级", 3),
        URGENT("紧急", 4);
        
        private final String description;
        private final int level;
        
        Priority(String description, int level) {
            this.description = description;
            this.level = level;
        }
    }
    
    /**
     * 获取格式化内容的文本表示
     * 
     * @return 文本内容
     */
    public String toText() {
        StringBuilder sb = new StringBuilder();
        
        if (summary != null) {
            sb.append("告警摘要: ").append(summary.getBriefDescription()).append("\n");
        }
        
        if (reasonAnalysis != null && reasonAnalysis.getDetectionReasons() != null) {
            sb.append("检测原因:\n");
            for (DetectionReason reason : reasonAnalysis.getDetectionReasons()) {
                sb.append("- ").append(reason.getDescription()).append("\n");
            }
        }
        
        if (handlingSuggestions != null && handlingSuggestions.getImmediateActions() != null) {
            sb.append("处理建议:\n");
            for (ActionSuggestion action : handlingSuggestions.getImmediateActions()) {
                sb.append("- ").append(action.getTitle()).append(": ").append(action.getDescription()).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 检查格式化内容是否完整
     * 
     * @return 是否完整
     */
    public boolean isComplete() {
        return alarmId != null &&
               strategy != null &&
               summary != null &&
               reasonAnalysis != null &&
               handlingSuggestions != null;
    }
}
