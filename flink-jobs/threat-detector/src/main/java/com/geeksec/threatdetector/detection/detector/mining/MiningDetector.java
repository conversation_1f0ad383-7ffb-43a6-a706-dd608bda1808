package com.geeksec.threatdetector.detection.detector.mining;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.geeksec.threatdetector.detection.DetectorType;
import com.geeksec.threatdetector.detection.ThreatDetector;
import com.geeksec.threatdetector.model.detection.DetectionResult;
import com.geeksec.threatdetector.model.input.DnsInfo;
import com.geeksec.threatdetector.model.input.NetworkEvent;
import com.geeksec.threatdetector.model.input.SslInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 挖矿检测器
 * 检测各种挖矿病毒和挖矿连接行为
 *
 * <AUTHOR>
 */
@Slf4j
public class MiningDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 已知挖矿域名黑名单
    private static final Set<String> MINING_DOMAINS = new HashSet<>();
    
    // 已知挖矿IP地址黑名单
    private static final Set<String> MINING_IPS = new HashSet<>();
    
    // 挖矿相关的SSL指纹
    private static final Set<String> MINING_SSL_FINGERPRINTS = new HashSet<>();
    
    // 挖矿相关的域名关键词
    private static final Set<String> MINING_KEYWORDS = new HashSet<>();

    static {
        initializeMiningDomains();
        initializeMiningIps();
        initializeMiningFingerprints();
        initializeMiningKeywords();
    }

    private static void initializeMiningDomains() {
        // 常见挖矿池域名
        MINING_DOMAINS.addAll(Arrays.asList(
                "pool.minergate.com",
                "xmr.pool.minergate.com",
                "etc.pool.minergate.com",
                "stratum.antpool.com",
                "stratum-eth.antpool.com",
                "eth-us-east1.nanopool.org",
                "eth-us-west1.nanopool.org",
                "xmr-us-east1.nanopool.org",
                "xmr-us-west1.nanopool.org"
        ));
        
        log.info("初始化挖矿域名黑名单，共 {} 个域名", MINING_DOMAINS.size());
    }

    private static void initializeMiningIps() {
        // 常见挖矿池IP地址
        MINING_IPS.addAll(Arrays.asList(
                "*************",
                "*************",
                "*************",
                "*************"
        ));
        
        log.info("初始化挖矿IP黑名单，共 {} 个IP", MINING_IPS.size());
    }

    private static void initializeMiningFingerprints() {
        // 挖矿相关的SSL指纹（基于旧版本代码中的指纹类型）
        MINING_SSL_FINGERPRINTS.addAll(Arrays.asList(
                "14010", // 根据旧版本代码中的挖矿指纹类型
                "mining_ssl_fingerprint_1",
                "mining_ssl_fingerprint_2"
        ));

        log.info("初始化挖矿SSL指纹库，共 {} 个指纹", MINING_SSL_FINGERPRINTS.size());
    }

    private static void initializeMiningKeywords() {
        // 挖矿相关的域名关键词
        MINING_KEYWORDS.addAll(Arrays.asList(
                "mine", "mining", "pool", "miner", "crypto", "bitcoin", "btc",
                "ethereum", "eth", "monero", "xmr", "zcash", "zec", "dash",
                "litecoin", "ltc", "hash", "donate"
        ));
        
        log.info("初始化挖矿关键词库，共 {} 个关键词", MINING_KEYWORDS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.MINING;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 1. DNS挖矿域名检测
            if (event.getEventType() == NetworkEvent.EventType.DNS && event.getDnsInfo() != null) {
                DetectionResult dnsResult = detectMiningDns(event);
                if (dnsResult != null) {
                    results.add(dnsResult);
                }
            }

            // 2. SSL挖矿指纹检测
            if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
                DetectionResult sslResult = detectMiningSsl(event);
                if (sslResult != null) {
                    results.add(sslResult);
                }
            }

            // 3. 挖矿IP地址检测
            DetectionResult ipResult = detectMiningIp(event);
            if (ipResult != null) {
                results.add(ipResult);
            }

        } catch (Exception e) {
            log.error("挖矿检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测DNS挖矿域名
     */
    private DetectionResult detectMiningDns(NetworkEvent event) {
        DnsInfo dnsInfo = event.getDnsInfo();
        String queryName = dnsInfo.getQueryName();
        
        if (queryName == null) {
            return null;
        }

        // 检查是否为已知挖矿域名
        if (MINING_DOMAINS.contains(queryName.toLowerCase())) {
            return createDetectionResult(event, "MINING_DOMAIN_EXACT", 
                    "挖矿域名访问",
                    DetectionResult.ThreatLevel.HIGH, 0.95,
                    "访问已知挖矿域名: " + queryName);
        }

        // 检查域名是否包含挖矿关键词
        String lowerQueryName = queryName.toLowerCase();
        for (String keyword : MINING_KEYWORDS) {
            if (lowerQueryName.contains(keyword)) {
                return createDetectionResult(event, "MINING_DOMAIN_KEYWORD", 
                        "疑似挖矿域名访问",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        String.format("域名包含挖矿关键词 '%s': %s", keyword, queryName));
            }
        }

        return null;
    }

    /**
     * 检测SSL挖矿指纹
     */
    private DetectionResult detectMiningSsl(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        
        // 检查SSL指纹
        if (sslInfo.getJa3Hash() != null && MINING_SSL_FINGERPRINTS.contains(sslInfo.getJa3Hash())) {
            return createDetectionResult(event, "MINING_SSL_FINGERPRINT", 
                    "挖矿SSL指纹匹配",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "匹配的挖矿SSL指纹: " + sslInfo.getJa3Hash());
        }

        // 检查服务器名称是否为挖矿域名
        if (sslInfo.getServerName() != null && MINING_DOMAINS.contains(sslInfo.getServerName().toLowerCase())) {
            return createDetectionResult(event, "MINING_SSL_DOMAIN", 
                    "SSL连接挖矿域名",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "SSL连接到挖矿域名: " + sslInfo.getServerName());
        }

        return null;
    }

    /**
     * 检测挖矿IP地址
     */
    private DetectionResult detectMiningIp(NetworkEvent event) {
        String dstIp = event.getDstIp();
        
        if (dstIp != null && MINING_IPS.contains(dstIp)) {
            return createDetectionResult(event, "MINING_IP_BLACKLIST", 
                    "挖矿IP地址通信",
                    DetectionResult.ThreatLevel.HIGH, 0.95,
                    "连接到已知挖矿IP: " + dstIp);
        }

        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .timestamp(event.getTimestamp())
                .build();
    }
}
