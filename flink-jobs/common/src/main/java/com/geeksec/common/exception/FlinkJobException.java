package com.geeksec.common.exception;

import lombok.Getter;

/**
 * Flink作业异常基类
 * 用于Flink作业中的异常处理
 * 
 * <AUTHOR>
 */
@Getter
public class FlinkJobException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private Object[] args;
    
    public FlinkJobException() {
        super();
    }
    
    public FlinkJobException(String message) {
        super(message);
    }
    
    public FlinkJobException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public FlinkJobException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    public FlinkJobException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public FlinkJobException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public FlinkJobException(Throwable cause) {
        super(cause);
    }
    
    public Object[] getArgs() {
        return args != null ? args.clone() : null;
    }
}
