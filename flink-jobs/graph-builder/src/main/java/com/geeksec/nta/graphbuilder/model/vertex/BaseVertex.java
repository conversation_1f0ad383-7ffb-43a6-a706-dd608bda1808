package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 基础顶点类
 * 所有顶点实体的基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public abstract class BaseVertex implements Serializable {
    private static final long serialVersionUID = 1L;

    // Common fields that might be used by subclasses
    protected Integer threatScore;
    protected Integer trustScore;
    protected String remark;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    public abstract String getVertexId();

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    public abstract VertexTag getVertexTag();

    // Lombok 会自动生成 getter/setter、toString、equals 和 hashCode 方法
}
