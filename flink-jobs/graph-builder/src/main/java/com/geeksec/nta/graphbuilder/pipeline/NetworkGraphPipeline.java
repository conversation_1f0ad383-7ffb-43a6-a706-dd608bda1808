package com.geeksec.nta.graphbuilder.pipeline;


import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.graphbuilder.pipeline.builder.PipelineBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * 网络图构建流水线
 * 将网络流量数据从Kafka提取并构建到Nebula Graph中
 *
 * <AUTHOR> Team
 */
@Slf4j
public class NetworkGraphPipeline {


    /**
     * 主方法
     * 构建和执行图数据处理流水线
     *
     * @param args 命令行参数
     * @throws Exception 如果执行失败
     */
    public static void main(String[] args) throws Exception {
        // 获取配置
        ParameterTool config = ConfigurationManager.getConfig();
        log.info("Starting NetworkGraphPipeline with configuration");

        // 设置flink相关配置
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 注册配置到全局环境
        env.getConfig().setGlobalJobParameters(config);

        // 设置链式任务
        env.disableOperatorChaining();

        // 使用PipelineBuilder构建流水线
        PipelineBuilder pipelineBuilder = new PipelineBuilder(env);
        pipelineBuilder.build();

        log.info("Start ETL Task From Kafka To Nebula, current time ---> {}",
                TimeUtils.formatCurrentTime());

        // 执行流水线
        pipelineBuilder.execute("GSF04-网络图数据构建");
    }
}
