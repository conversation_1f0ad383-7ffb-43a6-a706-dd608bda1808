package com.geeksec.flink.cert.serializer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.flink.cert.model.CertLabelUpdate;
import org.apache.doris.flink.sink.writer.serializer.DorisRecordSerializer;
import org.apache.flink.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 证书标签更新序列化器
 * 
 * 功能：
 * 1. 将 CertLabelUpdate 序列化为 Doris Stream Load 格式
 * 2. 生成 UPDATE SQL 语句
 * 3. 处理标签数组的格式转换
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class CertLabelUpdateSerializer implements DorisRecordSerializer<CertLabelUpdate> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String serialize(CertLabelUpdate record) {
        try {
            // 构建更新的JSON数据
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("der_sha1", record.getCertHash()); // 使用 der_sha1 作为主键
            
            // 处理标签数组
            if (record.getLabels() != null && !record.getLabels().isEmpty()) {
                // 将标签数组转换为Doris ARRAY格式
                String labelsArray = "[" + record.getLabels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")) + "]";
                updateData.put("labels", labelsArray);
            } else {
                // 空标签数组
                updateData.put("labels", "[]");
            }
            
            // 添加更新时间戳
            updateData.put("labels_updated_at", record.getUpdateTimestamp());
            
            // 序列化为JSON字符串
            String jsonString = objectMapper.writeValueAsString(updateData);
            
            log.debug("序列化证书标签更新: certHash={}, labels={}", 
                    record.getCertHash(), record.getLabels());
            
            return jsonString;
            
        } catch (Exception e) {
            log.error("序列化证书标签更新失败: {}", record, e);
            return null;
        }
    }
    
    /**
     * 生成 Doris Stream Load 的 UPDATE 语句
     */
    public String generateUpdateSql(CertLabelUpdate record) {
        if (StringUtils.isNullOrWhitespaceOnly(record.getCertHash())) {
            log.warn("证书哈希为空，跳过生成UPDATE语句");
            return null;
        }
        
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE dim_cert SET ");
            
            // 更新标签字段
            if (record.getLabels() != null && !record.getLabels().isEmpty()) {
                String labelsArray = "[" + record.getLabels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")) + "]";
                sql.append("labels = ").append(labelsArray);
            } else {
                sql.append("labels = []");
            }
            
            // 添加更新时间戳
            sql.append(", labels_updated_at = ").append(record.getUpdateTimestamp());
            
            // WHERE 条件 - 使用 der_sha1 作为主键
            sql.append(" WHERE der_sha1 = '").append(record.getCertHash()).append("'");
            
            String updateSql = sql.toString();
            log.debug("生成UPDATE SQL: {}", updateSql);
            
            return updateSql;
            
        } catch (Exception e) {
            log.error("生成UPDATE SQL失败: {}", record, e);
            return null;
        }
    }
    
    /**
     * 验证记录的有效性
     */
    private boolean isValidRecord(CertLabelUpdate record) {
        if (record == null) {
            log.warn("记录为null");
            return false;
        }
        
        if (StringUtils.isNullOrWhitespaceOnly(record.getCertHash())) {
            log.warn("证书哈希为空或空白");
            return false;
        }
        
        return true;
    }
    
    /**
     * 格式化标签数组为Doris ARRAY格式
     */
    private String formatLabelsArray(CertLabelUpdate record) {
        if (record.getLabels() == null || record.getLabels().isEmpty()) {
            return "[]";
        }
        
        return "[" + record.getLabels().stream()
            .map(String::valueOf)
            .collect(Collectors.joining(",")) + "]";
    }
}
