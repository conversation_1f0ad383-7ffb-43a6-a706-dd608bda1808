package com.geeksec.flink.cert.serializer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.flink.cert.model.CertLabelChange;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书标签变更事件反序列化器
 * 
 * 功能：
 * 1. 解析 Debezium CDC 消息
 * 2. 提取证书标签变更信息
 * 3. 过滤无效消息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Slf4j
public class CertLabelChangeDeserializer implements FlatMapFunction<String, CertLabelChange> {
    
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void flatMap(String value, Collector<CertLabelChange> out) throws Exception {
        try {
            JsonNode rootNode = objectMapper.readTree(value);
            
            // 获取操作类型
            String operation = rootNode.path("op").asText();
            if (operation.isEmpty()) {
                log.debug("跳过无操作类型的消息");
                return;
            }
            
            // 获取表名，确保是 cert_labels 表的变更
            String tableName = rootNode.path("source").path("table").asText();
            if (!"cert_labels".equals(tableName)) {
                log.debug("跳过非 cert_labels 表的变更: {}", tableName);
                return;
            }
            
            // 解析变更数据
            CertLabelChange change = parseChangeEvent(rootNode, operation);
            if (change != null) {
                out.collect(change);
                log.debug("解析证书标签变更事件: {}", change);
            }
            
        } catch (Exception e) {
            log.error("解析证书标签变更消息失败: {}", value, e);
        }
    }
    
    /**
     * 解析变更事件
     */
    private CertLabelChange parseChangeEvent(JsonNode rootNode, String operation) {
        try {
            JsonNode dataNode = null;
            
            // 根据操作类型获取数据节点
            switch (operation.toLowerCase()) {
                case "c": // CREATE/INSERT
                case "u": // UPDATE
                    dataNode = rootNode.path("after");
                    break;
                case "d": // DELETE
                    dataNode = rootNode.path("before");
                    break;
                case "r": // READ (初始快照)
                    dataNode = rootNode.path("after");
                    operation = "c"; // 将快照数据视为插入操作
                    break;
                default:
                    log.debug("跳过不支持的操作类型: {}", operation);
                    return null;
            }
            
            if (dataNode == null || dataNode.isNull()) {
                log.debug("数据节点为空，跳过处理");
                return null;
            }
            
            // 提取字段值
            String certHash = dataNode.path("cert_hash").asText();
            Integer labelId = dataNode.path("label_id").asInt();
            Integer createdBy = dataNode.path("created_by").isNull() ? 
                null : dataNode.path("created_by").asInt();
            
            // 验证必要字段
            if (certHash.isEmpty() || labelId == 0) {
                log.warn("证书哈希或标签ID为空，跳过处理: certHash={}, labelId={}", certHash, labelId);
                return null;
            }
            
            // 创建变更事件
            CertLabelChange change = new CertLabelChange();
            change.setCertHash(certHash);
            change.setLabelId(labelId);
            change.setOperation(mapOperation(operation));
            change.setCreatedBy(createdBy);
            
            // 设置源时间戳
            JsonNode sourceNode = rootNode.path("source");
            if (!sourceNode.isNull()) {
                long sourceTimestamp = sourceNode.path("ts_ms").asLong();
                change.setSourceTimestamp(sourceTimestamp);
            }
            
            return change;
            
        } catch (Exception e) {
            log.error("解析变更事件失败", e);
            return null;
        }
    }
    
    /**
     * 映射操作类型
     */
    private String mapOperation(String debeziumOp) {
        switch (debeziumOp.toLowerCase()) {
            case "c":
                return "INSERT";
            case "u":
                return "UPDATE";
            case "d":
                return "DELETE";
            default:
                return debeziumOp.toUpperCase();
        }
    }
}
