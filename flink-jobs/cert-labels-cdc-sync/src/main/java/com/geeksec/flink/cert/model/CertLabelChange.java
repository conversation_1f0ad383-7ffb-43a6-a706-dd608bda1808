package com.geeksec.flink.cert.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 证书标签变更事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CertLabelChange implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @JsonProperty("cert_hash")
    private String certHash;
    
    @JsonProperty("label_id")
    private Integer labelId;
    
    @JsonProperty("operation")
    private String operation; // INSERT, UPDATE, DELETE
    
    @JsonProperty("created_by")
    private Integer createdBy;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("source_timestamp")
    private Long sourceTimestamp;
    
    {
        this.timestamp = Instant.now().toEpochMilli();
    }
    
    public CertLabelChange(String certHash, Integer labelId, String operation) {
        this();
        this.certHash = certHash;
        this.labelId = labelId;
        this.operation = operation;
    }
}
