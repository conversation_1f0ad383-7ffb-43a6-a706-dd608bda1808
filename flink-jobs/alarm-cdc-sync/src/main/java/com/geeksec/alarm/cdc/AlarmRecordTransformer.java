package com.geeksec.alarm.cdc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.api.common.functions.MapFunction;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 告警记录数据转换器
 * 将PostgreSQL的alarm_records数据转换为Doris ods_alarm_log格式
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class AlarmRecordTransformer implements MapFunction<String, String> {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public String map(String value) throws Exception {
        try {
            JsonNode cdcRecord = objectMapper.readTree(value);
            String operation = cdcRecord.get("op").asText();
            
            // 对于删除操作，我们可能需要特殊处理或者忽略
            if ("d".equals(operation)) {
                log.debug("忽略删除操作: {}", value);
                return null;
            }
            
            // 获取变更后的数据
            JsonNode after = cdcRecord.get("after");
            if (after == null) {
                log.warn("CDC记录中没有after字段: {}", value);
                return null;
            }
            
            // 创建目标记录
            ObjectNode targetRecord = objectMapper.createObjectNode();
            
            // 生成告警ID（使用MD5哈希）
            String alarmId = generateAlarmId(after);
            targetRecord.put("alarm_id", alarmId);
            
            // 映射基础字段
            mapBasicFields(after, targetRecord);
            
            // 映射时间字段
            mapTimeFields(after, targetRecord);
            
            // 映射网络字段
            mapNetworkFields(after, targetRecord);
            
            // 映射扩展字段
            mapExtendedFields(after, targetRecord);
            
            // 设置默认值
            setDefaultValues(targetRecord);
            
            return objectMapper.writeValueAsString(targetRecord);
            
        } catch (Exception e) {
            log.error("转换告警记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 生成告警ID
     */
    private String generateAlarmId(JsonNode record) throws Exception {
        // 使用id + triggered_at生成唯一的告警ID
        String source = record.get("id").asText() + "_" + record.get("triggered_at").asText();
        
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(source.getBytes());
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString();
    }
    
    /**
     * 映射基础字段
     */
    private void mapBasicFields(JsonNode source, ObjectNode target) {
        // 直接映射的字段
        copyField(source, target, "alarm_title", "alarm_name");
        copyField(source, target, "alarm_content", "alarm_desc");
        copyField(source, target, "severity", "attack_level");
        copyField(source, target, "status", "alarm_status");
        copyField(source, target, "alarm_type", "alarm_type");
        copyField(source, target, "threat_type", "threat_type");
        copyField(source, target, "attack_stage", "attack_stage");
        copyField(source, target, "resolved_by", "resolved_by");
        
        // 数值字段
        if (source.has("rule_id") && !source.get("rule_id").isNull()) {
            target.put("rule_id", source.get("rule_id").asLong());
        }
        
        if (source.has("confidence") && !source.get("confidence").isNull()) {
            target.put("confidence", source.get("confidence").asDouble());
        }
        
        // JSON字段
        if (source.has("source_data") && !source.get("source_data").isNull()) {
            target.set("alarm_detail", source.get("source_data"));
        }
    }
    
    /**
     * 映射时间字段
     */
    private void mapTimeFields(JsonNode source, ObjectNode target) {
        // 事件时间使用triggered_at
        copyTimestampField(source, target, "triggered_at", "event_time");
        
        // 创建时间和更新时间
        copyTimestampField(source, target, "created_at", "alarm_create_time");
        copyTimestampField(source, target, "updated_at", "alarm_update_time");
        copyTimestampField(source, target, "resolved_at", "resolved_at");
    }
    
    /**
     * 映射网络字段
     */
    private void mapNetworkFields(JsonNode source, ObjectNode target) {
        copyField(source, target, "src_ip", "src_ip");
        copyField(source, target, "dst_ip", "dst_ip");
        copyField(source, target, "protocol", "protocol");
        
        if (source.has("src_port") && !source.get("src_port").isNull()) {
            target.put("src_port", source.get("src_port").asInt());
        }
        
        if (source.has("dst_port") && !source.get("dst_port").isNull()) {
            target.put("dst_port", source.get("dst_port").asInt());
        }
    }
    
    /**
     * 映射扩展字段
     */
    private void mapExtendedFields(JsonNode source, ObjectNode target) {
        copyField(source, target, "session_id", "session_id");
        copyField(source, target, "task_id", "task_id");
        copyField(source, target, "data_source", "data_source");
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues(ObjectNode target) {
        // 设置一些默认值
        if (!target.has("alarm_status") || target.get("alarm_status").isNull()) {
            target.put("alarm_status", "OPEN");
        }
    }
    
    /**
     * 复制字段
     */
    private void copyField(JsonNode source, ObjectNode target, String sourceField, String targetField) {
        if (source.has(sourceField) && !source.get(sourceField).isNull()) {
            target.set(targetField, source.get(sourceField));
        }
    }
    
    /**
     * 复制时间戳字段
     */
    private void copyTimestampField(JsonNode source, ObjectNode target, String sourceField, String targetField) {
        if (source.has(sourceField) && !source.get(sourceField).isNull()) {
            try {
                // PostgreSQL时间戳转换为Doris DATETIME格式
                long timestamp = source.get(sourceField).asLong();
                LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
                target.put(targetField, dateTime.format(dateTimeFormatter));
            } catch (Exception e) {
                log.warn("转换时间字段失败: {} = {}", sourceField, source.get(sourceField).asText());
            }
        }
    }
}
