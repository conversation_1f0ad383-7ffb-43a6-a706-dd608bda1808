package com.geeksec.alarm.cdc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.functions.FilterFunction;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警记录过滤器
 * 过滤出alarm_records表的变更事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class AlarmRecordFilter implements FilterFunction<String> {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean filter(String value) throws Exception {
        try {
            JsonNode jsonNode = objectMapper.readTree(value);
            
            // 检查是否是alarm_records表的变更
            JsonNode source = jsonNode.get("source");
            if (source != null) {
                String table = source.get("table").asText();
                String schema = source.get("schema").asText();
                
                // 只处理public.alarm_records表的变更
                if ("public".equals(schema) && "alarm_records".equals(table)) {
                    String operation = jsonNode.get("op").asText();
                    // 处理插入、更新和删除操作
                    return "c".equals(operation) || "u".equals(operation) || "d".equals(operation);
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("过滤告警记录时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }
}
