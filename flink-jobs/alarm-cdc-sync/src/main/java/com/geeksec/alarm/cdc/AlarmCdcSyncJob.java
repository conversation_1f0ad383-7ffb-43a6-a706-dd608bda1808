package com.geeksec.alarm.cdc;

import com.geeksec.common.config.FlinkConfigManager;
import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.doris.flink.sink.writer.serializer.SimpleStringSerializer;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

import lombok.extern.slf4j.Slf4j;

import java.util.Properties;

/**
 * 告警数据CDC同步作业
 * 将PostgreSQL中的alarm_records表实时同步到Doris的ods_alarm_log表
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class AlarmCdcSyncJob {
    
    public static void main(String[] args) throws Exception {
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置检查点
        env.enableCheckpointing(60000); // 1分钟检查点间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(30000);
        env.getCheckpointConfig().setCheckpointTimeout(600000); // 10分钟超时
        
        // 加载配置
        FlinkConfigManager configManager = new FlinkConfigManager();
        
        // 创建PostgreSQL CDC源
        SourceFunction<String> postgresSource = createPostgreSQLSource(configManager);
        
        // 创建数据流
        DataStreamSource<String> sourceStream = env.addSource(postgresSource, "PostgreSQL CDC Source");
        
        // 数据转换和处理
        sourceStream
            .filter(new AlarmRecordFilter()) // 过滤告警记录变更
            .map(new AlarmRecordTransformer()) // 转换数据格式
            .sinkTo(createDorisSink(configManager)) // 写入Doris
            .name("Doris Sink");
        
        // 执行作业
        log.info("启动告警数据CDC同步作业...");
        env.execute("Alarm CDC Sync Job");
    }
    
    /**
     * 创建PostgreSQL CDC源
     */
    private static SourceFunction<String> createPostgreSQLSource(FlinkConfigManager configManager) {
        Properties debeziumProperties = new Properties();
        debeziumProperties.put("snapshot.mode", "initial"); // 初始快照模式
        debeziumProperties.put("slot.name", "alarm_cdc_slot"); // 复制槽名称
        debeziumProperties.put("decoding.plugin.name", "pgoutput"); // 解码插件
        
        return PostgreSQLSource.<String>builder()
                .hostname(configManager.getPostgreSQLHost())
                .port(configManager.getPostgreSQLPort())
                .database(configManager.getPostgreSQLDatabase())
                .schemaList("public") // 监控的schema
                .tableList("public.alarm_records") // 监控的表
                .username(configManager.getPostgreSQLUsername())
                .password(configManager.getPostgreSQLPassword())
                .deserializer(new JsonDebeziumDeserializationSchema()) // JSON格式反序列化
                .debeziumProperties(debeziumProperties)
                .build();
    }
    
    /**
     * 创建Doris Sink
     */
    private static DorisSink<String> createDorisSink(FlinkConfigManager configManager) {
        DorisOptions.Builder dorisBuilder = DorisOptions.builder()
                .setFenodes(configManager.getDorisFeNodes())
                .setTableIdentifier("nta.ods_alarm_log")
                .setUsername(configManager.getDorisUsername())
                .setPassword(configManager.getDorisPassword());
        
        DorisExecutionOptions.Builder executionBuilder = DorisExecutionOptions.builder()
                .setLabelPrefix("alarm-cdc") // Stream Load标签前缀
                .setStreamLoadProp(Properties.class.cast(new Properties() {{
                    put("format", "json");
                    put("read_json_by_line", "true");
                    put("load_to_single_tablet", "false");
                }}));
        
        return DorisSink.<String>builder()
                .setDorisReadOptions(DorisReadOptions.builder().build())
                .setDorisOptions(dorisBuilder.build())
                .setDorisExecutionOptions(executionBuilder.build())
                .setSerializer(new SimpleStringSerializer()) // 字符串序列化器
                .build();
    }
}
