package com.geeksec.auth.domain.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 权限领域模型（值对象）
 */
@Getter
@EqualsAndHashCode(of = "key")
public class Permission {
    private final PermissionId id;
    private final String key;
    private final String name;
    private final PermissionType type;

    public Permission(PermissionId id, String key, String name, PermissionType type) {
        this.id = id;
        this.key = key;
        this.name = name;
        this.type = type;
    }

    /**
     * 用于权限检查的构造函数
     *
     * @param key 权限键
     */
    public Permission(String key) {
        this.id = null;
        this.key = key;
        this.name = null;
        this.type = null;
    }

    // 值对象
    public static class PermissionId {
        private final Long value;

        public PermissionId(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum PermissionType {
        MENU, OPERATION
    }


}
