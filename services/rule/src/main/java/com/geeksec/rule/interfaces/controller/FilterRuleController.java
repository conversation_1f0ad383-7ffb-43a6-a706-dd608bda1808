package com.geeksec.rule.interfaces.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.rule.application.service.FilterRuleService;
import com.geeksec.rule.interfaces.dto.FilterRuleCreateDTO;
import com.geeksec.rule.interfaces.dto.FilterRuleQueryDTO;
import com.geeksec.rule.interfaces.vo.FilterRuleVO;
import com.geeksec.rule.interfaces.vo.FilterModeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 过滤规则控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/filter-rules")
@RequiredArgsConstructor
@Tag(name = "过滤规则管理", description = "过滤规则的创建、查询、更新、删除等操作")
public class FilterRuleController {

    private final FilterRuleService filterRuleService;

    @Operation(summary = "创建过滤规则", description = "创建新的过滤规则")
    @PostMapping
    public ApiResponse<Void> createFilterRule(
            @Valid @RequestBody FilterRuleCreateDTO createDTO) {
        log.info("创建过滤规则，请求参数：{}", createDTO);
        return filterRuleService.createFilterRule(createDTO);
    }

    @Operation(summary = "更新过滤规则", description = "根据ID更新过滤规则")
    @PutMapping("/{id}")
    public ApiResponse<Void> updateFilterRule(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @Valid @RequestBody FilterRuleCreateDTO updateDTO) {
        log.info("更新过滤规则，ID：{}，请求参数：{}", id, updateDTO);
        return filterRuleService.updateFilterRule(id, updateDTO);
    }

    @Operation(summary = "删除过滤规则", description = "根据ID列表批量删除过滤规则")
    @DeleteMapping
    public ApiResponse<Void> deleteFilterRules(
            @Parameter(description = "规则ID列表") @RequestBody List<Long> ids) {
        log.info("删除过滤规则，ID列表：{}", ids);
        return filterRuleService.deleteFilterRules(ids);
    }

    @Operation(summary = "根据任务ID删除过滤规则", description = "删除指定任务的所有过滤规则")
    @DeleteMapping("/task/{taskId}")
    public ApiResponse<Void> deleteFilterRulesByTaskId(
            @Parameter(description = "任务ID") @PathVariable Integer taskId) {
        log.info("根据任务ID删除过滤规则，任务ID：{}", taskId);
        return filterRuleService.deleteFilterRulesByTaskId(taskId);
    }

    @Operation(summary = "分页查询过滤规则", description = "根据条件分页查询过滤规则列表")
    @PostMapping("/search")
    public ApiResponse<PageResultVo<FilterRuleVO>> getFilterRules(
            @Valid @RequestBody FilterRuleQueryDTO queryDTO) {
        log.info("分页查询过滤规则，查询条件：{}", queryDTO);
        return filterRuleService.getFilterRules(queryDTO);
    }

    @Operation(summary = "修改过滤模式", description = "修改任务的过滤模式（命中留存/丢弃）")
    @PatchMapping("/mode")
    public ApiResponse<Void> modifyFilterMode(
            @Parameter(description = "任务ID") @RequestParam Integer taskId,
            @Parameter(description = "过滤模式") @RequestParam Integer mode) {
        log.info("修改过滤模式，任务ID：{}，模式：{}", taskId, mode);
        return filterRuleService.modifyFilterMode(taskId, mode);
    }

    @Operation(summary = "获取过滤模式", description = "获取任务的过滤模式")
    @GetMapping("/mode/{taskId}")
    public ApiResponse<FilterModeVO> getFilterMode(
            @Parameter(description = "任务ID") @PathVariable Integer taskId) {
        log.info("获取过滤模式，任务ID：{}", taskId);
        return filterRuleService.getFilterMode(taskId);
    }

    @Operation(summary = "批量导入过滤规则", description = "通过CSV文件批量导入过滤规则")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponse<Object> importFilterRules(
            @Parameter(description = "任务ID") @RequestParam Integer taskId,
            @Parameter(description = "CSV文件") @RequestParam("file") MultipartFile file) {
        try {
            log.info("批量导入过滤规则，任务ID：{}，文件名：{}", taskId, file.getOriginalFilename());
            String csvContent = new String(file.getBytes());
            return filterRuleService.importFilterRulesFromCsv(taskId, csvContent);
        } catch (Exception e) {
            log.error("导入过滤规则失败", e);
            return ApiResponse.error("导入失败：" + e.getMessage());
        }
    }

    @Operation(summary = "导出过滤规则模板", description = "下载过滤规则导入模板")
    @GetMapping("/template")
    public ApiResponse<String> exportTemplate() {
        log.info("导出过滤规则模板");
        return filterRuleService.exportFilterRuleTemplate();
    }

    @Operation(summary = "导出过滤规则", description = "导出任务的所有过滤规则")
    @GetMapping("/export/{taskId}")
    public ApiResponse<List<List<String>>> exportFilterRules(
            @Parameter(description = "任务ID") @PathVariable Integer taskId) {
        log.info("导出过滤规则，任务ID：{}", taskId);
        return filterRuleService.getFilterRulesForExport(taskId);
    }
}
