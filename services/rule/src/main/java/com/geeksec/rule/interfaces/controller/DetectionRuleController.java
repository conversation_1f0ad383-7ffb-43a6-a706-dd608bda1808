package com.geeksec.rule.interfaces.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.rule.application.service.DetectionRuleService;
import com.geeksec.rule.interfaces.dto.DetectionRuleCreateDTO;
import com.geeksec.rule.interfaces.vo.DetectionRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 检测规则控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/detection-rules")
@RequiredArgsConstructor
@Tag(name = "检测规则管理", description = "检测规则的创建、查询、更新、删除等操作")
public class DetectionRuleController {

    private final DetectionRuleService detectionRuleService;

    @Operation(summary = "创建检测规则", description = "创建新的检测规则")
    @PostMapping
    public ApiResponse<Void> createDetectionRule(
            @Valid @RequestBody DetectionRuleCreateDTO createDTO) {
        log.info("创建检测规则，请求参数：{}", createDTO);
        return detectionRuleService.createDetectionRule(createDTO);
    }

    @Operation(summary = "更新检测规则", description = "根据ID更新检测规则")
    @PutMapping("/{id}")
    public ApiResponse<Void> updateDetectionRule(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @Valid @RequestBody DetectionRuleCreateDTO updateDTO) {
        log.info("更新检测规则，ID：{}，请求参数：{}", id, updateDTO);
        return detectionRuleService.updateDetectionRule(id, updateDTO);
    }

    @Operation(summary = "删除检测规则", description = "根据ID列表批量删除检测规则")
    @DeleteMapping
    public ApiResponse<Void> deleteDetectionRules(
            @Parameter(description = "规则ID列表") @RequestBody List<Long> ids) {
        log.info("删除检测规则，ID列表：{}", ids);
        return detectionRuleService.deleteDetectionRules(ids);
    }

    @Operation(summary = "根据任务ID删除检测规则", description = "删除指定任务的所有检测规则")
    @DeleteMapping("/task/{taskId}")
    public ApiResponse<Void> deleteDetectionRulesByTaskId(
            @Parameter(description = "任务ID") @PathVariable Integer taskId) {
        log.info("根据任务ID删除检测规则，任务ID：{}", taskId);
        return detectionRuleService.deleteDetectionRulesByTaskId(taskId);
    }

    @Operation(summary = "获取检测规则详情", description = "根据ID获取检测规则详细信息")
    @GetMapping("/{id}")
    public ApiResponse<DetectionRuleVO> getDetectionRule(
            @Parameter(description = "规则ID") @PathVariable Long id) {
        log.info("获取检测规则详情，ID：{}", id);
        return detectionRuleService.getDetectionRule(id);
    }

    @Operation(summary = "分页查询检测规则", description = "根据条件分页查询检测规则列表")
    @GetMapping
    public ApiResponse<PageResultVo<DetectionRuleVO>> getDetectionRules(
            @Parameter(description = "任务ID") @RequestParam Integer taskId,
            @Parameter(description = "规则ID") @RequestParam(required = false) Integer ruleId,
            @Parameter(description = "规则名称") @RequestParam(required = false) String ruleName,
            @Parameter(description = "规则状态") @RequestParam(required = false) String ruleState,
            @Parameter(description = "攻击阶段") @RequestParam(required = false) String attackStage,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "created_time") String orderField,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "DESC") String sortOrder,
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {

        log.info("分页查询检测规则，任务ID：{}，当前页：{}，每页大小：{}", taskId, currentPage, pageSize);
        return detectionRuleService.getDetectionRules(
                taskId, ruleId, ruleName, ruleState, attackStage,
                orderField, sortOrder, currentPage, pageSize
        );
    }

    @Operation(summary = "更新规则状态", description = "更新特征规则的生效/失效状态")
    @PatchMapping("/{id}/state")
    public ApiResponse<Void> updateRuleState(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam String state) {
        log.info("更新规则状态，ID：{}，新状态：{}", id, state);
        return detectionRuleService.updateRuleState(id, state);
    }

    @Operation(summary = "批量导入检测规则", description = "通过CSV文件批量导入检测规则")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponse<Object> importDetectionRules(
            @Parameter(description = "任务ID") @RequestParam Integer taskId,
            @Parameter(description = "CSV文件") @RequestParam("file") MultipartFile file) {
        try {
            log.info("批量导入检测规则，任务ID：{}，文件名：{}", taskId, file.getOriginalFilename());
            String csvContent = new String(file.getBytes());
            return detectionRuleService.importDetectionRulesFromCsv(taskId, csvContent);
        } catch (Exception e) {
            log.error("导入检测规则失败", e);
            return ApiResponse.error("导入失败：" + e.getMessage());
        }
    }

    @Operation(summary = "导出检测规则模板", description = "下载检测规则导入模板")
    @GetMapping("/template")
    public ApiResponse<String> exportTemplate() {
        log.info("导出检测规则模板");
        return detectionRuleService.exportDetectionRuleTemplate();
    }

    @Operation(summary = "获取标签信息", description = "根据规则ID列表获取标签信息（用于会话分析）")
    @PostMapping("/tags")
    public ApiResponse<List<Object>> getTagInfoByRuleIds(
            @Parameter(description = "规则ID列表") @RequestBody List<Integer> ruleIds) {
        log.info("获取标签信息，规则ID列表：{}", ruleIds);
        return detectionRuleService.getTagInfoByRuleIds(ruleIds);
    }
}
