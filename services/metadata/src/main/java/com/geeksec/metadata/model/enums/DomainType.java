package com.geeksec.metadata.model.enums;

import lombok.Getter;

/**
 * 域名类型枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum DomainType {
    
    /**
     * 精确域名
     */
    EXACT(1, "精确域名"),
    
    /**
     * 模糊域名
     */
    FUZZY(2, "模糊域名");
    
    private final int code;
    private final String description;
    
    DomainType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    

    
    /**
     * 根据代码获取域名类型
     * 
     * @param code 类型代码
     * @return 域名类型
     */
    public static DomainType fromCode(int code) {
        for (DomainType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的域名类型代码: " + code);
    }
}
