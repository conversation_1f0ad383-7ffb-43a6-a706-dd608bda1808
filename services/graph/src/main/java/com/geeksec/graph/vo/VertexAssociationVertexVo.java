package com.geeksec.graph.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
*@description: 点关联关系点Vo
*@author: shiwenxu
*@createtime: 2023/8/30 11:28
**/
@Data
@EqualsAndHashCode(of = {"num", "lv", "id", "type", "status"})
public class VertexAssociationVertexVo {

    private String num = "";

    private Long lv = 0L;

    private String main = "";

    private String label = "";

    private String id = "";

    private String type = "";

    private String status = "";


}

