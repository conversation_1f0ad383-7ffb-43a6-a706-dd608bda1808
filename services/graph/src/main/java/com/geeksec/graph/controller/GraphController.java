package com.geeksec.graph.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.graph.condition.VidsSearchCondition;
import com.geeksec.graph.service.AtlasService;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 网络流量智能图谱系统控制器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@Slf4j
@RequestMapping("/atlas")
@RequiredArgsConstructor
@Tag(name = "图谱管理", description = "图谱操作")
public class GraphController extends BaseController {

    @Autowired
    private AtlasService atlasService;

    /**
     * 根据VID查询对应的第一层数据(图关联查询入口)
     *
     * @param condition VID搜索条件
     * @return 查询结果
     */
    @PostMapping("/search")
    @Operation(summary = "VID搜索", description = "根据VID查询对应的第一层数据")
    public ApiResponse<Object> vidsSearch(@RequestBody VidsSearchCondition condition) {
        try {
            // 不允许存在空字符串
            if (condition.getVidList().stream().anyMatch(StrUtil::isEmpty)) {
                return ApiResponse.error("VID列表中不能包含空字符串");
            }
            Object result = atlasService.vidSearch(condition);
            return success(result);
        } catch (Exception e) {
            log.error("VID搜索失败", e);
            return ApiResponse.error("VID搜索失败: " + e.getMessage());
        }
    }

}
