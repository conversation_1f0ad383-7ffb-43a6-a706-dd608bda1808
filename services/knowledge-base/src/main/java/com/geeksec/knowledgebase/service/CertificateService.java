package com.geeksec.knowledgebase.service;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 证书相关知识库服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CertificateService {

    private static final String FREE_CERT_AUTHORITIES_PATH = "/certificate/free_certificate_authorities.txt";
    private static final String OID_MAPPINGS_PATH = "/certificate/OID.csv";
    private static final String TRUSTED_CA_CERTS_PATH = "/certificate/trusted_ca_certificates.txt";

    private final List<String> freeCertificateAuthorities = new ArrayList<>();
    private final Map<String, Map<String, Object>> oidInfo = new ConcurrentHashMap<>();
    private final Map<String, List<Map<String, Object>>> oidsByType = new ConcurrentHashMap<>();
    private final List<String> trustedCaCertificates = new ArrayList<>();

    @PostConstruct
    public void initialize() {
        loadFreeCertificateAuthorities();
        loadOidMappings();
        loadTrustedCaCertificates();
    }

    public boolean isFreeCertificateAuthority(String issuer) {
        if (issuer == null || issuer.trim().isEmpty()) {
            return false;
        }
        String lowerIssuer = issuer.toLowerCase().trim();
        return freeCertificateAuthorities.stream()
                .anyMatch(freeCa -> lowerIssuer.contains(freeCa.toLowerCase()));
    }

    public Map<String, Object> getOidInfo(String oid) {
        return oidInfo.get(oid);
    }

    public List<Map<String, Object>> getOidsByType(String type) {
        return oidsByType.get(type.toUpperCase());
    }

    public Map<String, String> parseSubjectDN(String subjectDN) {
        Map<String, String> subjectMap = new ConcurrentHashMap<>();
        Pattern pattern = Pattern.compile("([A-Z]+)=([^,]+)");
        Matcher matcher = pattern.matcher(subjectDN);
        while (matcher.find()) {
            subjectMap.put(matcher.group(1), matcher.group(2));
        }
        return subjectMap;
    }

    /**
     * 检查证书SHA1是否为可信CA证书
     *
     * @param sha1 证书SHA1哈希值
     * @return 是否为可信CA证书
     */
    public boolean isTrustedCaCertificate(String sha1) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            return false;
        }
        return trustedCaCertificates.contains(sha1.toLowerCase().trim());
    }

    /**
     * 获取所有可信CA证书SHA1列表
     *
     * @return 可信CA证书SHA1列表
     */
    public List<String> getTrustedCaCertificates() {
        return new ArrayList<>(trustedCaCertificates);
    }

    /**
     * 批量检查证书SHA1是否为可信CA证书
     *
     * @param sha1List 证书SHA1哈希值列表
     * @return SHA1到是否可信的映射
     */
    public Map<String, Boolean> batchCheckTrustedCaCertificates(List<String> sha1List) {
        Map<String, Boolean> result = new ConcurrentHashMap<>();
        if (sha1List != null) {
            for (String sha1 : sha1List) {
                result.put(sha1, isTrustedCaCertificate(sha1));
            }
        }
        return result;
    }

    private List<String> readLinesFromResource(String path) throws IOException {
        List<String> lines = new ArrayList<>();
        try (InputStream is = getClass().getResourceAsStream(path)) {
            if (is == null) {
                log.warn("资源文件未找到: {}", path);
                return lines;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                }
            }
        }
        return lines;
    }

    private void loadFreeCertificateAuthorities() {
        try {
            List<String> lines = readLinesFromResource(FREE_CERT_AUTHORITIES_PATH);
            freeCertificateAuthorities.addAll(lines);
            log.info("成功加载免费证书颁发机构列表 {} 个", freeCertificateAuthorities.size());
        } catch (IOException e) {
            log.error("加载免费证书颁发机构列表失败", e);
        }
    }

    private void loadOidMappings() {
        try {
            List<String> lines = readLinesFromResource(OID_MAPPINGS_PATH);
            boolean isFirstLine = true;
            for (String line : lines) {
                if (isFirstLine) {
                    isFirstLine = false;
                    continue; // 跳过标题行
                }

                String[] parts = line.split(",");
                if (parts.length >= 3) {
                    String oid = parts[0].trim();
                    String description = parts[1].trim();
                    String type = parts[2].trim().toUpperCase();

                    if (!oid.isEmpty() && !description.isEmpty()) {
                        Map<String, Object> info = Map.of(
                                "oid", oid,
                                "description", description,
                                "type", type
                        );
                        oidInfo.put(oid, info);
                        oidsByType.computeIfAbsent(type, k -> new ArrayList<>()).add(info);
                    }
                }
            }
            log.info("成功加载OID映射 {} 个", oidInfo.size());
        } catch (IOException e) {
            log.error("加载OID映射失败", e);
        }
    }

    /**
     * 加载可信CA证书列表
     */
    private void loadTrustedCaCertificates() {
        try {
            List<String> lines = readLinesFromResource(TRUSTED_CA_CERTS_PATH);
            for (String line : lines) {
                line = line.trim();
                // 跳过空行和注释行
                if (!line.isEmpty() && !line.startsWith("#")) {
                    trustedCaCertificates.add(line.toLowerCase());
                }
            }
            log.info("成功加载可信CA证书列表 {} 个", trustedCaCertificates.size());
        } catch (IOException e) {
            log.error("加载可信CA证书列表失败", e);
        }
    }
}
