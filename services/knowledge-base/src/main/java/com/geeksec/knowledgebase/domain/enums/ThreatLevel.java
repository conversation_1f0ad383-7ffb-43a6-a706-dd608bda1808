package com.geeksec.knowledgebase.domain.enums;

import lombok.Getter;

/**
 * 威胁等级枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ThreatLevel {
    
    /**
     * 信息级别 - 仅供参考，无需立即处理
     */
    INFO("信息", "仅供参考的安全信息，无需立即处理", 1, "#17a2b8"),
    
    /**
     * 低危 - 风险较小，建议关注
     */
    LOW("低危", "风险较小的安全威胁，建议关注和监控", 2, "#28a745"),
    
    /**
     * 中危 - 需要关注并及时处理
     */
    MEDIUM("中危", "需要关注的安全威胁，建议及时处理", 3, "#ffc107"),
    
    /**
     * 高危 - 严重威胁，需要立即处理
     */
    HIGH("高危", "严重的安全威胁，需要立即处理和响应", 4, "#fd7e14"),
    
    /**
     * 严重 - 极其严重，需要紧急处理
     */
    CRITICAL("严重", "极其严重的安全威胁，需要紧急处理和响应", 5, "#dc3545");
    
    private final String displayName;
    private final String description;
    private final int level;
    private final String color;
    
    ThreatLevel(String displayName, String description, int level, String color) {
        this.displayName = displayName;
        this.description = description;
        this.level = level;
        this.color = color;
    }
    

    
    /**
     * 根据显示名称获取枚举值
     */
    public static ThreatLevel fromDisplayName(String displayName) {
        for (ThreatLevel level : values()) {
            if (level.displayName.equals(displayName)) {
                return level;
            }
        }
        return INFO;
    }
    
    /**
     * 根据等级数值获取枚举值
     */
    public static ThreatLevel fromLevel(int level) {
        for (ThreatLevel threatLevel : values()) {
            if (threatLevel.level == level) {
                return threatLevel;
            }
        }
        return INFO;
    }
    
    /**
     * 判断是否为高风险等级（高危及以上）
     */
    public boolean isHighRisk() {
        return this.level >= HIGH.level;
    }
    
    /**
     * 判断是否为严重等级
     */
    public boolean isCritical() {
        return this == CRITICAL;
    }
}
