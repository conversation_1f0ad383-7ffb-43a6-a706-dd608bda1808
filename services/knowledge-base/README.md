# NTA 3.0 知识库服务

## 概述

知识库服务是NTA 3.0平台的核心组件，负责统一管理和提供各类安全分析所需的知识数据，包括威胁情报、检测规则、地理位置信息等。该服务替代了原有的硬编码数据和CSV文件存储方式，提供了更加灵活、可维护的知识库管理方案。

## 主要功能

### 1. 威胁情报管理
- 威胁类型分类（恶意软件、APT、僵尸网络等）
- 威胁等级评估（低危、中危、高危、严重）
- 攻击向量和影响范围描述
- CVE关联和技术背景信息
- 置信度评分和数据来源追踪

### 2. 证书标签管理
- 证书标签分类和评分
- 黑白名单评分映射
- 标签类型管理（安全、信任、用途等）
- 证书检测模型映射

### 3. 告警知识库
- 告警规则和检测原理
- 攻击链和MITRE技术映射
- 修复建议和误报条件
- 告警等级和处理优先级

### 4. 检测器配置
- 检测器类型和参数配置
- 阈值设置和优先级管理
- 检测规则版本控制
- 动态配置更新

### 5. 地理位置数据
- IP地理位置查询
- ASN组织信息
- 匿名代理和卫星提供商标识

### 6. 域名数据管理
- 恶意域名列表
- 公共后缀列表
- Tranco排名数据
- 域名安全检查

## 技术架构

### 数据库设计
- **主数据库**: PostgreSQL 15+
- **缓存层**: Redis + Caffeine
- **数据模型**: 支持JSON字段和复杂数据类型

### 应用架构
- **框架**: Spring Boot 3.2.0 + JDK 17
- **数据访问**: Spring Data JPA
- **缓存**: Spring Cache + Caffeine
- **监控**: Spring Boot Actuator + Prometheus

### API设计
- **RESTful API**: 标准的REST接口设计
- **批量操作**: 支持批量查询和更新
- **分页查询**: 大数据量的分页处理
- **缓存策略**: 多级缓存提升性能

## 快速开始

### 1. 环境要求
- JDK 17+
- PostgreSQL 15+
- Redis 6+
- Maven 3.8+

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE nta_knowledge;

-- 创建用户
CREATE USER nta_user WITH PASSWORD 'nta_password';
GRANT ALL PRIVILEGES ON DATABASE nta_knowledge TO nta_user;
```

### 3. 配置文件
```yaml
spring:
  datasource:
    url: **********************************************
    username: nta_user
    password: nta_password
  
  data:
    redis:
      host: localhost
      port: 6379
```

### 4. 启动服务
```bash
cd services/knowledge-base
mvn spring-boot:run
```

### 5. 数据迁移
```bash
# 运行数据迁移脚本
python3 scripts/migrate-knowledge-data.py
```

## API使用示例

### 威胁情报查询
```bash
# 获取所有威胁情报
curl http://localhost:8080/knowledge-base/api/v1/threat-intelligence

# 根据威胁类型查询
curl http://localhost:8080/knowledge-base/api/v1/threat-intelligence/type/MALWARE

# 根据威胁等级查询
curl http://localhost:8080/knowledge-base/api/v1/threat-intelligence/severity/HIGH
```

### 证书标签查询
```bash
# 获取所有证书标签
curl http://localhost:8080/knowledge-base/api/v1/certificate-labels

# 获取黑名单评分映射
curl http://localhost:8080/knowledge-base/api/v1/certificate-labels/score-map/black

# 批量获取标签评分
curl -X POST http://localhost:8080/knowledge-base/api/v1/certificate-labels/batch/scores \
  -H "Content-Type: application/json" \
  -d '[201, 202, 203]'
```

## Flink作业集成

### 1. 添加依赖
```xml
<dependency>
    <groupId>com.geeksec</groupId>
    <artifactId>common</artifactId>
    <version>3.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 使用知识库客户端
```java
// 创建客户端实例（使用默认URL）
KnowledgeBaseClient client = new KnowledgeBaseClient();

// 或者指定URL
KnowledgeBaseClient client = new KnowledgeBaseClient("http://knowledge-base:8080/knowledge-base");

// 在Flink作业中使用工具类
KnowledgeBaseClient client = KnowledgeBaseUtils.createInstance(parameters);

// 获取证书标签评分
Map<String, Integer> blackScoreMap = client.getCertificateBlackScoreRemarkMap();
Map<String, Integer> whiteScoreMap = client.getCertificateWhiteScoreRemarkMap();

// 获取威胁情报
List<Map<String, Object>> threats = client.getThreatIntelligenceByType("MALWARE");

// 检查恶意域名
boolean isMalicious = client.isMaliciousDomain("example.com");

// 记得在使用完毕后关闭客户端
client.close();
```

### 3. 配置知识库URL
```properties
# Flink作业配置
knowledge.base.url=http://knowledge-base-service:8080/knowledge-base
```

## 管理界面

访问 `http://localhost:8080/knowledge-base/management` 进入管理界面，支持：

- 威胁情报的增删改查
- 证书标签的管理和配置
- 数据同步和导入导出
- 系统状态监控

## 部署指南

### Docker部署
```bash
# 构建镜像
mvn clean package dockerfile:build

# 运行容器
docker run -d \
  --name knowledge-base \
  -p 8080:8080 \
  -e DB_HOST=postgresql \
  -e DB_PASSWORD=your_password \
  nta/knowledge-base:3.0.0-SNAPSHOT
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f deployment/k8s/knowledge-base-deployment.yaml

# 检查状态
kubectl get pods -n nta -l app=knowledge-base
```

## 监控和运维

### 健康检查
```bash
curl http://localhost:8080/knowledge-base/actuator/health
```

### 指标监控
```bash
curl http://localhost:8080/knowledge-base/actuator/metrics
curl http://localhost:8080/knowledge-base/actuator/prometheus
```

### 日志配置
```yaml
logging:
  level:
    com.geeksec.knowledgebase: DEBUG
  file:
    name: logs/knowledge-base.log
```

## 性能优化

### 缓存策略
- **本地缓存**: Caffeine缓存热点数据
- **分布式缓存**: Redis缓存查询结果
- **缓存预热**: 启动时预加载常用数据

### 数据库优化
- **索引优化**: 为常用查询字段创建索引
- **连接池**: Druid连接池配置
- **查询优化**: 使用批量查询减少数据库访问

### 应用优化
- **异步处理**: 非关键操作异步执行
- **批量操作**: 支持批量查询和更新
- **资源管理**: 合理配置JVM参数

## 故障排查

### 常见问题
1. **数据库连接失败**: 检查数据库配置和网络连通性
2. **缓存异常**: 检查Redis连接和配置
3. **API响应慢**: 检查数据库查询和缓存命中率
4. **内存溢出**: 调整JVM堆内存配置

### 日志分析
```bash
# 查看应用日志
tail -f logs/knowledge-base.log

# 查看错误日志
grep ERROR logs/knowledge-base.log
```

## 开发指南

### 添加新的知识库数据类型
1. 创建实体类和枚举
2. 定义Repository接口
3. 实现Service层逻辑
4. 添加Controller接口
5. 更新数据库迁移脚本

### 扩展API接口
1. 在Controller中添加新的端点
2. 实现相应的Service方法
3. 添加单元测试
4. 更新API文档

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
