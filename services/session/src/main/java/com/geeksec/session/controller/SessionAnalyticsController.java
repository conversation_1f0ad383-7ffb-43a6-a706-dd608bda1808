package com.geeksec.session.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.session.model.dto.*;
import com.geeksec.session.service.SessionAnalyticsService;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话分析统计控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "会话分析统计", description = "提供会话数据的统计分析功能")
@RestController
@RequestMapping("/api/sessions/analytics")
@RequiredArgsConstructor
public class SessionAnalyticsController {
    
    private final SessionAnalyticsService sessionAnalyticsService;
    
    @Operation(summary = "获取会话统计概览", description = "获取指定时间范围内的会话统计概览信息")
    @PostMapping("/statistics")
    public ApiResponse<SessionStatisticsResponse> getSessionStatistics(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "过滤条件")
            @RequestBody(required = false) SessionQueryRequest filterCondition) {
        log.debug("获取会话统计概览, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getSessionStatistics(startTime, endTime, filterCondition));
    }
    
    @Operation(summary = "获取会话聚合分析", description = "根据指定字段对会话数据进行聚合分析")
    @PostMapping("/aggregation")
    public ApiResponse<Page<Map<String, Object>>> getSessionAggregation(
            @Parameter(description = "聚合查询请求", required = true)
            @Valid @RequestBody SessionAggregationRequest request) {
        log.debug("获取会话聚合分析, 参数: {}", request);
        return ApiResponse.success(sessionAnalyticsService.getSessionAggregation(request));
    }
    
    @Operation(summary = "获取会话趋势分析", description = "获取指定时间范围内的会话趋势数据")
    @GetMapping("/trend")
    public ApiResponse<List<SessionTrendResponse>> getSessionTrend(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "时间间隔(分钟)", example = "60")
            @RequestParam(defaultValue = "60") int interval,
            @Parameter(description = "过滤条件")
            @RequestBody(required = false) SessionQueryRequest filterCondition) {
        log.debug("获取会话趋势分析, 开始时间: {}, 结束时间: {}, 间隔: {}分钟", startTime, endTime, interval);
        return ApiResponse.success(sessionAnalyticsService.getSessionTrend(startTime, endTime, interval, filterCondition));
    }
    
    @Operation(summary = "获取热门源IP排行", description = "获取指定时间范围内的热门源IP排行榜")
    @GetMapping("/ranking/source-ips")
    public ApiResponse<List<Map<String, Object>>> getTopSourceIpRanking(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "排序字段", example = "session_count")
            @RequestParam(defaultValue = "session_count") String orderBy) {
        log.debug("获取热门源IP排行, 开始时间: {}, 结束时间: {}, 限制: {}", startTime, endTime, limit);
        return ApiResponse.success(sessionAnalyticsService.getTopSourceIpRanking(startTime, endTime, limit, orderBy));
    }
    
    @Operation(summary = "获取热门目标IP排行", description = "获取指定时间范围内的热门目标IP排行榜")
    @GetMapping("/ranking/destination-ips")
    public ApiResponse<List<Map<String, Object>>> getTopDestinationIpRanking(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "排序字段", example = "session_count")
            @RequestParam(defaultValue = "session_count") String orderBy) {
        log.debug("获取热门目标IP排行, 开始时间: {}, 结束时间: {}, 限制: {}", startTime, endTime, limit);
        return ApiResponse.success(sessionAnalyticsService.getTopDestinationIpRanking(startTime, endTime, limit, orderBy));
    }
    
    @Operation(summary = "获取热门端口排行", description = "获取指定时间范围内的热门端口排行榜")
    @GetMapping("/ranking/ports")
    public ApiResponse<List<Map<String, Object>>> getTopPortRanking(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "端口类型", example = "dst_port")
            @RequestParam(defaultValue = "dst_port") String portType) {
        log.debug("获取热门端口排行, 开始时间: {}, 结束时间: {}, 限制: {}, 端口类型: {}", startTime, endTime, limit, portType);
        return ApiResponse.success(sessionAnalyticsService.getTopPortRanking(startTime, endTime, limit, portType));
    }
    
    @Operation(summary = "获取应用协议分布", description = "获取指定时间范围内的应用协议分布统计")
    @GetMapping("/distribution/applications")
    public ApiResponse<List<Map<String, Object>>> getApplicationProtocolDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取应用协议分布, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getApplicationProtocolDistribution(startTime, endTime));
    }
    
    @Operation(summary = "获取网络协议分布", description = "获取指定时间范围内的网络协议分布统计")
    @GetMapping("/distribution/protocols")
    public ApiResponse<List<Map<String, Object>>> getNetworkProtocolDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取网络协议分布, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getNetworkProtocolDistribution(startTime, endTime));
    }
    
    @Operation(summary = "获取流量方向分析", description = "获取指定时间范围内的流量方向分析")
    @GetMapping("/traffic-direction")
    public ApiResponse<Map<String, Object>> getTrafficDirectionAnalysis(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取流量方向分析, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getTrafficDirectionAnalysis(startTime, endTime));
    }
    
    @Operation(summary = "获取会话持续时间分布", description = "获取指定时间范围内的会话持续时间分布")
    @GetMapping("/distribution/duration")
    public ApiResponse<List<Map<String, Object>>> getSessionDurationDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取会话持续时间分布, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getSessionDurationDistribution(startTime, endTime));
    }
    
    @Operation(summary = "获取数据包大小分布", description = "获取指定时间范围内的数据包大小分布")
    @GetMapping("/distribution/packet-size")
    public ApiResponse<List<Map<String, Object>>> getPacketSizeDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取数据包大小分布, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getPacketSizeDistribution(startTime, endTime));
    }
    
    @Operation(summary = "获取标签使用统计", description = "获取指定时间范围内的标签使用统计")
    @GetMapping("/labels/usage")
    public ApiResponse<List<Map<String, Object>>> getLabelUsageStatistics(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取标签使用统计, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getLabelUsageStatistics(startTime, endTime));
    }
    
    @Operation(summary = "获取异常会话检测", description = "检测指定时间范围内的异常会话")
    @PostMapping("/anomalies")
    public ApiResponse<List<Map<String, Object>>> getAnomalousSessionDetection(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "异常检测阈值配置")
            @RequestBody(required = false) Map<String, Object> threshold) {
        log.debug("获取异常会话检测, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionAnalyticsService.getAnomalousSessionDetection(startTime, endTime, threshold));
    }
    
    @Operation(summary = "获取网络拓扑分析", description = "获取指定时间范围内的网络拓扑分析")
    @GetMapping("/topology")
    public ApiResponse<Map<String, Object>> getNetworkTopologyAnalysis(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "分析深度", example = "3")
            @RequestParam(defaultValue = "3") int depth) {
        log.debug("获取网络拓扑分析, 开始时间: {}, 结束时间: {}, 深度: {}", startTime, endTime, depth);
        return ApiResponse.success(sessionAnalyticsService.getNetworkTopologyAnalysis(startTime, endTime, depth));
    }
    
    @Operation(summary = "获取实时会话监控", description = "获取实时会话监控数据")
    @GetMapping("/realtime")
    public ApiResponse<Map<String, Object>> getRealTimeSessionMonitoring(
            @Parameter(description = "监控时间窗口(分钟)", example = "5")
            @RequestParam(defaultValue = "5") int minutes) {
        log.debug("获取实时会话监控, 时间窗口: {}分钟", minutes);
        return ApiResponse.success(sessionAnalyticsService.getRealTimeSessionMonitoring(minutes));
    }
}
