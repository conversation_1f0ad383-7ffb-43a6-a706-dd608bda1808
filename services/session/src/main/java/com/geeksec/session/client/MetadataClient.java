package com.geeksec.session.client;

import com.geeksec.common.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Map;

/**
 * 元数据服务Feign客户端
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@FeignClient(name = "metadata-service")
public interface MetadataClient {
    
    /**
     * 根据标签ID获取标签信息
     *
     * @param labelId 标签ID
     * @return 标签信息
     */
    @GetMapping("/metadata/api/metadata/labels/{labelId}")
    ApiResponse<Map<String, Object>> getLabelById(@PathVariable("labelId") Integer labelId);
    
    /**
     * 根据设备IP获取设备信息
     * 
     * @param deviceIp 设备IP
     * @return 设备信息
     */
    @GetMapping("/metadata/api/metadata/devices/ip/{deviceIp}")
    ApiResponse<Map<String, Object>> getDeviceByIp(@PathVariable("deviceIp") String deviceIp);
    
    /**
     * 根据协议号获取协议信息
     * 
     * @param protocolNumber 协议号
     * @return 协议信息
     */
    @GetMapping("/metadata/api/metadata/protocols/number/{protocolNumber}")
    ApiResponse<Map<String, Object>> getProtocolByNumber(@PathVariable("protocolNumber") Integer protocolNumber);
    
    /**
     * 批量获取标签信息
     *
     * @param labelIds 标签ID列表
     * @return 标签信息列表
     */
    @GetMapping("/metadata/api/metadata/labels/batch")
    ApiResponse<List<Map<String, Object>>> getLabelsByIds(@PathVariable("labelIds") List<Integer> labelIds);
}
