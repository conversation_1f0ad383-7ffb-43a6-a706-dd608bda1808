package com.geeksec.task.application.service;

import java.util.List;

import com.geeksec.task.model.vo.FileTreeNodeVo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 离线任务批次服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface OfflineTaskBatchService {

    /**
     * 根据任务ID查询批次信息
     *
     * @param condition 查询条件
     * @return 批次分页信息
     */
    BatchPageResult pageBatch(OfflineTaskBatchQueryCondition condition);

    /**
     * 数据导入
     *
     * @param dto 批次信息
     * @return 导入结果
     */
    BatchOperationResult addBatch(OfflineTaskBatchDto dto);

    /**
     * 取消数据导入
     *
     * @param dto 取消参数
     * @return 取消结果
     */
    BatchOperationResult cancelBatch(OfflineTaskBatchCancelDto dto);

    /**
     * 查询服务器文件路径（基于MinIO）
     * 
     * @param directoryPath 目录路径
     * @return 文件树节点列表
     */
    List<FileTreeNodeVo> listServerPath(String directoryPath);

    /**
     * 检查文件路径是否存在（基于MinIO）
     * 
     * @param filePathList 文件路径列表
     * @return 检查结果
     */
    CheckFilePathsResult checkFilePaths(List<String> filePathList);

    /**
     * 删除批次下的PCAP文件（基于MinIO）
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    DeleteFilesResult deletePcapFiles(String taskId);

    /**
     * 检查文件路径结果
     */
    @Data
    @AllArgsConstructor
    class CheckFilePathsResult {
        private boolean status;
        private List<String> nonExistFiles;
    }

    /**
     * 删除文件结果
     */
    @Data
    @AllArgsConstructor
    class DeleteFilesResult {
        private boolean status;
        private String message;
    }

    /**
     * 批次操作结果
     */
    @Data
    @AllArgsConstructor
    class BatchOperationResult {
        private boolean success;
        private String message;
        private Object data;

        public BatchOperationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    /**
     * 批次分页结果
     */
    @Data
    @AllArgsConstructor
    class BatchPageResult {
        private List<OfflineTaskBatchPageVo> data;
        private long total;
        private int currentPage;
        private int pageSize;
    }

    // 临时类定义，实际应该在对应的包中定义
    @Data
    class OfflineTaskBatchPageVo {
        private Integer batchId;
        private Integer taskId;
        private String batchRemark;
        private Integer batchStatus;
        private Double batchProgress;
        private Integer beginTime;
        private Integer endTime;
        private Long batchBytes;
        private Integer batchSession;
        private Integer batchAlarm;
        private String batchDir;
        private Integer state;
    }

    @Data
    class OfflineTaskBatchQueryCondition {
        private Integer taskId;
        private Integer currentPage = 1;
        private Integer pageSize = 10;
    }

    @Data
    class OfflineTaskBatchDto {
        private Integer taskId;
        private String batchDescription;
        private Integer batchType;
        private String fullflowState;
        private String flowlogState;
        private List<String> filePathList;
    }

    @Data
    class OfflineTaskBatchCancelDto {
        private Integer batchId;
    }
}
