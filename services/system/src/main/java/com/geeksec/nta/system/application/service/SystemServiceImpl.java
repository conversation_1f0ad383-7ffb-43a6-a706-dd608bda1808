package com.geeksec.nta.system.application.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;


import com.geeksec.nta.system.domain.model.*;
import com.geeksec.nta.system.domain.service.SystemService;
import com.geeksec.nta.system.infrastructure.repository.DiskFieldDao;
import com.geeksec.nta.system.infrastructure.repository.DiskTypeDao;
import com.geeksec.nta.system.infrastructure.repository.SystemInfoDao;

import com.geeksec.nta.system.infrastructure.external.CommandExecutor;
import com.geeksec.nta.system.domain.service.LibraryCheckService;
import com.geeksec.nta.system.domain.service.DiskManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 系统管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemServiceImpl implements SystemService {

    /**
     * 数据清理的Key
     */
    private static final List<String> CLEAN_KEY = Arrays.asList("conf", "filter", "rule", "pcap", "PbSession", "SSL", "HTTP", "DNS", "log", "cert");





    private final SystemInfoDao systemInfoDao;
    private final DiskTypeDao diskTypeDao;
    private final DiskFieldDao diskFieldDao;
    private final CommandExecutor commandExecutor;
    private final LibraryCheckService libraryCheckService;
    private final DiskManagementService diskManagementService;

    @Override
    public JSONObject shutdown() {
        log.info("执行关机操作");

        try {
            // 修改tb_valset值，保证其发送请求后为关机操作,关机为0，重启为1
            systemInfoDao.modifyShutdownValue(0);

            // 直接执行关机命令
            String command = "/bin/bash /opt/GeekSec/web/rule_syn/shutdown_restart.sh shutdown";
            String result = commandExecutor.executeCommand(command);

            log.info("关机命令执行成功: {}", result);
            return createSuccessJson("关机操作已启动");

        } catch (Exception e) {
            log.error("关机操作失败: {}", e.getMessage());
            throw new RuntimeException("关机操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject reboot() {
        log.info("执行重启操作");

        try {
            // 修改tb_valset值，保证其发送请求后为重启操作,关机为0，重启为1
            systemInfoDao.modifyShutdownValue(1);

            // 直接执行重启命令
            String command = "/bin/bash /opt/GeekSec/web/rule_syn/shutdown_restart.sh reboot";
            String result = commandExecutor.executeCommand(command);

            log.info("重启命令执行成功: {}", result);
            return createSuccessJson("重启操作已启动");

        } catch (Exception e) {
            log.error("重启操作失败: {}", e.getMessage());
            throw new RuntimeException("重启操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject changePassword(String userName, String password) {
        // TODO: 实现密码修改逻辑
        log.info("修改用户密码: {}", userName);
        return createSuccessJson();
    }

    @Override
    public JSONObject getDiskInfoData() {
        log.info("获取可管理的磁盘信息");

        // 注意：磁盘监控信息请使用 monitor 服务
        // 这里只返回可管理的磁盘列表
        return diskManagementService.getManageableDisks();
    }

    @Override
    public SystemInfoVo getSystemInfo() {
        SystemInfoVo systemInfo = null;
        try {
            systemInfo = systemInfoDao.getSystemInfo();
            if (systemInfo != null) {
                systemInfo.setTime(getSystemUptimeSeconds());
                systemInfo.setStartTime(getSystemStartTime());
            }
        } catch (Exception e) {
            log.error("系统信息查询失败: {}", e.getMessage());
        }
        return systemInfo;
    }

    @Override
    public ProductInfoVo getProductInfo() {
        ProductInfoVo productInfo = null;
        try {
            productInfo = systemInfoDao.getProductInfo();
        } catch (Exception e) {
            log.error("产品信息查询失败: {}", e.getMessage());
        }
        return productInfo;
    }



    /**
     * 创建成功响应
     */
    private JSONObject createSuccessJson() {
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }

    /**
     * 创建成功响应
     */
    private JSONObject createSuccessJson(Object data) {
        JSONObject result = new JSONObject();
        result.put("success", true);
        result.put("data", data);
        return result;
    }

    /**
     * 获取系统运行时间（秒）
     */
    private Long getSystemUptimeSeconds() {
        try (BufferedReader reader = new BufferedReader(new FileReader("/proc/uptime"))) {
            String line = reader.readLine();
            if (line != null) {
                String[] parts = line.split(" ");
                return (long) Double.parseDouble(parts[0]);
            }
        } catch (Exception e) {
            log.error("获取系统运行时间失败: {}", e.getMessage());
        }
        return 0L;
    }

    /**
     * 获取系统启动时间
     */
    private LocalDateTime getSystemStartTime() {
        Long uptimeSeconds = getSystemUptimeSeconds();
        if (uptimeSeconds > 0) {
            Instant startInstant = Instant.now().minusSeconds(uptimeSeconds);
            return LocalDateTime.ofInstant(startInstant, ZoneId.systemDefault());
        }
        return null;
    }

    @Override
    public JSONObject cleanData(CleanCondition condition) {
        log.info("即将清理数据: {}", condition);
        long startTime = System.currentTimeMillis();

        try {
            // 构建清理参数
            StringBuilder cleanParams = new StringBuilder();
            Integer taskId = condition.getTaskId();
            List<String> cleanList = condition.getCleanList();

            cleanParams.append("{\"task_id\":").append(taskId);
            for (String clean : CLEAN_KEY) {
                cleanParams.append(",\"").append(clean).append("\":");
                cleanParams.append(cleanList.contains(clean));
            }
            cleanParams.append("}");

            // 直接执行清理命令
            String command = String.format("cd /opt/GeekSec/web/rule_syn/task && /root/miniconda3/bin/python3 clean_task_data.py '%s'",
                                         cleanParams.toString());
            String result = commandExecutor.executeCommand(command);

            log.info("数据清理成功，用时：{}秒，结果：{}", (System.currentTimeMillis() - startTime) / 1000, result);
            return createSuccessJson("数据清理完成");

        } catch (Exception e) {
            log.error("数据清理失败: {}", e.getMessage());
            throw new RuntimeException("数据清理失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject systemReset(JSONObject json) {
        log.info("尝试重置系统操作：{}", json);

        try {
            // 直接执行系统重置命令
            String command = "cd /opt/GeekSec/web/rule_syn/ && /bin/bash system_reset.sh";
            String result = commandExecutor.executeCommand(command);

            log.info("系统重置命令执行成功: {}", result);
            return createSuccessJson("系统重置操作已启动");

        } catch (Exception e) {
            log.error("系统重置失败: {}", e.getMessage());
            throw new RuntimeException("系统重置失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject cleanDataSchedule() {
        try {
            // 直接执行清理状态查询命令
            String command = "cd /opt/GeekSec/web/rule_syn/task && /root/miniconda3/bin/python3 clean_task_status.py";
            String result = commandExecutor.executeCommand(command);

            // 尝试解析为JSON
            try {
                JSONObject statusData = JSONObject.parseObject(result);
                return createSuccessJson(statusData);
            } catch (Exception e) {
                // 如果不是JSON格式，包装成JSON返回
                JSONObject statusInfo = new JSONObject();
                statusInfo.put("raw_output", result);
                statusInfo.put("timestamp", System.currentTimeMillis());
                return createSuccessJson(statusInfo);
            }

        } catch (Exception e) {
            log.error("查询数据清理状态失败: {}", e.getMessage());
            throw new RuntimeException("查询数据清理状态失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject diskChange() {
        log.info("尝试进行磁盘更新操作。");

        try {
            // 在数据库中记录一条
            DiskType diskType = new DiskType();
            diskType.setStartTime(System.currentTimeMillis() / 1000);
            diskType.setState(0);
            diskType.setType(1);
            diskTypeDao.insert(diskType);

            return diskManagementService.updateDisk();

        } catch (Exception e) {
            log.error("磁盘更新失败: {}", e.getMessage());
            throw new RuntimeException("磁盘更新失败: " + e.getMessage());
        }
    }



    @Override
    public JSONObject diskRebuild() {
        log.info("尝试进行磁盘重组操作。");

        try {
            // 在数据库中记录一条
            DiskType diskType = new DiskType();
            diskType.setStartTime(System.currentTimeMillis() / 1000);
            diskType.setState(0);
            diskType.setType(2);
            diskTypeDao.insert(diskType);

            return diskManagementService.rebuildDisk();

        } catch (Exception e) {
            log.error("磁盘重组失败: {}", e.getMessage());
            throw new RuntimeException("磁盘重组失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject diskMountReady() {
        log.info("准备挂载磁盘操作。");
        return diskManagementService.prepareMount();
    }

    @Override
    public JSONObject diskMountData() {
        log.info("挂载磁盘操作。");
        return diskManagementService.mountDataDisk();
    }

    @Override
    public JSONObject checkSo(Integer ruleId) {
        log.info("动态库文件检测，规则ID: {}", ruleId);
        // 直接使用Java实现的动态库检测服务
        return libraryCheckService.checkSoFiles(ruleId);
    }

    @Override
    public JSONObject dockerCheckSo(String path) {
        log.info("Docker动态库文件检测，路径: {}", path);
        // 直接使用Java实现的动态库检测服务
        return libraryCheckService.checkDockerSoFiles(path);
    }

    @Override
    public JSONObject checkDiskStatus() {
        log.info("查询磁盘重组状态。");
        try {
            DiskType diskType = diskTypeDao.getOrderIdOne();
            if (diskType != null) {
                return createSuccessJson(diskType);
            } else {
                return createSuccessJson();
            }
        } catch (Exception e) {
            log.error("查询磁盘重组状态失败: {}", e.getMessage());
            throw new RuntimeException("查询磁盘重组状态失败");
        }
    }

    @Override
    public JSONObject getDiskField() {
        log.info("获取磁盘字段信息。");
        try {
            List<DiskField> diskFields = diskFieldDao.selectAll();
            return createSuccessJson(diskFields);
        } catch (Exception e) {
            log.error("获取磁盘字段信息失败: {}", e.getMessage());
            throw new RuntimeException("获取磁盘字段信息失败");
        }
    }
}
