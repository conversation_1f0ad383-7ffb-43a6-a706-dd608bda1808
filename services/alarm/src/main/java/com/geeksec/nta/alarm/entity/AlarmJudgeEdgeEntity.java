package com.geeksec.nta.alarm.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@EqualsAndHashCode(of = {"from", "to"})
public class AlarmJudgeEdgeEntity {

    /**
     * 起点
     */
    @JsonProperty(value = "from")
    private String from;

    /**
     * 终点
     */
    @JsonProperty(value = "to")
    private String to;

    /**
     * 边ID
     */
    @JsonProperty(value = "id")
    private String rankId;

    /**
     * 请求使用服务
     */
    @JsonProperty(value = "label")
    private String label;

    @JsonProperty(value = "lv")
    private Integer level;

    @JsonProperty(value = "num")
    private String num;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "type")
    private String type;



}
