package com.geeksec.nta.alarm.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.mapper.DorisConnectMapper;
import com.geeksec.nta.alarm.service.AlarmJudgeService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description：告警研判服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmJudgeServiceImpl implements AlarmJudgeService {
    final AlarmMapper alarmMapper;

    final DorisConnectMapper dorisConnectMapper;

    // final ThAnalysisDao thAnalysisDao; //TODO 待完善 - 暂时注释掉，等待实现

    @Override
    public Map<String, Object> createAlarmJudgeGraph(Map<String, Object> alarmMap) {
        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();

        Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
        Integer alarmKnowId = 0;
        if (alarmKnowledgeId instanceof String) {
            alarmKnowId = Integer.parseInt((String) alarmKnowledgeId);
        } else if (alarmKnowledgeId instanceof Number) {
            alarmKnowId = ((Number) alarmKnowledgeId).intValue();
        }

        Map<String, Object> resultMap;

        Object alarmType = alarmMap.get("alarm_type");
        if (alarmType instanceof String) {
            resultMap = handleGraphByKnowledgeType(alarmMap, alarmKnowId);
        } else {
            resultMap = handleGraphBySessionId(alarmMap);
        }

        return handleJudgeGraphResult(resultMap);
    }

    @Override
    public Map<String, Object> createAlarmJudgeGraphByRole(AlarmRoleJudgeCondition condition) {
        Map<String, Object> resultMap = new HashMap<>();
        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
        List<Map<String, Object>> sessionLabels = new ArrayList<>();

        log.info("通过角色和对应IP进行告警研判延伸查询 (Doris实现), condition: {}", condition);

        String ipAddr = condition.getIpAddr();
        List<String> roles = condition.getRole();
        AlarmRoleJudgeCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AlarmRoleJudgeCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        List<Map<String, Object>> alarms = alarmMapper.findAlarmsByRoleAndIp(
                ipAddr, roles, timeRange, condition.getQueryNum());

        if (CollectionUtils.isEmpty(alarms)) {
            throw new RuntimeException("未查询到相关告警信息");
        } else {
            log.info("查询到{}条告警信息，进行图像绘制", alarms.size());
            for (Map<String, Object> alarmMap : alarms) {
                Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
                Integer alarmKnowId = 0;
                if (alarmKnowledgeId instanceof String) {
                    alarmKnowId = Integer.parseInt((String) alarmKnowledgeId);
                } else if (alarmKnowledgeId instanceof Number) {
                    alarmKnowId = ((Number) alarmKnowledgeId).intValue();
                }

                Map<String, Object> singleGraphMap = handleGraphByKnowledgeType(alarmMap, alarmKnowId);
                vertexList.addAll((List<AlarmJudgeVertexEntity>) singleGraphMap.get("vertex"));
                edgeList.addAll((List<AlarmJudgeEdgeEntity>) singleGraphMap.get("edge"));
                sessionLabels.addAll((List<Map<String, Object>>) singleGraphMap.get("label_vertex"));
            }
        }
        resultMap.put("vertex", vertexList);
        resultMap.put("edge", edgeList);
        resultMap.put("label_vertex", sessionLabels);
        return resultMap;
    }

    private Map<String, Object> handleGraphByKnowledgeType(Map<String, Object> alarmMap, Integer alarmKnowledgeId) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
        List<Map<String, Object>> sessionLabels = new ArrayList<>();

        switch (alarmKnowledgeId) {
            case 100002: createScanBehavior(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 100017: createMingingConn(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 100005: createRemoteTrojan(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 100006: createMiningVirus(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 100009: createIllegalExtraConn(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 100010: craeteConvertTunnel(alarmMap, vertexList, edgeList, sessionLabels); break;
            case 120044: createRandomFinger(alarmMap, vertexList, edgeList, sessionLabels); break;
            default: break;
        }

        resultMap.put("vertex", vertexList);
        resultMap.put("edge", edgeList);
        resultMap.put("label_vertex", sessionLabels);
        return resultMap;
    }

    private Map<String, Object> handleGraphBySessionId(Map<String, Object> alarmMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> targets = (List<Map<String, Object>>) alarmMap.get("targets");
        if (CollectionUtils.isEmpty(targets)) {
            resultMap.put("vertex", new ArrayList<>());
            resultMap.put("edge", new ArrayList<>());
            resultMap.put("label_vertex", new ArrayList<>());
            return resultMap;
        }
        String sessionId = (String) targets.get(0).get("name");

        Map<String, Object> dataMap = dorisConnectMapper.findConnectLogBySessionId(sessionId);
        Set<String> ipSet = new HashSet<>();

        if (dataMap == null || dataMap.isEmpty()) {
            resultMap.put("vertex", new ArrayList<>());
            resultMap.put("edge", new ArrayList<>());
            resultMap.put("label_vertex", new ArrayList<>());
            return resultMap;
        } else {
            List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
            List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
            List<Map<String, Object>> sessionLabels = new ArrayList<>();

            String sIp = (String) dataMap.get("sIp");
            String dIp = (String) dataMap.get("dIp");
            ipSet.add(sIp);
            ipSet.add(dIp);
            String key = sIp + "_" + dIp;

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sIp);
            alarmConnEdge.setTo(dIp);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sIp);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dIp);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            for (String ipAddr : ipSet) {
                AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
                ipVertex.setType("IP");
                ipVertex.setLabel(ipAddr);
                ipVertex.setVid(ipAddr);
                ipVertex.setLevel(0);
                ipVertex.setStatus(StringUtils.EMPTY);
                ipVertex.setNum(StringUtils.EMPTY);
                ipVertex.setIdentity("other");
                vertexList.add(ipVertex);
            }

            resultMap.put("vertex", vertexList);
            resultMap.put("edge", edgeList);
            resultMap.put("label_vertex", sessionLabels);
        }
        return resultMap;
    }

    private List<String> getIpListByCharacter(Map<String, Object> alarmMap, String character) {
        List<String> ipList = new ArrayList<>();
        Object mapListObj = alarmMap.get(character);
        if (mapListObj instanceof List) {
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) mapListObj;
            for (Map<String, Object> ipMap : mapList) {
                String ipAddr = (String) ipMap.get("ip");
                ipList.add(ipAddr);
            }
        }
        return ipList;
    }

    private List<AlarmJudgeVertexEntity> getAlarmRouteList(Map<String, Object> alarmMap) {
        List<AlarmJudgeVertexEntity> routeVertexList = new ArrayList<>();
        Object attackRouteListObj = alarmMap.get("attack_route");
        if (attackRouteListObj instanceof List) {
            List<Map<String, Object>> attackRouteList = (List<Map<String, Object>>) attackRouteListObj;
            for (Map<String, Object> routeMap : attackRouteList) {
                String routeType = (String) routeMap.get("type");
                String routeName = (String) routeMap.get("name");
                List<String> tagList = (List<String>) routeMap.get("label");
                AlarmJudgeVertexEntity routeVertex = new AlarmJudgeVertexEntity();
                if (StringUtils.equals(routeType, "finger")) {
                    routeType = "SSLFINGER";
                }
                routeVertex.setType(routeType.toUpperCase());
                routeVertex.setLabel(routeName);
                routeVertex.setVid(routeName);
                routeVertex.setTagList(CollectionUtils.isNotEmpty(tagList) ? tagList : new ArrayList<>());
                routeVertex.setLevel(0);
                routeVertex.setIdentity("route");
                routeVertex.setStatus(StringUtils.EMPTY);
                routeVertex.setNum(StringUtils.EMPTY);
                routeVertexList.add(routeVertex);
            }
        }
        return routeVertexList;
    }

    private List<Map<String, Object>> getAlarmConnectionDataList(Map<String, Object> alarmMap) {
        Object alarmKnowledgeIdObj = alarmMap.get("alarm_knowledge_id");
        Integer alarmKnowId = 0;
        if (alarmKnowledgeIdObj instanceof String) {
            alarmKnowId = Integer.parseInt((String) alarmKnowledgeIdObj);
        } else if (alarmKnowledgeIdObj instanceof Number) {
            alarmKnowId = ((Number) alarmKnowledgeIdObj).intValue();
        }

        List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");
        if (CollectionUtils.isEmpty(sessionList)) {
            return Collections.emptyList();
        }

        List<String> querySessionList = new ArrayList<>(sessionList);
        int limit = sessionList.size();

        if (alarmKnowId == 120044) {
            querySessionList = Collections.singletonList(sessionList.get(0));
            limit = 1;
        }

        List<Map<String, Object>> results = dorisConnectMapper.findConnectLogsBySessionIds(querySessionList, limit);

        if (CollectionUtils.isEmpty(results)) {
            log.warn("在 connect_log 表中未找到会话 [{}], 尝试在 ssl_log 表中查找 (此逻辑待实现)", querySessionList);
            // 假设有 dorisConnectMapper.findSslLogsBySessionIds(...)
            // results = dorisConnectMapper.findSslLogsBySessionIds(querySessionList, limit);
        }
        return results == null ? Collections.emptyList() : results;
    }

    private void createScanBehavior(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        try {
            List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
            List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
            List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
            vertexList.addAll(routeVertexList);
            Set<String> ipSet = new HashSet<>();

            List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
            for (Map<String, Object> dataMap : connectionDataList) {
                String sip = (String) dataMap.get("sIp");
                String dip = (String) dataMap.get("dIp");
                String key = sip + "_" + dip;
                ipSet.add(sip);
                ipSet.add(dip);

                AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                alarmConnEdge.setFrom(sip);
                alarmConnEdge.setTo(dip);
                alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                alarmConnEdge.setRankId(key);
                edgeList.add(alarmConnEdge);

                List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
                Map<String, Object> labelMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(labelList)) {
                    labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                }
                labelMap.put("id", key + "_label");
                labelMap.put("type", "LABELS");
                labelMap.put("labels", labelsInfo);
                sessionLabels.add(labelMap);

                AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                sipConnLabelEdge.setFrom(sip);
                sipConnLabelEdge.setTo(key + "_label");
                sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                sipConnLabelEdge.setRankId(key);
                edgeList.add(sipConnLabelEdge);

                AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                connDipLabelEdge.setFrom(key + "_label");
                connDipLabelEdge.setTo(dip);
                connDipLabelEdge.setLabel(StringUtils.EMPTY);
                connDipLabelEdge.setRankId(key);
                edgeList.add(connDipLabelEdge);

                if (CollectionUtils.isNotEmpty(routeVertexList)) {
                    for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                        AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                        sIpFingerEdge.setFrom(sip);
                        sIpFingerEdge.setTo(sslFingerVertex.getVid());
                        sIpFingerEdge.setLabel(StringUtils.EMPTY);
                        sIpFingerEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(sIpFingerEdge);

                        AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                        fingerDipEdge.setFrom(sslFingerVertex.getVid());
                        fingerDipEdge.setTo(dip);
                        fingerDipEdge.setLabel(StringUtils.EMPTY);
                        fingerDipEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(fingerDipEdge);
                    }
                }
            }

            for (String ipAddr : ipSet) {
                AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
                ipVertex.setType("IP");
                ipVertex.setLabel(ipAddr);
                ipVertex.setVid(ipAddr);
                ipVertex.setLevel(0);
                ipVertex.setStatus(StringUtils.EMPTY);
                ipVertex.setNum(StringUtils.EMPTY);
                if (attackerIpList.contains(ipAddr)) {
                    ipVertex.setIdentity("attacker");
                } else if (victimIpList.contains(ipAddr)) {
                    ipVertex.setIdentity("victim");
                } else {
                    ipVertex.setIdentity("other");
                }
                vertexList.add(ipVertex);
            }
        } catch (Exception e) {
            log.error("绘制扫描行为告警研判关联图失败,error->", e);
        }
    }

    private void createMingingConn(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        for (Map<String, Object> dataMap : connectionDataList) {
            String sip = (String) dataMap.get("sIp");
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            HashMap<String, Object> domainIpMap = ((List<HashMap<String, Object>>) dataMap.get("DNS")).get(0);
            List<String> domainIpList = (List<String>) domainIpMap.get("DomainIp");

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity domainVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpDomainEdge = new AlarmJudgeEdgeEntity();
                    sIpDomainEdge.setFrom(sip);
                    sIpDomainEdge.setTo(domainVertex.getVid());
                    sIpDomainEdge.setLabel(StringUtils.EMPTY);
                    sIpDomainEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpDomainEdge);

                    AlarmJudgeEdgeEntity domainDipEdge = new AlarmJudgeEdgeEntity();
                    domainDipEdge.setFrom(domainVertex.getVid());
                    domainDipEdge.setTo(dip);
                    domainDipEdge.setLabel(StringUtils.EMPTY);
                    domainDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(domainDipEdge);

                    AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                    alarmConnEdge.setFrom(sip);
                    alarmConnEdge.setTo(dip);
                    alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                    alarmConnEdge.setRankId(key);
                    edgeList.add(alarmConnEdge);

                    List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                    List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
                    Map<String, Object> labelMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(labelList)) {
                        labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                    }
                    labelMap.put("id", key + "_label");
                    labelMap.put("type", "LABELS");
                    labelMap.put("labels", labelsInfo);
                    sessionLabels.add(labelMap);

                    AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                    sipConnLabelEdge.setFrom(sip);
                    sipConnLabelEdge.setTo(key + "_label");
                    sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                    sipConnLabelEdge.setRankId(key);
                    edgeList.add(sipConnLabelEdge);

                    AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                    connDipLabelEdge.setFrom(key + "_label");
                    connDipLabelEdge.setTo(dip);
                    connDipLabelEdge.setLabel(StringUtils.EMPTY);
                    connDipLabelEdge.setRankId(key);
                    edgeList.add(connDipLabelEdge);

                    for (String domainIp : domainIpList) {
                        AlarmJudgeEdgeEntity dnsIpDomainIpEdge = new AlarmJudgeEdgeEntity();
                        dnsIpDomainIpEdge.setFrom(dip);
                        dnsIpDomainIpEdge.setTo(domainIp);
                        dnsIpDomainIpEdge.setLabel(StringUtils.EMPTY);
                        dnsIpDomainIpEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(dnsIpDomainIpEdge);
                        ipSet.add(domainIp);
                    }
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private void createRemoteTrojan(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        for (Map<String, Object> dataMap : connectionDataList) {
            String sip = (String) dataMap.get("sIp");
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                    sIpFingerEdge.setFrom(sip);
                    sIpFingerEdge.setTo(sslFingerVertex.getVid());
                    sIpFingerEdge.setLabel(StringUtils.EMPTY);
                    sIpFingerEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpFingerEdge);

                    AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                    fingerDipEdge.setFrom(sslFingerVertex.getVid());
                    fingerDipEdge.setTo(dip);
                    fingerDipEdge.setLabel(StringUtils.EMPTY);
                    fingerDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(fingerDipEdge);
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private void createIllegalExtraConn(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        for (Map<String, Object> dataMap : connectionDataList) {
            String sip = (String) dataMap.get("sIp");
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                    sIpFingerEdge.setFrom(sip);
                    sIpFingerEdge.setTo(sslFingerVertex.getVid());
                    sIpFingerEdge.setLabel(StringUtils.EMPTY);
                    sIpFingerEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpFingerEdge);

                    AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                    fingerDipEdge.setFrom(sslFingerVertex.getVid());
                    fingerDipEdge.setTo(dip);
                    fingerDipEdge.setLabel(StringUtils.EMPTY);
                    fingerDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(fingerDipEdge);
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private void craeteConvertTunnel(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();
        Set<String> keySet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        for (Map<String, Object> dataMap : connectionDataList) {
            String sip = (String) dataMap.get("sIp");
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            if (!keySet.contains(key)) {
                AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                alarmConnEdge.setFrom(sip);
                alarmConnEdge.setTo(dip);
                alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                alarmConnEdge.setRankId(key);
                edgeList.add(alarmConnEdge);
                keySet.add(key);

                List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
                Map<String, Object> labelMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(labelList)) {
                    labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                }
                labelMap.put("id", key + "_label");
                labelMap.put("type", "LABELS");
                labelMap.put("labels", labelsInfo);
                sessionLabels.add(labelMap);

                AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                sipConnLabelEdge.setFrom(sip);
                sipConnLabelEdge.setTo(key + "_label");
                sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                sipConnLabelEdge.setRankId(key);
                edgeList.add(sipConnLabelEdge);

                AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                connDipLabelEdge.setFrom(key + "_label");
                connDipLabelEdge.setTo(dip);
                connDipLabelEdge.setLabel(StringUtils.EMPTY);
                connDipLabelEdge.setRankId(key);
                edgeList.add(connDipLabelEdge);
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private void createMiningVirus(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        for (Map<String, Object> dataMap : connectionDataList) {
            String sip = (String) dataMap.get("sIp");
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity routeVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpRouteEdge = new AlarmJudgeEdgeEntity();
                    sIpRouteEdge.setFrom(sip);
                    sIpRouteEdge.setTo(routeVertex.getVid());
                    sIpRouteEdge.setLabel(StringUtils.EMPTY);
                    sIpRouteEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpRouteEdge);

                    AlarmJudgeEdgeEntity routeDipEdge = new AlarmJudgeEdgeEntity();
                    routeDipEdge.setFrom(routeVertex.getVid());
                    routeDipEdge.setTo(dip);
                    routeDipEdge.setLabel(StringUtils.EMPTY);
                    routeDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(routeDipEdge);
                }
            }
        }
        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private void createRandomFinger(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        vertexList.addAll(routeVertexList);
        Set<String> ipSet = new HashSet<>();

        List<Map<String, Object>> connectionDataList = getAlarmConnectionDataList(alarmMap);
        if (CollectionUtils.isEmpty(connectionDataList)) {
            return;
        }
        Map<String, Object> dataMap = connectionDataList.get(0);
        String sip = (String) dataMap.get("sIp");
        String dip = (String) dataMap.get("dIp");
        String key = sip + "_" + dip;
        ipSet.add(sip);
        ipSet.add(dip);

        AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
        alarmConnEdge.setFrom(sip);
        alarmConnEdge.setTo(dip);
        alarmConnEdge.setLabel((String) dataMap.get("AppName"));
        alarmConnEdge.setRankId(key);
        edgeList.add(alarmConnEdge);

        List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
        List<AnalysisLabelInfoEntity> labelsInfo = new ArrayList<>();
        Map<String, Object> labelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labelList)) {
            labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
        }
        labelMap.put("id", key + "_label");
        labelMap.put("type", "LABELS");
        labelMap.put("labels", labelsInfo);
        sessionLabels.add(labelMap);

        AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
        sipConnLabelEdge.setFrom(sip);
        sipConnLabelEdge.setTo(key + "_label");
        sipConnLabelEdge.setLabel(StringUtils.EMPTY);
        sipConnLabelEdge.setRankId(key);
        edgeList.add(sipConnLabelEdge);

        AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
        connDipLabelEdge.setFrom(key + "_label");
        connDipLabelEdge.setTo(dip);
        connDipLabelEdge.setLabel(StringUtils.EMPTY);
        connDipLabelEdge.setRankId(key);
        edgeList.add(connDipLabelEdge);

        if (CollectionUtils.isNotEmpty(routeVertexList)) {
            for (AlarmJudgeVertexEntity sllFingerVertex : routeVertexList) {
                AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                sIpFingerEdge.setFrom(sip);
                sIpFingerEdge.setTo(sllFingerVertex.getVid());
                sIpFingerEdge.setLabel(StringUtils.EMPTY);
                sIpFingerEdge.setRankId(StringUtils.EMPTY);
                edgeList.add(sIpFingerEdge);

                AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                fingerDipEdge.setFrom(sllFingerVertex.getVid());
                fingerDipEdge.setTo(dip);
                fingerDipEdge.setLabel(StringUtils.EMPTY);
                fingerDipEdge.setRankId(StringUtils.EMPTY);
                edgeList.add(fingerDipEdge);
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    private Map<String, Object> handleJudgeGraphResult(Map<String, Object> resuleMap) {
        List<AlarmJudgeEdgeEntity> edgeList = (List<AlarmJudgeEdgeEntity>) resuleMap.get("edge");
        if(edgeList != null){
            List<AlarmJudgeEdgeEntity> newEdgeList = new ArrayList<>();
            Set<String> edgeSet = new HashSet<>();
            for (AlarmJudgeEdgeEntity edge : edgeList) {
                String edgeStr = edge.getFrom() + "_" + edge.getTo();
                if (!edgeSet.contains(edgeStr)) {
                    newEdgeList.add(edge);
                    edgeSet.add(edgeStr);
                }
            }
            resuleMap.put("edge", newEdgeList);
        }

        List<Map<String, Object>> sessionLabelList = (List<Map<String, Object>>) resuleMap.get("label_vertex");
        if(sessionLabelList != null){
            List<Map<String, Object>> newSessionLabelList = new ArrayList<>();
            Set<String> sessionLabelSet = new HashSet<>();
            for (Map<String, Object> sessionLabel : sessionLabelList) {
                String sessionLabelStr = (String) sessionLabel.get("id");
                if (!sessionLabelSet.contains(sessionLabelStr)) {
                    newSessionLabelList.add(sessionLabel);
                    sessionLabelSet.add(sessionLabelStr);
                }
            }
            resuleMap.put("label_vertex", newSessionLabelList);
        }
        return resuleMap;
    }
}