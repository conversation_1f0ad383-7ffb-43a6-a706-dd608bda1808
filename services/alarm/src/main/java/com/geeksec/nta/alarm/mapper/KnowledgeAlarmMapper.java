package com.geeksec.nta.alarm.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.nta.alarm.entity.KnowledgeAlarm;
import com.geeksec.nta.alarm.entity.ModelAttackInfo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import com.geeksec.nta.alarm.vo.KnowledgeTypeVo;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DS("nta-mysql")
@Mapper
public interface KnowledgeAlarmMapper extends BaseMapper<KnowledgeAlarm> {


    /**
     * 获取完整的规则/知识库 列表
     *
     * @return
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList(@Param("userId")Integer userId);

    /**
     * 获取告警的攻击类型    过滤规则默认的attackType为-1  attackTypeName=规则
     *
     * @return
     */
    List<KnowledgeTypeVo> getKnowledgeType();

    /**
     * 计算当前攻击链路的组类是否存在
     *
     * @param attackerIp
     * @param victimIp
     * @param labelStr
     * @return
     */
    long countAttackChain(@Param("attackerIp") String attackerIp, @Param("victimIp") String victimIp, @Param("labelStr") String labelStr);

    /**
     * 插入攻击链数据
     *
     * @param attackerIp
     * @param victimIp
     * @param labelStr
     */
    void insertAttackChain(@Param("attackerIp") String attackerIp, @Param("victimIp") String victimIp, @Param("labelStr") String labelStr);

    /**
     * 获取全量模型告警攻击链字典
     *
     * @return
     */
    List<ModelAttackInfo> getAttackChainMap();
}
