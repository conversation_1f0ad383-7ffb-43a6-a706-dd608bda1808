package com.geeksec.model.service.impl;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.geeksec.common.core.exceptions.BusinessException;
import com.geeksec.model.dto.ModelCreateRequest;
import com.geeksec.model.dto.ModelQueryCondition;
import com.geeksec.model.dto.ModelResponse;
import com.geeksec.model.dto.ModelStateUpdateRequest;
import com.geeksec.model.dto.ModelStatistics;
import com.geeksec.model.dto.ModelUpdateRequest;
import com.geeksec.model.entity.ModelInfo;
import com.geeksec.model.enums.ModelStateEnum;
import com.geeksec.model.repository.ModelInfoMapper;
import com.geeksec.model.service.ModelService;
import com.mybatisflex.core.paginate.Page;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 模型管理服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelServiceImpl implements ModelService {

    private final ModelInfoMapper modelInfoMapper;
    private final KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * 模型状态变更主题（从配置文件读取）
     */
    @Value("${model.kafka.topics.model-state-changed:model_state_changed}")
    private String modelStateChangedTopic;

    @Override
    public Page<ModelResponse> getModelPage(ModelQueryCondition condition) {
        try {
            Page<ModelInfo> page = Page.of(condition.getCurrentPage(), condition.getPageSize());
            Page<ModelInfo> result = modelInfoMapper.selectModelPage(page, condition);
            
            return result.map(this::convertToResponse);
        } catch (Exception e) {
            log.error("分页查询模型列表失败，condition: {}", condition, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public List<ModelResponse> getModelList(ModelQueryCondition condition) {
        try {
            List<ModelInfo> modelInfos = modelInfoMapper.selectModelList(condition);
            return modelInfos.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询模型列表失败，condition: {}", condition, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public ModelResponse getModelById(Integer modelId) {
        if (modelId == null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        
        try {
            ModelInfo modelInfo = modelInfoMapper.selectOneById(modelId);
            if (modelInfo == null || modelInfo.getIsDeleted() == 1) {
                return null;
            }
            return convertToResponse(modelInfo);
        } catch (Exception e) {
            log.error("根据ID查询模型失败，modelId: {}", modelId, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public ModelResponse getModelByName(String modelName) {
        if (StringUtils.isBlank(modelName)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        
        try {
            ModelInfo modelInfo = modelInfoMapper.selectByModelName(modelName.trim());
            if (modelInfo == null) {
                return null;
            }
            return convertToResponse(modelInfo);
        } catch (Exception e) {
            log.error("根据名称查询模型失败，modelName: {}", modelName, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createModel(ModelCreateRequest request, String operator) {
        validateCreateRequest(request);
        
        // 检查模型名称是否已存在
        if (isModelNameExists(request.getModelName(), null)) {
            throw new BusinessException(ErrorCode.MODEL_NAME_DUPLICATE);
        }
        
        try {
            ModelInfo modelInfo = ModelInfo.builder()
                    .modelName(request.getModelName().trim())
                    .modelAlgorithm(request.getModelAlgorithm())
                    .modelType(request.getModelType())
                    .modelVersion(request.getModelVersion())
                    .remark(request.getRemark())
                    .state(request.getState())
                    .modelConfig(request.getModelConfig())
                    .modelPath(request.getModelPath())
                    .priority(request.getPriority() != null ? request.getPriority() : 100)
                    .tags(request.getTags())
                    .createdTime(LocalDateTime.now())
                    .updatedTime(LocalDateTime.now())
                    .createdBy(operator)
                    .updatedBy(operator)
                    .isDeleted(0)
                    .build();
            
            modelInfoMapper.insert(modelInfo);
            
            log.info("创建模型成功，modelId: {}, modelName: {}, operator: {}", 
                    modelInfo.getModelId(), modelInfo.getModelName(), operator);
            
            return modelInfo.getModelId();
        } catch (Exception e) {
            log.error("创建模型失败，request: {}, operator: {}", request, operator, e);
            throw new BusinessException(ErrorCode.MODEL_ADD_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateModel(ModelUpdateRequest request, String operator) {
        validateUpdateRequest(request);
        
        ModelInfo existingModel = modelInfoMapper.selectOneById(request.getModelId());
        if (existingModel == null || existingModel.getIsDeleted() == 1) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        
        // 检查模型名称是否已存在（排除当前模型）
        if (StringUtils.isNotBlank(request.getModelName()) && 
            isModelNameExists(request.getModelName(), request.getModelId())) {
            throw new BusinessException(ErrorCode.MODEL_NAME_DUPLICATE);
        }
        
        try {
            ModelInfo.ModelInfoBuilder builder = ModelInfo.builder()
                    .modelId(request.getModelId())
                    .updatedTime(LocalDateTime.now())
                    .updatedBy(operator);
            
            // 只更新非空字段
            if (StringUtils.isNotBlank(request.getModelName())) {
                builder.modelName(request.getModelName().trim());
            }
            if (StringUtils.isNotBlank(request.getModelAlgorithm())) {
                builder.modelAlgorithm(request.getModelAlgorithm());
            }
            if (StringUtils.isNotBlank(request.getModelType())) {
                builder.modelType(request.getModelType());
            }
            if (StringUtils.isNotBlank(request.getModelVersion())) {
                builder.modelVersion(request.getModelVersion());
            }
            if (StringUtils.isNotBlank(request.getRemark())) {
                builder.remark(request.getRemark());
            }
            if (StringUtils.isNotBlank(request.getModelConfig())) {
                builder.modelConfig(request.getModelConfig());
            }
            if (StringUtils.isNotBlank(request.getModelPath())) {
                builder.modelPath(request.getModelPath());
            }
            if (request.getPriority() != null) {
                builder.priority(request.getPriority());
            }
            if (StringUtils.isNotBlank(request.getTags())) {
                builder.tags(request.getTags());
            }
            
            ModelInfo modelInfo = builder.build();
            int result = modelInfoMapper.update(modelInfo);
            
            if (result > 0) {
                log.info("更新模型成功，modelId: {}, operator: {}", request.getModelId(), operator);
                return true;
            } else {
                log.warn("更新模型失败，没有找到对应记录，modelId: {}", request.getModelId());
                return false;
            }
        } catch (Exception e) {
            log.error("更新模型失败，request: {}, operator: {}", request, operator, e);
            throw new BusinessException(ErrorCode.UPDATE_MODEL_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateModelState(ModelStateUpdateRequest request, String operator) {
        validateStateUpdateRequest(request);

        try {
            int result = modelInfoMapper.updateModelState(request.getModelId(), request.getState(), operator);

            if (result > 0) {
                log.info("更新模型状态成功，modelId: {}, state: {}, operator: {}",
                        request.getModelId(), request.getState(), operator);

                // 发送Kafka消息通知状态变更
                sendModelStateChangeMessage(request.getModelId(), request.getState());

                return true;
            } else {
                log.warn("更新模型状态失败，没有找到对应记录，modelId: {}", request.getModelId());
                return false;
            }
        } catch (Exception e) {
            log.error("更新模型状态失败，request: {}, operator: {}", request, operator, e);
            throw new BusinessException(ErrorCode.MODEL_STATE_CHANGE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteModel(Integer modelId, String operator) {
        if (modelId == null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        try {
            int result = modelInfoMapper.softDeleteModel(modelId, operator);

            if (result > 0) {
                log.info("删除模型成功，modelId: {}, operator: {}", modelId, operator);
                return true;
            } else {
                log.warn("删除模型失败，没有找到对应记录，modelId: {}", modelId);
                return false;
            }
        } catch (Exception e) {
            log.error("删除模型失败，modelId: {}, operator: {}", modelId, operator, e);
            throw new BusinessException(ErrorCode.MODEL_DELETE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateModelState(List<Integer> modelIds, Integer state, String operator) {
        if (modelIds == null || modelIds.isEmpty()) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (!ModelStateEnum.isValidCode(state)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }

        try {
            int result = modelInfoMapper.batchUpdateModelState(modelIds, state, operator);

            if (result > 0) {
                log.info("批量更新模型状态成功，modelIds: {}, state: {}, operator: {}",
                        modelIds, state, operator);

                // 批量发送Kafka消息
                for (Integer modelId : modelIds) {
                    sendModelStateChangeMessage(modelId, state);
                }

                return true;
            } else {
                log.warn("批量更新模型状态失败，没有找到对应记录，modelIds: {}", modelIds);
                return false;
            }
        } catch (Exception e) {
            log.error("批量更新模型状态失败，modelIds: {}, state: {}, operator: {}",
                    modelIds, state, operator, e);
            throw new BusinessException(ErrorCode.MODEL_STATE_CHANGE_ERROR);
        }
    }

    @Override
    public List<ModelResponse> getModelsByState(Integer state) {
        if (!ModelStateEnum.isValidCode(state)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }

        try {
            List<ModelInfo> modelInfos = modelInfoMapper.selectByState(state);
            return modelInfos.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据状态查询模型列表失败，state: {}", state, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public List<ModelResponse> getModelsByAlgorithm(String algorithm) {
        if (StringUtils.isBlank(algorithm)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        try {
            List<ModelInfo> modelInfos = modelInfoMapper.selectByAlgorithm(algorithm.trim());
            return modelInfos.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据算法类型查询模型列表失败，algorithm: {}", algorithm, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public List<ModelResponse> getModelsByType(String modelType) {
        if (StringUtils.isBlank(modelType)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        try {
            List<ModelInfo> modelInfos = modelInfoMapper.selectByModelType(modelType.trim());
            return modelInfos.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据模型类型查询模型列表失败，modelType: {}", modelType, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public List<ModelResponse> getModelsByTag(String tag) {
        if (StringUtils.isBlank(tag)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        try {
            List<ModelInfo> modelInfos = modelInfoMapper.selectByTag(tag.trim());
            return modelInfos.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据标签查询模型列表失败，tag: {}", tag, e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public ModelStatistics getModelStatistics() {
        try {
            long totalCount = modelInfoMapper.countByState(null);
            long enabledCount = modelInfoMapper.countByState(ModelStateEnum.ENABLED.getCode());
            long disabledCount = modelInfoMapper.countByState(ModelStateEnum.DISABLED.getCode());

            double enabledRate = totalCount > 0 ? (double) enabledCount / totalCount : 0.0;

            return ModelStatistics.builder()
                    .totalCount(totalCount)
                    .enabledCount(enabledCount)
                    .disabledCount(disabledCount)
                    .enabledRate(enabledRate)
                    .build();
        } catch (Exception e) {
            log.error("获取模型统计信息失败", e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public Boolean isModelNameExists(String modelName, Integer excludeId) {
        if (StringUtils.isBlank(modelName)) {
            return false;
        }

        try {
            ModelInfo existingModel = modelInfoMapper.selectByModelName(modelName.trim());
            if (existingModel == null) {
                return false;
            }

            // 如果指定了排除ID，则检查是否为同一个模型
            if (excludeId != null && existingModel.getModelId().equals(excludeId)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("检查模型名称是否存在失败，modelName: {}, excludeId: {}", modelName, excludeId, e);
            return false;
        }
    }

    @Override
    public Boolean validateModelConfig(String modelConfig) {
        if (StringUtils.isBlank(modelConfig)) {
            return true; // 空配置认为是有效的
        }

        try {
            JSON.parseObject(modelConfig);
            return true;
        } catch (JSONException e) {
            log.warn("模型配置JSON格式无效，modelConfig: {}", modelConfig, e);
            return false;
        }
    }

    /**
     * 发送模型状态变更消息到Kafka
     *
     * @param modelId 模型ID
     * @param state 新状态
     */
    private void sendModelStateChangeMessage(Integer modelId, Integer state) {
        try {
            // 构建消息内容，格式与NTA 2.0保持一致
            Map<String, Object> message = new HashMap<>();
            message.put(modelId.toString(), state);
            String jsonMessage = JSON.toJSONString(message);

            CompletableFuture<SendResult<String, Object>> future =
                    kafkaTemplate.send(modelStateChangedTopic, "model_state_change", jsonMessage);

            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.warn("模型状态变更Kafka消息发送失败，modelId: {}, state: {}", modelId, state, throwable);
                } else {
                    log.debug("模型状态变更Kafka消息发送成功，modelId: {}, state: {}", modelId, state);
                }
            });
        } catch (Exception e) {
            log.error("发送模型状态变更Kafka消息异常，modelId: {}, state: {}", modelId, state, e);
        }
    }

    /**
     * 转换实体为响应DTO
     *
     * @param modelInfo 模型实体
     * @return 响应DTO
     */
    private ModelResponse convertToResponse(ModelInfo modelInfo) {
        if (modelInfo == null) {
            return null;
        }

        ModelStateEnum stateEnum = ModelStateEnum.getByCode(modelInfo.getState());
        String stateDesc = stateEnum != null ? stateEnum.getDescription() : "未知";

        return ModelResponse.builder()
                .modelId(modelInfo.getModelId())
                .modelName(modelInfo.getModelName())
                .modelAlgorithm(modelInfo.getModelAlgorithm())
                .modelType(modelInfo.getModelType())
                .modelVersion(modelInfo.getModelVersion())
                .remark(modelInfo.getRemark())
                .state(modelInfo.getState())
                .stateDesc(stateDesc)
                .modelConfig(modelInfo.getModelConfig())
                .modelPath(modelInfo.getModelPath())
                .modelHash(modelInfo.getModelHash())
                .priority(modelInfo.getPriority())
                .tags(modelInfo.getTags())
                .createdTime(modelInfo.getCreatedTime())
                .updatedTime(modelInfo.getUpdatedTime())
                .createdBy(modelInfo.getCreatedBy())
                .updatedBy(modelInfo.getUpdatedBy())
                .build();
    }

    /**
     * 验证创建请求
     *
     * @param request 创建请求
     */
    private void validateCreateRequest(ModelCreateRequest request) {
        if (request == null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (StringUtils.isBlank(request.getModelName())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (StringUtils.isBlank(request.getModelAlgorithm())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (request.getState() == null || !ModelStateEnum.isValidCode(request.getState())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }

        if (StringUtils.isNotBlank(request.getModelConfig()) &&
            !validateModelConfig(request.getModelConfig())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
    }

    /**
     * 验证更新请求
     *
     * @param request 更新请求
     */
    private void validateUpdateRequest(ModelUpdateRequest request) {
        if (request == null || request.getModelId() == null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (StringUtils.isNotBlank(request.getModelConfig()) &&
            !validateModelConfig(request.getModelConfig())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
    }

    /**
     * 验证状态更新请求
     *
     * @param request 状态更新请求
     */
    private void validateStateUpdateRequest(ModelStateUpdateRequest request) {
        if (request == null || request.getModelId() == null || request.getState() == null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        if (!ModelStateEnum.isValidCode(request.getState())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
    }
}
