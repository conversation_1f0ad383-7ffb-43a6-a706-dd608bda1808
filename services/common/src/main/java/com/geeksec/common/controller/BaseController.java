package com.geeksec.common.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import com.geeksec.common.core.enums.ErrorCode;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.dto.BasePageRequest;
import com.mybatisflex.core.paginate.Page;

import cn.hutool.http.HttpStatus;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用控制器基类
 * 
 * 提供通用的响应包装方法和工具方法，各服务控制器可以继承此类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public abstract class BaseController {

    // ==================== 响应包装方法 ====================

    /**
     * 返回成功响应
     * 
     * @param data 数据
     * @param <T> 数据类型
     * @return API响应
     */
    protected <T> ApiResponse<T> success(T data) {
        return ApiResponse.success(data);
    }

    /**
     * 返回成功响应（无数据）
     * 
     * @return API响应
     */
    protected ApiResponse<Void> success() {
        return ApiResponse.success();
    }

    /**
     * 返回成功响应（自定义消息）
     *
     * @param message 成功消息
     * @return API响应
     */
    protected ApiResponse<String> success(String message) {
        return ApiResponse.success(message);
    }

    /**
     * 返回成功响应（自定义消息和数据）
     * 
     * @param message 成功消息
     * @param data 数据
     * @param <T> 数据类型
     * @return API响应
     */
    protected <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.success(message, data);
    }

    /**
     * 返回错误响应
     * 
     * @param message 错误信息
     * @return API响应
     */
    protected ApiResponse<Void> error(String message) {
        return ApiResponse.error(message);
    }

    /**
     * 返回错误响应
     * 
     * @param code 错误码
     * @param message 错误信息
     * @return API响应
     */
    protected ApiResponse<Void> error(Integer code, String message) {
        return ApiResponse.error(code, message);
    }

    /**
     * 返回错误响应（使用结果码枚举）
     *
     * @param errorCode 错误码枚举
     * @return 错误响应
     */
    protected ApiResponse<Void> error(ErrorCode errorCode) {
        return ApiResponse.error(errorCode.getCode(), errorCode.getMessage());
    }

    /**
     * 返回分页成功响应
     * 
     * @param page 分页数据
     * @param <T> 数据类型
     * @return API响应
     */
    protected <T> ApiResponse<Page<T>> successPage(Page<T> page) {
        return ApiResponse.success(page);
    }

    // ==================== 参数验证方法 ====================

    /**
     * 检查参数验证结果
     * 
     * @param bindingResult 验证结果
     * @return 如果有错误则返回错误响应，否则返回null
     */
    protected ApiResponse<Void> checkValidation(BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<String> errors = bindingResult.getFieldErrors()
                    .stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.toList());
            return error(HttpStatus.HTTP_BAD_REQUEST, String.join(", ", errors));
        }
        return null;
    }

    /**
     * 验证必填参数
     *
     * @param value 参数值
     * @param paramName 参数名称
     * @return 如果参数为空则返回错误响应，否则返回null
     */
    protected ApiResponse<Void> validateRequired(Object value, String paramName) {
        if (value == null || (value instanceof String && org.apache.commons.lang3.StringUtils.isEmpty((String) value))) {
            return error(HttpStatus.HTTP_BAD_REQUEST, paramName + "不能为空");
        }
        return null;
    }

    /**
     * 验证ID参数
     * 
     * @param id ID值
     * @return 如果ID无效则返回错误响应，否则返回null
     */
    protected ApiResponse<Void> validateId(Long id) {
        if (id == null || id <= 0) {
            return error(HttpStatus.HTTP_BAD_REQUEST, "ID参数无效");
        }
        return null;
    }

    /**
     * 验证分页参数
     * 
     * @param pageRequest 分页请求
     * @return 如果参数无效则返回错误响应，否则返回null
     */
    protected ApiResponse<Void> validatePageRequest(BasePageRequest pageRequest) {
        if (pageRequest == null) {
            return error(HttpStatus.HTTP_BAD_REQUEST, "分页参数不能为空");
        }
        
        if (pageRequest.getPageNum() == null || pageRequest.getPageNum() < 1) {
            return error(HttpStatus.HTTP_BAD_REQUEST, "页码必须大于0");
        }
        
        if (pageRequest.getPageSize() == null || pageRequest.getPageSize() < 1 || pageRequest.getPageSize() > 100) {
            return error(HttpStatus.HTTP_BAD_REQUEST, "每页大小必须在1-100之间");
        }
        
        return null;
    }

    // ==================== 工具方法 ====================

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    protected String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (org.apache.commons.lang3.StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * 获取用户代理
     * 
     * @param request HTTP请求
     * @return 用户代理
     */
    protected String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * 记录操作日志
     * 
     * @param operation 操作名称
     * @param request HTTP请求
     */
    protected void logOperation(String operation, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        String userAgent = getUserAgent(request);
        log.info("操作日志 - 操作: {} | IP: {} | UA: {}", operation, clientIp, userAgent);
    }

    /**
     * 记录操作日志（带用户信息）
     * 
     * @param operation 操作名称
     * @param userId 用户ID
     * @param request HTTP请求
     */
    protected void logOperation(String operation, Long userId, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        String userAgent = getUserAgent(request);
        log.info("操作日志 - 操作: {} | 用户ID: {} | IP: {} | UA: {}", operation, userId, clientIp, userAgent);
    }
}
