package com.geeksec.common.enums;

import lombok.Getter;

/**
 * 告警实体角色枚举
 * 定义告警涉及实体在安全事件中的角色身份
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmEntityRole {

    /**
     * 未知角色
     */
    UNKNOWN(0, "未知角色"),

    /**
     * 攻击者
     */
    ATTACKER(1, "攻击者"),

    /**
     * 被攻击者
     */
    VICTIM(2, "被攻击者"),

    /**
     * 被控主机
     */
    COMPROMISED_HOST(4, "被控主机");

    private final int code;
    private final String description;

    AlarmEntityRole(int code, String description) {
        this.code = code;
        this.description = description;
    }



    /**
     * 根据代码获取告警实体角色
     *
     * @param code 角色代码
     * @return 告警实体角色
     */
    public static AlarmEntityRole fromCode(int code) {
        for (AlarmEntityRole role : values()) {
            if (role.code == code) {
                return role;
            }
        }
        throw new IllegalArgumentException("未知的告警实体角色代码: " + code);
    }
}
