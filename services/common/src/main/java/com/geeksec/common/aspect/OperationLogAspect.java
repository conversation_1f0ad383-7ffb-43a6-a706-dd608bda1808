package com.geeksec.common.aspect;

import com.geeksec.common.annotation.OperationLog;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * 操作日志切面
 * 用于拦截带有 {@link OperationLog} 注解的方法，记录操作日志
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    /**
     * 定义切点
     */
    @Pointcut("@annotation(com.geeksec.common.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 在方法返回后记录日志
     */
    @AfterReturning(pointcut = "operationLogPointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return;
        }

        HttpServletRequest request = attributes.getRequest();

        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog operationLog = method.getAnnotation(OperationLog.class);

        // 记录日志
        log.info("=== 操作日志 ===");
        log.info("时间: {}", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        log.info("模块: {}", operationLog.module());
        log.info("操作: {}", operationLog.operation());
        log.info("描述: {}", operationLog.description());
        log.info("方法: {}.{}", signature.getDeclaringTypeName(), signature.getName());
        log.info("请求IP: {}", request.getRemoteAddr());
        log.info("请求URL: {}", request.getRequestURL().toString());
        log.info("请求方式: {}", request.getMethod());
        log.info("请求参数: {}", Arrays.toString(joinPoint.getArgs()));
        log.info("返回结果: {}", result);
        log.info("================");

        // 这里可以扩展为将日志保存到数据库
    }
}
