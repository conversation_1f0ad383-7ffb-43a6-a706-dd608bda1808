package com.geeksec.common.metadata.enums;

import lombok.Getter;

/**
 * 捕获模式枚举
 * 系统级配置，跨模块共享
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum CaptureMode {
    
    /**
     * 旁路模式
     */
    BYPASS(0, "旁路模式"),
    
    /**
     * 串联模式
     */
    INLINE(1, "串联模式"),
    
    /**
     * 混合模式
     */
    HYBRID(2, "混合模式");
    
    private final int code;
    private final String description;
    
    CaptureMode(int code, String description) {
        this.code = code;
        this.description = description;
    }
    

    
    /**
     * 根据代码获取捕获模式
     * 
     * @param code 模式代码
     * @return 捕获模式
     */
    public static CaptureMode fromCode(int code) {
        for (CaptureMode mode : values()) {
            if (mode.code == code) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的捕获模式代码: " + code);
    }
}
