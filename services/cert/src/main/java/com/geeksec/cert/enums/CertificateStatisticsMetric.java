package com.geeksec.cert.enums;

import lombok.Getter;

/**
 * 证书统计指标枚举
 * 定义证书模块中各种统计分析的指标维度
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum CertificateStatisticsMetric {
    
    /**
     * 证书数量统计
     */
    CERTIFICATE_COUNT(0, "证书数量统计", "统计系统中证书的总数量"),
    
    /**
     * 用户导入证书标签统计
     */
    USER_IMPORTED_LABELS(1, "用户导入证书标签统计", "统计用户手动导入的证书标签数量和分布"),
    
    /**
     * 证书算法和密钥长度分布统计
     */
    ALGORITHM_KEY_LENGTH_DISTRIBUTION(2, "证书算法和密钥长度分布统计", "统计不同加密算法和密钥长度的证书数量分布"),
    
    /**
     * 基于签发机构的证书分布统计
     */
    ISSUER_DISTRIBUTION(3, "基于签发机构的证书分布统计", "统计不同证书颁发机构(CA)签发的证书数量分布"),
    
    /**
     * 基于信任等级的证书分布统计
     */
    TRUST_LEVEL_DISTRIBUTION(4, "基于信任等级的证书分布统计", "统计基于黑白名单权值的证书信任等级分布");
    
    private final int code;
    private final String name;
    private final String description;
    
    CertificateStatisticsMetric(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    

    
    /**
     * 根据代码获取证书统计指标
     *
     * @param code 指标代码
     * @return 证书统计指标
     */
    public static CertificateStatisticsMetric fromCode(int code) {
        for (CertificateStatisticsMetric metric : values()) {
            if (metric.code == code) {
                return metric;
            }
        }
        throw new IllegalArgumentException("未知的证书统计指标代码: " + code);
    }
    
    /**
     * 根据名称获取证书统计指标
     *
     * @param name 指标名称
     * @return 证书统计指标
     */
    public static CertificateStatisticsMetric fromName(String name) {
        if (name == null) {
            return null;
        }
        for (CertificateStatisticsMetric metric : values()) {
            if (metric.name.equals(name)) {
                return metric;
            }
        }
        return null;
    }
    
    /**
     * 检查代码是否有效
     *
     * @param code 指标代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
