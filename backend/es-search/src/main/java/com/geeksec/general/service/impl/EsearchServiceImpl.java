package com.geeksec.general.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.search.*;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.*;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Description：
 */
@Component
public class EsearchServiceImpl implements EsearchService {

    private static final Logger logger = LoggerFactory.getLogger(EsearchServiceImpl.class);
    private RestHighLevelClient client = null;
    private volatile long lastBuildTime = 0L;

    // ES Host 列表 [***************:9200,***************:9100]
    @Value("${elasticsearch.hosts}")
    private String[] esHosts;

    @Autowired
    private ThreadPoolTaskExecutor myExecutor;

    // 线程池最大数量
    private static final int THREAD_POOL_SIZE = 50;
    private static final int CLIENT_POOL_SIZE = 20;
    private static final ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

    //    @PostConstruct()
    private void init() {
        try {
            createClient();
        } catch (Exception e) {
            logger.error("failed to init es client");
        }
    }

    private void close() {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                logger.error("close es client failed");
            }
        }
    }


    private synchronized void createClient() {
        if (lastBuildTime > 0L && System.currentTimeMillis() - lastBuildTime < 1000 * 20) {
            return;
        }// 20秒内不进行重复创建
        try {
            if (client == null) {
                logger.info("start create elasticsearch client");
                client = initClient();
            } else {
                boolean ping = client.ping(RequestOptions.DEFAULT);
                if (ping) {
                    return;
                } else {
                    close();
                    client = initClient();
                }
            }
        } catch (IOException e) {
            logger.warn("create elasticsearch client failed:", e);
        } finally {
            lastBuildTime = System.currentTimeMillis();
        }
    }

    // 通过参数创建 RestHighLevelClient
    private RestHighLevelClient initClient() throws IOException {
        HttpHost[] addrInfo = new HttpHost[esHosts.length];
        for (int i = 0; i < esHosts.length; i++) {
            String esAddr = esHosts[i].split(":")[0];
            Integer esPort = Integer.parseInt(esHosts[i].split(":")[1]);
            addrInfo[i] = new HttpHost(esAddr, esPort, "http");
        }
        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(RestClient.builder(addrInfo).setRequestConfigCallback(
                // 设置连接超时时间
                new RestClientBuilder.RequestConfigCallback() {
                    @Override
                    public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder requestConfigBuilder) {
                        return requestConfigBuilder.setConnectTimeout(2*60*1000)
                                .setSocketTimeout(5*60*1000)
                                .setContentCompressionEnabled(true);
                    }
                }
        ).setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpAsyncClientBuilder) {
                return httpAsyncClientBuilder
                        .setMaxConnTotal(100) // 设置最大连接数
                        .setMaxConnPerRoute(100) // 设置每条路由的最大连接数
                        .setDefaultIOReactorConfig(IOReactorConfig.custom()
                                .setIoThreadCount(CLIENT_POOL_SIZE)
                                .setSoKeepAlive(true)
                                .build());
            }
        }));
        boolean ping = restHighLevelClient.ping(RequestOptions.DEFAULT);
        if (ping) {
            logger.info("elasticsearch client created success!");
        }
        return restHighLevelClient;

    }

    @Override
    public long indexCount(String protocolName) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }
        if (StringUtils.isEmpty(protocolName)) {

        }
        CountRequest countRequest = new CountRequest(protocolName);
        try {
            CountResponse response = client.count(countRequest, RequestOptions.DEFAULT);
            if (response == null) {
                return 0;
            }
            logger.info("查询到索引\"{}\"当前数量为{}", protocolName, response.getCount());
            return response.getCount();
        } catch (Exception e) {
            logger.error("查询索引\"{}\"文档数量失败:", protocolName, e);
            createClient();
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> fuzzySearch(String indexName, String key, String domain, String sub, int page, int pageSize) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 组装查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.fuzzyQuery(key, domain));
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from((page - 1) * pageSize).size(pageSize);
        searchRequest.source(searchSourceBuilder);

        List<Map<String, Object>> result = normalSearch(searchRequest);
        return result;
    }

    // 普通Default模式搜索ES数据
    @Override
    public List<Map<String, Object>> normalSearch(SearchRequest searchRequest) {

        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
//            //System.out.ut.println(searchRequest.source());
            List<Map<String, Object>> list = new ArrayList<>();
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> result = hit.getSourceAsMap();
                list.add(result);
            }
            return list;
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("Default模式查询ES数据库错误:", e);
            return null;
        }
    }

    @Override
    // 聚合查询
    public Aggregations aggrSearch(SearchRequest searchRequest) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            if (response.getAggregations() != null) {
                Aggregations aggregations = response.getAggregations();
                return aggregations;
            } else {
                logger.warn("聚合查询数据为空,request is :{}", searchRequest);
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("聚合查询ES数据错误", e);
        }
        return null;
    }

    @Override
    public SearchResponse esSearch(SearchRequest searchRequest) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            return searchResponse;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Default模式查询ES数据库错误:", e);
            return null;
        }
    }

    @Override
    public ResultVo<List<String>> searchEsIds(Integer totalSize, Integer esLimit, BoolQueryBuilder boolQueryBuilder, List<String> indexNames, String orderField, String fieldName) {
        // 判断当前需要多少查询线程
        Integer threadNum = 0;
        if (totalSize <= esLimit) {
            // 需要聚合的数据量小于1W的情况
            threadNum = 1;
        } else {
            // 取模数进行线程数的确定
            threadNum = totalSize / esLimit;
            if ((totalSize % esLimit) != 0) {
                threadNum += 1;
            }
        }

        List<String> resultIds = Collections.synchronizedList(Lists.newArrayListWithCapacity(totalSize));

        long startTime = System.currentTimeMillis();

        try {
            List<Future<List<String>>> futures = new ArrayList<>();
            // 初始化查询次数
            for (int i = 0; i < threadNum; i++) {
                int offset = i * esLimit;
                Future<List<String>> future = executor.submit(() -> {
                    return doQuery(client, boolQueryBuilder, indexNames, offset, orderField, fieldName);
                });
                futures.add(future);
            }

            for (Future<List<String>> future : futures) {
                resultIds.addAll(future.get());
            }
            long endTime = System.currentTimeMillis();
            logger.info("多线程查询ES数据成功，使用{}条线程进行查询，耗时: {}", endTime - startTime);

            executor.shutdown();

        } catch (Exception e) {
            logger.error("异步查询ES ID集合失败!");
            System.out.println(e);
        }

        return ResultVo.success(resultIds);
    }

    private static List<String> doQuery(RestHighLevelClient client, BoolQueryBuilder boolQueryBuilder, List<String> indexNames, int offset, String orderField, String fieldName) throws ExecutionException, InterruptedException {
        // 1.创建查询对象
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]));

        // 2.封装查询条件
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort(orderField, SortOrder.DESC);
        searchSourceBuilder.from(offset);
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.fetchSource(new String[]{fieldName.equals("_id") ? "id" : fieldName}, null);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(90));
        searchRequest.source(searchSourceBuilder);

        CompletableFuture<SearchResponse> future = new CompletableFuture<>();
        client.searchAsync(searchRequest, new ActionListener<SearchResponse>() {
            @Override
            public void onResponse(SearchResponse response) {
                future.complete(response);
            }

            @Override
            public void onFailure(Exception e) {
                future.completeExceptionally(e);
            }
        });
        List<String> ids = new ArrayList<>();
        SearchResponse searchResponse = future.get();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            ids.add(hit.getId());
        }
        return ids;
    }

    @Override
    public List<Map<String, Object>> scrollSearch(SearchRequest searchRequest) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        List<Map<String, Object>> list = new ArrayList<>();
        list.clear();

        // 1.处理scroll滚动查询的请求
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(5L));
        searchRequest.scroll(scroll);
        try {
            // 2.获取返回结果 scrollId, source
            logger.info("开始进行scroll对id进行查询,当前时间{}", new Date());
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);

            SearchHit[] searchHits = response.getHits().getHits();
            String scrollId = response.getScrollId();

            for (SearchHit hit : response.getHits().getHits()) {
                // 3. 首页的数据
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                sourceAsMap.put("id", hit.getId());
                list.add(sourceAsMap);
            }
            // 限定查询最多10000条
            Integer queryCount = 1;
            // 滚动查询部分，从10001条数据开始取
            while (searchHits != null && searchHits.length > 0) {
                if (queryCount == 10) {
                    break;
                }
                // 4.循环创建searchScrollRequest
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                // 5.再指定scroll的生存时间，若不指定会归零
                scrollRequest.scroll(TimeValue.timeValueMinutes(5L));
                // 6.执行查询获取结果
                response = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 7.判断是否查询到了数据输出
                SearchHit[] hits = response.getHits().getHits();
                if (hits != null && hits.length > 0) {
                    scrollId = response.getScrollId();
                    searchHits = hits;
                    for (SearchHit hit : hits) {
                        //循环输出
                        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                        sourceAsMap.put("id", hit.getId());
                        list.add(sourceAsMap);
                    }
                    queryCount++;
                } else {
                    //若无数据则退出
                    break;
                }
            }
            // 8、创建ClearScrollRequest
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            // 9、指定scrollId
            clearScrollRequest.addScrollId(scrollId);
            // 10、删除scrollId
            ClearScrollResponse clearScrollResponse = null;
            try {
                clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            } catch (Exception e) {
                logger.error("滚动查询删除失败!error->", e);
            }
            // 11、根据它返回判断删除成功没
            boolean succeeded = clearScrollResponse.isSucceeded();
        } catch (Exception e) {
            logger.info("分页查询失败,erorr->", e);
        }
        logger.info("scroll查询完成,当前时间->{}", new Date());

        return list;
    }

    @Override
    public UpdateResponse updateDoc(UpdateRequest updateRequest) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            UpdateResponse response = client.update(updateRequest, RequestOptions.DEFAULT);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("更新文档操作失败--->{}", e);
            return null;
        }
    }

    @Override
    public CountResponse esSearchForCount(CountRequest searchRequest) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
//            //System.out.ut.println(searchRequest.source());
            CountResponse searchResponse = client.count(searchRequest, RequestOptions.DEFAULT);
            return searchResponse;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("es查询数据量异常:", e);
            return null;
        }
    }


    @Override
    public BulkByScrollResponse esDelete(DeleteByQueryRequest request) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            BulkByScrollResponse response = client.deleteByQuery(request, RequestOptions.DEFAULT);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("es删除文档异常:", e);
            return null;
        }
    }

    @Override
    public ResultVo<List<String>> getEsIds(Integer size, Integer esLimit,
                                           BoolQueryBuilder boolQueryBuilder,
                                           List<String> indexNames, String orderField, String fieldName) {

        Integer from = 0;
        int threadNum = 0;
        if (esLimit <= size) {
            //1万以内不拆线程
            threadNum = 1;
        } else {
            //取余  取模确定线程数
            threadNum = esLimit / size;
            if ((esLimit % size) != 0) {
                threadNum += 1;
            }
        }
        List<String> resultIds = Collections.synchronizedList(Lists.newArrayListWithCapacity(esLimit));
        long t1 = System.currentTimeMillis();
        List<SearchSourceBuilder> requestList = new ArrayList<>();

        for (int i = 0; i < threadNum; i++) {
            //这里要求上游传输过来的参数最多切分10个线程
            if (from >= esLimit) {
                break;
            }
            ;
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            if (fieldName.equals("_id")) {
                searchSourceBuilder.fetchSource(false);
            } else {
                //这里目前只有SessionId
                searchSourceBuilder.fetchSource(fieldName, null);
            }
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            from += size;
            searchSourceBuilder.sort(orderField, SortOrder.DESC);
            //收集线程任务
            requestList.add(searchSourceBuilder);
        }
        //es的请求url
        String url = StringUtils.join(indexNames, ",");
        String urlEs = "/" + url + "/_search";

        try {
            logger.info("es多线程查询查ids开始，共计线程数量={}", requestList.size());
            CountDownLatch countDownLatch = new CountDownLatch(requestList.size());

            ExecutorService executorService = Executors.newFixedThreadPool(requestList.size());

            List<Callable<Void>> tasks = new ArrayList<>();
            for (SearchSourceBuilder searchRequest : requestList) {
                tasks.add(() -> {
                    try {
                        Response response = getRespByLow("POST", urlEs, searchRequest.toString());
                        int statusCode = response.getStatusLine().getStatusCode();
                        if (statusCode != 200) {
                            logger.info("多线程中es查询异常,返回值statusCode={}", statusCode);
                            return null;
                        }
                        JSONObject jsonObject = JSON.parseObject(EntityUtils.toString(response.getEntity()));
                        JSONObject hits = jsonObject.getJSONObject("hits");
                        JSONArray hitsList = hits.getJSONArray("hits");
                        List<String> ids = new ArrayList<>(size);
                        for (Object o : hitsList) {
                            JSONObject json = (JSONObject) o;
                            if (fieldName.equals("_id")) {
                                ids.add(json.getString("_id"));
                            } else {
                                JSONObject sourceOb = json.getJSONObject("_source");
                                ids.add(sourceOb.getString(fieldName));
                            }
                        }
                        synchronized (resultIds) {
                            resultIds.addAll(ids);
                        }
                    } catch (IOException e) {
                        logger.info("多线程中es查询异常 e={}", e);
                    }
                    countDownLatch.countDown();
                    return null;
                });
            }

            executorService.invokeAll(tasks);
            executorService.shutdown();
            countDownLatch.await(2, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("es多线程查询异常e={}", e);
            return ResultVo.fail("查询异常，请联系管理员");
        }
        long t2 = System.currentTimeMillis();
        logger.info("es多线程查询 查询时间t={}", (t2 - t1));
        return ResultVo.success(resultIds);
    }

    @Override
    public ResultVo<List<String>> getEsIdsByScroll(Integer totalSize, BoolQueryBuilder boolQueryBuilder, List<String> indexName, String orderField, String fieldName) {

        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        Integer size = 10000;
        int scrollTime = 0;
        if (totalSize < size) {
            // 单独只查询一次
            scrollTime = 1;
        } else {
            // 取余 确定共需查询多少次
            scrollTime = totalSize / size;
            if ((totalSize % size) != 0) {
                scrollTime += 1;
            }
        }

        // 设置游标扫描
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.sort(orderField, SortOrder.DESC);
        searchSourceBuilder.size(10000);
        //searchSourceBuilder.fetchSource(new String[]{fieldName.equals("_id") ? "id" : fieldName}, null);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(180));

        SearchRequest searchRequet = new SearchRequest(indexName.toArray(new String[indexName.size()]));
        searchRequet.source(searchSourceBuilder);
        // 1.处理scroll滚动查询的请求
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        searchRequet.scroll(scroll);
        try {
            //2.获取返回结果scrollId
            logger.info("开始进行scroll游标扫描查询ES");
            SearchResponse searchResponse = client.search(searchRequet, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHits searchHits = searchResponse.getHits();
            List<String> docIdList = new ArrayList<>();

            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);

            int searchCount = 1;
            // 只查询一次的情况
            if (scrollTime == 1 && searchHits != null && searchHits.getHits().length > 0) {
                for (SearchHit hit : searchHits.getHits()) {
                    docIdList.add(hit.getId());
                }
                return ResultVo.success(docIdList);
            } else {
                while (searchHits != null && searchHits.getHits().length > 0 && scrollTime >= searchCount) {
                    for (SearchHit hit : searchHits.getHits()) {
                        docIdList.add(hit.getId());
                    }
                    SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                    scrollRequest.scroll(scroll);
                    searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                    scrollId = searchResponse.getScrollId();
                    searchHits = searchResponse.getHits();
                    clearScrollRequest.addScrollId(scrollId);
                    searchCount += 1;
                }
            }
            ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest);
            return ResultVo.success(docIdList);
        } catch (Exception e) {
            logger.error("scroll游标查询ES失败", e);
            throw new GkException(GkErrorEnum.ES_SEARCH_ERROR);
        }
    }


    /**
     * @param reqType 目前只传 POST
     * @param url     url
     * @param jsonStr body 里的参数
     * @return
     * @throws IOException
     */
    private Response getRespByLow(String reqType, String url, String jsonStr) throws IOException {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        RestClient lowLevelClient = client.getLowLevelClient();
        Request request = new Request(reqType, url);
        //url的参数
        //request.addParameter("pretty", "true");
        //body参数
        request.setEntity(new NStringEntity(jsonStr, ContentType.APPLICATION_JSON));
        Response response = lowLevelClient.performRequest(request);
        return response;
    }

    @Override
    public List<String> getSessionIdsByMateDataQuery(Map<String, BoolQueryBuilder> map) {
        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        //不为null  但是SessionIds为空 表示查询为空  直接返回
        //如果为null 表示 没有这样的查询条件  可以无视本次操作
        if (map.size() < 1) {
            return null;
        }
        Set<String> set = new HashSet<>();
        for (String index : map.keySet()) {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = map.get(index);
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.sort("StartTime", SortOrder.DESC);
            searchSourceBuilder.fetchSource("SessionId", null);
            //元数据出发的只查1万
            searchSourceBuilder.size(10000);
            SearchRequest searchRequest = new SearchRequest(index + "*").preference("_only_nodes:box_type:hot");
            searchRequest.source(searchSourceBuilder);
            List<Map<String, Object>> esResultMapList = normalSearch(searchRequest);
            //这里判断返回值 esResultMapList
            for (Map<String, Object> esResultMap : esResultMapList) {
                String sessionId = esResultMap.get("SessionId").toString();
                set.add(sessionId);
            }
        }
        return new ArrayList<>(set);
    }

    @Override
    public AcknowledgedResponse deleteDoc(DeleteIndexRequest deleteIndexRequest) {

        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            return client.indices().delete(deleteIndexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public MultiSearchResponse esMultiSearch(MultiSearchRequest multiSearchRequest) {

        if (ObjectUtils.isEmpty(client)) {
            init();
        }

        try {
            return client.msearch(multiSearchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
