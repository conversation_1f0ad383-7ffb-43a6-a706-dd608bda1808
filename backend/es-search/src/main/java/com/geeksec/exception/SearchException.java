package com.geeksec.exception;

/**
 * <AUTHOR>
 * @Description：
 */
public class SearchException extends BaseException {
    private static final long serialVersionUID = 1L;

    /**
     * DOC未找到的CODE
     */
    public static final int ERROR_ONT_FOUND_CODE = -1;

    public SearchException() {
        super();
    }

    public SearchException(String message) {
        super(message);
    }

    public SearchException(int code, String message) {
        super(code, message);
    }

    public SearchException(String message, Throwable cause) {
        super(message, cause);
    }

    public SearchException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public SearchException(Throwable cause) {
        super(cause);
    }

}
