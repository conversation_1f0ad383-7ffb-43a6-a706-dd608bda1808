package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: 攻击
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "ATTACKER")
@Data
public class AttackerVertex {

  /**
  * 攻击id
  */
  @Id
  @Column(name = "attack_id")
  private String attackId;

  /**
   * ip地址
   */
  @Column(name = "ip_addr")
  private String ipAddr;

}
