package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 标签
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "LABEL")
@Data
public class LabelVertex {

  /**
  * 标签ID
  */
  @Id
  @Column(name = "label_id")
  private Long labelId;

  /**
  * 标签名称
  */
  @Column(name = "label_name")
  private String labelName;

  /**
  * 标签目标
  */
  @Column(name = "label_target_type")
  private Long labelTargetType;

  /**
  * 标签说明
  */
  @Column(name = "label_desc")
  private String labelDesc;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}
