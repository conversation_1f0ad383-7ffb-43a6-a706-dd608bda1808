package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.UrlDao;
import com.geeksec.ngbatis.service.UrlService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Url服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class UrlServiceImpl implements UrlService {

    @Autowired
    private UrlDao urlDao;

    /**
     * Url关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getUrlNebulaAssociationNext(GraphNextInfoCondition condition) {
        return urlDao.listUrlAllEdgeTypeAssociationNext(condition);
    }
}
