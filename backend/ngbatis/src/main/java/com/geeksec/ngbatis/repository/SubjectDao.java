package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.pojo.vertex.SubjectVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SubjectDao extends NebulaDaoBasic<SubjectVertex, String> {

    /**
     * 查询所有者所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listSubjectAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 所有者属性关联查询Next
     */
    List<VertexEdgeNextVo> listSubjectNebulaNextByProperties(@Param("condition") GraphPropertiesNextCondition condition);

}
