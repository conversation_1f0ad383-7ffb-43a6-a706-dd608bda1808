package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.UrlVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UrlDao extends NebulaDaoBasic<UrlVertex, String> {

    /**
     * 查询url所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listUrlAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
