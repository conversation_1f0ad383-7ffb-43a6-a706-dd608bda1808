package com.geeksec.ngbatis.pojo.edge;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 服务端使用证书（IP->证书）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "server_use_cert")
@Data
public class ServerUseCertEdge {

  /**
   * 证书id
   */
  @Column(name = "cert_id")
  private String certId;

  /**
   * sni
   */
  private String sni;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}
