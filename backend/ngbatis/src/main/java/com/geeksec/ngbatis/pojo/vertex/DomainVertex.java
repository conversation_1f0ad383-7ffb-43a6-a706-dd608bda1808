package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 域名
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "DOMAIN")
@Data
public class DomainVertex {

  /**
  * 域名地址
  */
  @Id
  @Column(name = "domain_addr")
  private String domainAddr;

  /**
  * 字节数
  */
  private Long bytes;

  /**
  * 平均流量 bps
  */
  @Column(name = "average_bps")
  private Long averageBps;

  /**
  * Alex排名
  */
  @Column(name = "alexa_rank")
  private Long alexaRank;

  /**
  * 首次出现时间
  */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
  * 末次出现时间
  */
  @Column(name = "last_seen")
  private Timestamp lastTime;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 备注
  */
  private String remark;

  /**
  * 信息
  */
  private String whois;

}
