package com.geeksec.ngbatis.pojo.edge;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 服务归属于IP地址（应用服务->IP）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "app_server")
@Data
public class AppServerEdge {

  /**
  * 应用名称
  */
  @Column(name = "app_name")
  private String appName;

  /**
  * ip地址
  */
  private String ip;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}
