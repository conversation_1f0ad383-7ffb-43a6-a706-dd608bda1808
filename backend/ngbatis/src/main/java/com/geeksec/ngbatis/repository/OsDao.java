package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.OsVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface OsDao extends NebulaDaoBasic<OsVertex, String> {

    /**
     * 查询os所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listOsAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
