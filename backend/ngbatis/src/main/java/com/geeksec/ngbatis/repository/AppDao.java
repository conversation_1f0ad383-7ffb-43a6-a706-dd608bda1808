package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.AppVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppDao extends NebulaDaoBasic<AppVertex, String> {

    /**
     * 查询APP所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAppAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
