package com.geeksec.ngbatis.pojo.edge;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: DNS解析最终IP（域名->IP）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "parse_to")
@Data
public class ParseToEdge {

  /**
   * dns服务
   */
  @Column(name = "dns_server")
  private String dnsServer;

  /**
   * 最终解析
   */
  @Column(name = "final_parse")
  private Boolean finalParse;

  /**
   * 最大ttl
   */
  @Column(name = "max_ttl")
  private Long maxTtl;

  /**
   * 最小ttl
   */
  @Column(name = "min_ttl")
  private Long minTtl;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}
