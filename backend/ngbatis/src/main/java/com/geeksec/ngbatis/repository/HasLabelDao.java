package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.pojo.edge.HasLabelEdge;
import com.geeksec.ngbatis.vo.HasLabelVertexVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface HasLabelDao extends NebulaDaoBasic<HasLabelEdge, String> {

    /**
     * 查询当前符合标签条件的实体
     */
    List<HasLabelVertexVo> listTagVertexByTagIds(@Param("tagIds") List<String> tagIds);

    /**
    * 删除标签
    */
    void deleteHasLabel(@Param("id") String id);

    /**
     * 插入标签
     */
    void insertHasLabel(@Param("id") String id,@Param("labels") List<String> labels);
}
