package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.AttackerVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AttackerDao extends NebulaDaoBasic<AttackerVertex, String> {

    /**
     * 查询attack所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAttackAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
