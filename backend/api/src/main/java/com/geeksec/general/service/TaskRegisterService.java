package com.geeksec.general.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.condition.TaskRegisterCondition;
import com.geeksec.analysis.entity.DownloadTaskRegister;

/**
 * @Author: Guan<PERSON>ao
 * @Date: 2022/5/19 11:18
 * @Description： 通用的下载任务注册
 */
public interface TaskRegisterService {

    /**
     * 任务注册
     *
     * @param condition 条件
     * @return 状态
     */
    JSONObject taskRegister(TaskRegisterCondition condition);

    /**
     * 下载列表查询，TODO 不包括pcap下载
     *
     * @param condition 条件
     * @return 下载列表
     */
    JSONObject searchList(TaskRegisterCondition condition);

    /**
     * 删除任务接口，仅仅支持待处理型任务
     *
     * @param id id
     * @return 是否成功
     */
    JSONObject deleteTask(Integer id);

    /**
     * 数据下载接口
     *
     * @param id 任务id
     * @return 数据文件
     */
    DownloadTaskRegister downloadData(Integer id);
}
