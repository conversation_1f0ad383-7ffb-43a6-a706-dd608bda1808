package com.geeksec.general.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.AnalysisPushDao;
import com.geeksec.analysis.dao.ThreatInfoDao;
import com.geeksec.analysis.entity.ThreatInfo;
import com.geeksec.analysis.entity.condition.AddInternalNetCondition;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.analysis.entity.condition.UpdateInternalNetCondition;
import com.geeksec.analysis.entity.vo.InternalNetVo;
import com.geeksec.analysis.entity.vo.PoolIpInfoVo;
import com.geeksec.analysis.utils.ESQueryUtil;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ReqCommon;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.entity.communication.vo.CommunicationVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.condition.workbench.CommuListCondition;
import com.geeksec.general.condition.workbench.InternalNetCondition;
import com.geeksec.general.service.EsearchService;
import com.geeksec.general.service.WorkBenchService;
import com.geeksec.ngbatis.service.EdgeTypeService;
import com.geeksec.ngbatis.vo.DomainIpVo;
import com.geeksec.push.dao.normal.TaskStatisticDao;
import com.geeksec.push.entity.vo.TaskStatisticBpsVo;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.CompareUtils;
import com.geeksec.util.IpUtils;
import com.geeksec.util.TimeUtil;
import com.google.common.collect.Lists;
import org.apache.hbase.thirdparty.io.netty.util.internal.StringUtil;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.max.ParsedMax;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class WorkBenchServiceImpl implements WorkBenchService {
    private static final Logger logger = LoggerFactory.getLogger(WorkBenchServiceImpl.class);

    @Value("${elasticsearch.es_connect_index}")
    private String esConnectIndex;

    // 威胁情报域名
    private static HashMap<String, ThreatInfo> threatDomainMap = new HashMap<>();

    // DNS服务器IP
    private static HashMap<String, Object> dnsServerMap = new HashMap<>();

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private AnalysisPushDao analysisPushDao;

    @Autowired
    private ThreatInfoDao threatInfoDao;

    @Autowired
    private TaskStatisticDao taskStatisticDao;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Value("${mining.labels}")
    private String minningLabels;

    @Autowired
    private EdgeTypeService edgeTypeService;


    @PostConstruct
    private void initDnsServer() {
        String path = "dict/user_defined_dns_server.csv";
        StringBuilder stringBuilder = new StringBuilder();
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
        if (inputStream != null) {
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))){
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }

            } catch (IOException e) {
                logger.error("读取path->{}文件失败！error->{}", path, e);
            }
        }
        else {
            stringBuilder.append("");
        }
        List<String> dnsServerStr = Lists.newArrayList(stringBuilder.toString().split("\n"));
        for (String info : dnsServerStr) {
            if (info.equals("address,desc")) {
                continue;
            }
            dnsServerMap.put(info.split(",")[0], info.split(",")[1]);
        }
    }

    @Override
    public HashMap<String, Object> getCommuList(CommuListCondition condition) {
        logger.info("IP态势图，condition={}", condition);
        long t1 = System.currentTimeMillis();
        Integer limit = condition.getLimit();
        if (limit == null)
            condition.setLimit(10);
        // 拼装查询条件
        List<HashMap<String, Object>> query = condition.getQuery();
        ReqCommon reqCommon = new ReqCommon();
        reqCommon.setAsc(condition.getAsc());
        reqCommon.setLimit(condition.getLimit());
        reqCommon.setPage(condition.getPage());
        reqCommon.setTaskId(condition.getTaskId());
        Long startTime = condition.getStartTime();
        if (startTime == null)
            startTime = 0L;
        Long endTime = condition.getEndTime();
        if (endTime == null)
            endTime = 0L;
        if (startTime > 0) {
            reqCommon.setStartTime(startTime);
        }
        if (endTime > 0) {
            reqCommon.setEndTime(endTime);
        }
        // 本次查询总Bool条件语句
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 若含有查询条件，拼装qb
        if (query.size() != 0) {
            boolQueryBuilder = ESQueryUtil.buildQueryEntrance(query, boolQueryBuilder);
        }
        ESQueryUtil.specialHandle(boolQueryBuilder, reqCommon.getTaskId(), reqCommon.getStartTime(), reqCommon.getEndTime());

        List<CommunicationVo> voList = new ArrayList<>();
        HashMap<String, Object> resultMap = new HashMap<>();
        //组装聚合查询请求
        Map<String, Object> map = filterIndexName(esConnectIndex, reqCommon.getStartTime(), reqCommon.getEndTime(), reqCommon.getTaskId());
        List<String> indexNames = (List<String>) map.get("indexNames");
        if (indexNames == null || indexNames.size() < 1) {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }
        Integer sysType = condition.getSysType();
        if (sysType == 1) {
            //挖矿首页
            String[] split = minningLabels.split(",");
            boolQueryBuilder.filter(QueryBuilders.termsQuery("Labels", split));
        }
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder countSearchBuilder = new SearchSourceBuilder();
        countSearchBuilder.query(boolQueryBuilder);
        countRequest.source(countSearchBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        if (count == 0) {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }
        Boolean isFrontPage = condition.getIsFrontPage();
        Integer limitSize = esLimit;
        if (isFrontPage) {
            //首页聚合 10000
            limitSize = 10000;
            if (sysType == 0) {
                //挖矿 == 1  暂时不加时间限制  只有标签限制
                //这里设置传输时间是 最后的索引的前一周
                long initEndTime = (long) map.get("maxTime");
                long initStartTime = initEndTime - (24 * 60 * 60);
                RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("CreateTime");
                timeRangeQuery.from(initStartTime);
                timeRangeQuery.to(initEndTime);
                boolQueryBuilder.must(timeRangeQuery);
            }
        }
        //查询到的条数可能很少  没必要生成10个
        if (count < limitSize) {
            limitSize = (int) count;
        }

        ResultVo<List<String>> ResultVoIds = esearchService.getEsIds(10000, limitSize, boolQueryBuilder, indexNames, "CreateTime", "_id");
        if (ResultVoIds.getErr() != 0) {
            resultMap.put("error", ResultVoIds.getErr());
            resultMap.put("msg", ResultVoIds.getMsg());
            return resultMap;
        }
        List<String> ids = ResultVoIds.getData();
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        if (ids.size() > 0) {
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termsQuery("_id", ids));
            aggrSourceBuilder = aggrSourceBuilder.clearRescorers();
            aggrSourceBuilder.query(boolQueryBuilder);
        } else {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }

        // 聚合条件
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("communication")
                .size(reqCommon.getLimit())
                .field("sip_appid_dport_dip");
        aggrSourceBuilder.size(0);
        aggrSourceBuilder.aggregation(termsAggregationBuilder);

        SearchRequest aggrRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        aggrRequest.source(aggrSourceBuilder);
        // 直接返回聚合处理结果
        long tt1 = System.currentTimeMillis();
        Aggregations aggregations = esearchService.aggrSearch(aggrRequest);
        long tt2 = System.currentTimeMillis();
        logger.info("IP态势图，聚合查询时间t={}", (tt2 - tt1));
        ParsedStringTerms commuData = aggregations.get("communication");
        List<String> aggrResult = new ArrayList<>();

        // 开始处理结果数据
        // metadata 结构 166.111.5.203_10071_53_8.8.8.8 sip_appid_dport_dip
        List<? extends Terms.Bucket> buckets = commuData.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            String metadata = bucket.getKeyAsString();
            long docCount = bucket.getDocCount();
            CommunicationVo vo = new CommunicationVo();
            vo.setSIp(metadata.split("_")[0]);
            vo.setAppId(metadata.split("_")[1]);
            vo.setDstPort(metadata.split("_")[2]);
            vo.setDIp(metadata.split("_")[3]);
            vo.setCount(docCount);
            voList.add(vo);
            aggrResult.add(metadata);
        }


        resultMap.put("records", voList);
        resultMap.put("total", buckets.size());
        long t3 = System.currentTimeMillis();
        logger.info("IP态势图查询完毕，总时长={}", (t3 - t1));
        return resultMap;
    }

    @Override
    public HashMap<String, Object> getCommuListNew(CommuListCondition condition) {
        logger.info("首页IP态势图、会话分析，condition={}", condition);
        Integer limit = condition.getLimit();
        if (limit == null)
            condition.setLimit(10);
        // 拼装查询条件
        List<HashMap<String, Object>> query = condition.getQuery();
        ReqCommon reqCommon = new ReqCommon();
        reqCommon.setAsc(condition.getAsc());
        reqCommon.setLimit(condition.getLimit());
        reqCommon.setPage(condition.getPage());
        reqCommon.setTaskId(condition.getTaskId());

        // 设置查询的条件为最后一条数据的最后一个小时的数据（通过ES查询）
        //1.去es_index中匹配最后一条数据的时间
        try {

            SearchRequest latestIndexRequest = new SearchRequest("es_index");
            SearchSourceBuilder latestSourceBuilder = new SearchSourceBuilder();
            latestSourceBuilder.sort("last_time", SortOrder.DESC);
            latestSourceBuilder.size(1);
            BoolQueryBuilder indexBoolQueryBuilder = new BoolQueryBuilder();
            indexBoolQueryBuilder.must(QueryBuilders.wildcardQuery("index", "connectinfo*"));
            indexBoolQueryBuilder.must(QueryBuilders.termsQuery("task", condition.getTaskId()));
            latestSourceBuilder.query(indexBoolQueryBuilder);
            latestIndexRequest.source(latestSourceBuilder);
            SearchResponse response = esearchService.esSearch(latestIndexRequest);
            SearchHit[] hits = response.getHits().getHits();
            String indexName = "";
            List<CommunicationVo> voList = new ArrayList<>();
            HashMap<String, Object> resultMap = new HashMap<>();
            if (hits.length > 0) {
                indexName = hits[0].getSourceAsMap().get("index").toString();
            } else {
                logger.info("当前没有会话数据索引");
                resultMap.put("records", voList);
                resultMap.put("total", 0);
                return resultMap;
            }

            // 组装聚合查询请求
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            // 判断是否为挖矿产品,添加标签查询
            if (condition.getSysType() == 1) {
                String[] split = minningLabels.split(",");
                boolQueryBuilder.filter(QueryBuilders.termsQuery("label", split));
            }

            CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
            SearchSourceBuilder countSourceBuilder = new SearchSourceBuilder();
            countSourceBuilder.query(boolQueryBuilder);
            countRequest.source(countSourceBuilder);
            CountResponse countResponse = esearchService.esSearchForCount(countRequest);

            long count = countResponse.getCount();
            if (count == 0) {
                resultMap.put("records", voList);
                resultMap.put("total", 0);
                return resultMap;
            }

            // 设置聚合的条数，放在首页一般放10条就够了
            int limitSize = 15;

            // 开始聚合最近一周的数据
            TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("communication")
                    .size(limitSize)
                    .field("sip_appid_dport_dip");
            SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
            aggrSourceBuilder.from(0).terminateAfter(100).sort("CreateTime", SortOrder.DESC);
            aggrSourceBuilder.aggregation(termsAggregationBuilder);

            SearchRequest aggrRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
            aggrRequest.source(aggrSourceBuilder);
            // 直接返回聚合处理结果
            Aggregations aggregations = esearchService.aggrSearch(aggrRequest);
            ParsedStringTerms commuData = aggregations.get("communication");
            List<String> aggrResult = new ArrayList<>();

            // 开始处理结果数据
            // metadata 结构 166.111.5.203_10071_53_8.8.8.8 sip_appid_dport_dip
            List<? extends Terms.Bucket> buckets = commuData.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                String metadata = bucket.getKeyAsString();
                long docCount = bucket.getDocCount();
                CommunicationVo vo = new CommunicationVo();
                vo.setSIp(metadata.split("_")[0]);
                vo.setAppId(metadata.split("_")[1]);
                vo.setDstPort(metadata.split("_")[2]);
                vo.setDIp(metadata.split("_")[3]);
                vo.setCount(docCount);
                voList.add(vo);
                aggrResult.add(metadata);
            }
            resultMap.put("records", voList);
            resultMap.put("total", buckets.size());
            return resultMap;
        } catch (Exception e) {
            logger.error("首页IP态势图、会话分析异常", e);
            return new HashMap<>();
        }
    }


    @Override
    public ResultVo getCommuList2(CommunicationCondition condition) {
        logger.info("IP态势图、会话分析，condition={}", condition);
        long t1 = System.currentTimeMillis();
        Integer limit = condition.getPageSize();
        if (limit == null) {
            condition.setPageSize(10);
        }
        //获取索引集合
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        Map<String, Object> map = filterIndexName(esConnectIndex, timeRange.getLeft(), timeRange.getRight(), condition.getTaskId());
        List<String> indexNames = (List<String>) map.get("indexNames");
        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setTotal(0);
        //组装聚合查询请求
        if (indexNames == null || indexNames.size() < 1) {
            return ResultVo.success(pageResultVo);
        }

        //先判断元数据查询
        Map<String, BoolQueryBuilder> map1 = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map1);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return ResultVo.success(pageResultVo);
        }

        //配置基本的查询条件
        BoolQueryBuilder boolQueryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder countSearchBuilder = new SearchSourceBuilder();
        countSearchBuilder.query(boolQueryBuilder);
        countRequest.source(countSearchBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        if (count == 0) {
            return ResultVo.success(pageResultVo);
        }

        Boolean isFrontPage = condition.getIsFrontPage();
        Integer limitSize = esLimit;
        if (isFrontPage) {
            //首页聚合 10000
            limitSize = 10000;
            //这里设置传输时间是 最后的索引的前一周
            long initEndTime = (long) map.get("maxTime");
            long initStartTime = initEndTime - (24 * 60 * 60);
            RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("CreateTime");
            timeRangeQuery.from(initStartTime);
            timeRangeQuery.to(initEndTime);
            boolQueryBuilder.must(timeRangeQuery);
        }
        //查询到的条数可能很少  没必要生成10个
        if (count < limitSize) {
            limitSize = (int) count;
        }

        ResultVo<List<String>> ResultVoIds = esearchService.getEsIds(10000, limitSize, boolQueryBuilder, indexNames, "CreateTime", "_id");
        if (ResultVoIds.getErr() != 0) {
            return ResultVo.success(pageResultVo);
        }
        List<String> ids = ResultVoIds.getData();
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        if (ids.size() > 0) {
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("_id", ids));
            aggrSourceBuilder = aggrSourceBuilder.clearRescorers();
            aggrSourceBuilder.query(boolQueryBuilder);
        } else {
            return ResultVo.success(pageResultVo);
        }

        // 聚合条件
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("communication")
                .size(condition.getPageSize())
                .field("sip_appid_dport_dip");


        aggrSourceBuilder.size(0);
        aggrSourceBuilder.aggregation(termsAggregationBuilder);

        SearchRequest aggrRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        aggrRequest.source(aggrSourceBuilder);
        // 直接返回聚合处理结果
        long tt1 = System.currentTimeMillis();
        Aggregations aggregations = esearchService.aggrSearch(aggrRequest);
        long tt2 = System.currentTimeMillis();
        logger.info("IP态势图2、会话分析聚合查询时间t={}", (tt2 - tt1));
        ParsedStringTerms commuData = aggregations.get("communication");
        List<String> aggrResult = new ArrayList<>();

        // 开始处理结果数据
        // metadata 结构 166.111.5.203_10071_53_8.8.8.8 sip_appid_dport_dip
        List<? extends Terms.Bucket> buckets = commuData.getBuckets();
        List<CommunicationVo> voList = new ArrayList<>();
        for (Terms.Bucket bucket : buckets) {
            String metadata = bucket.getKeyAsString();
            long docCount = bucket.getDocCount();
            CommunicationVo vo = new CommunicationVo();
            vo.setSIp(metadata.split("_")[0]);
            vo.setAppId(metadata.split("_")[1]);
            vo.setDstPort(metadata.split("_")[2]);
            vo.setDIp(metadata.split("_")[3]);
            vo.setCount(docCount);
            voList.add(vo);
            aggrResult.add(metadata);
        }
        long t3 = System.currentTimeMillis();
        logger.info("IP态势图2、会话分析查询完毕，总时长={}", (t3 - t1));
        pageResultVo.setRecords(voList);
        pageResultVo.setTotal(buckets.size());
        return ResultVo.success(pageResultVo);

    }

    @Override
    public HashMap<String, Object> getCommuList3(CommuListCondition condition) {

        if (MapUtils.isEmpty(threatDomainMap)) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("target_type", "domain");
            wrapper.eq("tag_name", "MinePool");
            wrapper.select("DISTINCT target,target_type,tag_name");
            List<ThreatInfo> infoList = threatInfoDao.selectList(wrapper);
            if (!CollectionUtils.isEmpty(infoList)) {
                for (ThreatInfo info : infoList) {
                    threatDomainMap.put(info.getTarget(), info);
                }
            }
        }

        logger.info("矿池态势图，condition={}", condition);
        long t1 = System.currentTimeMillis();
        Integer limit = condition.getLimit();
        if (limit == null)
            condition.setLimit(10);
        // 拼装查询条件
        List<HashMap<String, Object>> query = condition.getQuery();
        ReqCommon reqCommon = new ReqCommon();
        reqCommon.setAsc(condition.getAsc());
        reqCommon.setLimit(condition.getLimit());
        reqCommon.setPage(condition.getPage());
        reqCommon.setTaskId(condition.getTaskId());
        Long startTime = condition.getStartTime();
        if (startTime == null)
            startTime = 0L;
        Long endTime = condition.getEndTime();
        if (endTime == null)
            endTime = 0L;
        if (startTime > 0) {
            reqCommon.setStartTime(startTime);
        }
        if (endTime > 0) {
            reqCommon.setEndTime(endTime);
        }
        // 本次查询总Bool条件语句
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 若含有查询条件，拼装qb
        if (query.size() != 0) {
            boolQueryBuilder = ESQueryUtil.buildQueryEntrance(query, boolQueryBuilder);
        }
        ESQueryUtil.specialHandle(boolQueryBuilder, reqCommon.getTaskId(), reqCommon.getStartTime(), reqCommon.getEndTime());

        List<CommunicationVo> voList = new ArrayList<>();
        HashMap<String, Object> resultMap = new HashMap<>();
        //组装聚合查询请求
        Map<String, Object> map = filterIndexName(esConnectIndex, reqCommon.getStartTime(), reqCommon.getEndTime(), reqCommon.getTaskId());
        List<String> indexNames = (List<String>) map.get("indexNames");
        if (indexNames == null || indexNames.size() < 1) {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }
        Integer sysType = condition.getSysType();
        if (sysType == 1) {
            //挖矿首页
            String[] split = minningLabels.split(",");
            boolQueryBuilder.filter(QueryBuilders.termsQuery("Labels", split));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("AppName", "APP_DNS"));
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder countSearchBuilder = new SearchSourceBuilder();
        countSearchBuilder.query(boolQueryBuilder);
        countRequest.source(countSearchBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        if (count == 0) {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }
        Boolean isFrontPage = condition.getIsFrontPage();
        Integer limitSize = esLimit;
        if (isFrontPage) {
            //首页聚合 100000
            limitSize = 100000;
            if (sysType == 0) {
                //挖矿 == 1  暂时不加时间限制  只有标签限制
                //这里设置传输时间是 最后的索引的前一周
                long initEndTime = (long) map.get("maxTime");
                long initStartTime = initEndTime - (24 * 60 * 60);
                RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("CreateTime");
                timeRangeQuery.from(initStartTime);
                timeRangeQuery.to(initEndTime);
                boolQueryBuilder.must(timeRangeQuery);
            }
        }
        //查询到的条数可能很少  没必要生成10个
        if (count < limitSize) {
            limitSize = (int) count;
        }

        ResultVo<List<String>> ResultVoIds = esearchService.getEsIds(10000, limitSize, boolQueryBuilder, indexNames, "CreateTime", "_id");
        if (ResultVoIds.getErr() != 0) {
            resultMap.put("error", ResultVoIds.getErr());
            resultMap.put("msg", ResultVoIds.getMsg());
            return resultMap;
        }
        List<String> ids = ResultVoIds.getData();
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        if (ids.size() > 0) {
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termsQuery("_id", ids));
            aggrSourceBuilder = aggrSourceBuilder.clearRescorers();
            aggrSourceBuilder.query(boolQueryBuilder);
        } else {
            resultMap.put("records", voList);
            resultMap.put("total", 0);
            return resultMap;
        }

        aggrSourceBuilder.size(0);

        SearchRequest aggrRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]));
        // 组装聚合剩下的两个字段
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();

        // 1.拼装字段 sip dip appName dport
        TermsValuesSourceBuilder sIpTermsBuilder = new TermsValuesSourceBuilder("s_ip").field("sIp").missingBucket(true);
        sources.add(sIpTermsBuilder);
        TermsValuesSourceBuilder dIpTermsBuilder = new TermsValuesSourceBuilder("domain").field("DNS.Domain.keyword").missingBucket(true);
        sources.add(dIpTermsBuilder);
        TermsValuesSourceBuilder appNameTermsBuilder = new TermsValuesSourceBuilder("appName").field("AppName").missingBucket(true);
        sources.add(appNameTermsBuilder);
        TermsValuesSourceBuilder dPortTermsBuilder = new TermsValuesSourceBuilder("dPort").field("dPort").missingBucket(true);
        sources.add(dPortTermsBuilder);
        CompositeAggregationBuilder composite = new CompositeAggregationBuilder("sip_domain", sources).size(100000);

        // 2、执行查询
        aggrSourceBuilder.aggregation(composite);
        aggrRequest.source(aggrSourceBuilder);
        // 直接返回聚合处理结果
        Aggregations aggregations = esearchService.aggrSearch(aggrRequest);
        ParsedComposite parsedComposite = aggregations.get("sip_domain");
        List<ParsedComposite.ParsedBucket> list = parsedComposite.getBuckets();

        List<Map<String, Object>> aggrList = new ArrayList<>();
        for (ParsedComposite.ParsedBucket parsedBucket : list) {
            Map<String, Object> data = new HashMap<>();
            for (Map.Entry<String, Object> m : parsedBucket.getKey().entrySet()) {
                data.put(m.getKey(), m.getValue());
            }

            if (!ObjectUtils.isEmpty(data.get("domain"))) {
                String domain = (String) data.get("domain");
                String sIp = (String) data.get("s_ip");
                if (threatDomainMap.containsKey(domain) && !dnsServerMap.containsKey(sIp)) {
                    data.put("cnt", parsedBucket.getDocCount());
                    aggrList.add(data);
                }
            }
        }
        if (CollectionUtils.isEmpty(aggrList)) {
            logger.info("矿池态势图聚合查询结果为空");
            return resultMap;
        }
        logger.info("矿池态势图聚合结果条数 -->{}", aggrList.size());
        if (aggrList.size() < 10) {
            aggrList = aggrList.stream().sorted(Comparator.comparingLong(CompareUtils::comparingByCnt).reversed()).collect(Collectors.toList()).subList(0, aggrList.size());
        } else {
            aggrList = aggrList.stream().sorted(Comparator.comparingLong(CompareUtils::comparingByCnt).reversed()).collect(Collectors.toList()).subList(0, 10);
        }
        resultMap.put("data", aggrList);
        resultMap.put("total", aggrList.size());

        return resultMap;
    }


    @Override
    public HashMap<String, Object> getInternalNetList(InternalNetCondition condition) {

        List<InternalNetVo> result = new ArrayList<>();
        HashMap<String, Object> resultMap = new HashMap<>();
        logger.info("开始查询内网IP列表,condition--->{}", condition);
        try {
            Integer limit = condition.getPageSize();
            Integer currentPage = condition.getCurrentPage();
            Integer offset = (currentPage - 1) * limit;
            result = analysisPushDao.getInternalNet(condition.getTaskId(), condition.getOrderFiled(), condition.getSortOrder(), limit, offset);
            Integer count = analysisPushDao.getInternalNetCount(condition.getTaskId());
            if (count == null) {
                count = 0;
            }
            resultMap.put("result", result);
            resultMap.put("total", count);
        } catch (Exception e) {
            logger.error("查询内网IP列表信息失败--->{}", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject addInternalNetInfo(AddInternalNetCondition condition) {
        logger.info("添加IP内网网段信息,condition --->{}", condition);

        // 判断当前内网IP是否存在
        Integer exist = analysisPushDao.existInternalIp(condition.getInterIp());
        if (exist != 0) {
            throw new GkException(GkErrorEnum.FAIL);
        }

        // 获取关联目的MAC值(From ES)
        String dIp = condition.getInterIp();
        SearchRequest searchRequest = new SearchRequest();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dIp", dIp));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder).indices(esConnectIndex);
        try {
            SearchResponse response = esearchService.esSearch(searchRequest);
            long count = response.getHits().getTotalHits();
            if (count == 0) {
                condition.setMac(StringUtil.EMPTY_STRING);
            } else {
                SearchHit[] hits = response.getHits().getHits();
                Map<String, Object> hitMap = hits[0].getSourceAsMap();
                String dMac = (String) hitMap.get("dMac");
                condition.setMac(dMac);
            }
            condition.setCreatedTime(new Date());
            analysisPushDao.addInternalInfo(condition);
            logger.info("添加单体IP内网网段信息成功");
            return CommonUtil.successJson("添加单体IP内网网段信息成功");
        } catch (Exception e) {
            logger.error("添加IP内网网段信息失败,error-->", e);
            throw new GkException(GkErrorEnum.FAIL);
        }
    }

    @Override
    public boolean deleteInterInfoByIds(List<Integer> ids) {
        logger.info("批量删除内网IP网段信息,taskIds --->{}", ids);
        try {
            analysisPushDao.batchDeleteInterInfo(ids);
            return true;
        } catch (Exception e) {
            logger.error("批量删除内网IP网段信息失败!error---{}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean updateInternalNetInfo(UpdateInternalNetCondition condition) {
        logger.info("更新内网IP网段信息,condition--->{}", condition);
        try {
            // 仍然要去ES里面查询，是否有相关MAC地址
            String dIp = condition.getInterIp();
            SearchRequest searchRequest = new SearchRequest();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("dIp", dIp));
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchRequest.source(searchSourceBuilder).indices(esConnectIndex);
            try {
                SearchResponse response = esearchService.esSearch(searchRequest);
                long count = response.getHits().getTotalHits();
                if (count == 0) {
                    condition.setMac(StringUtil.EMPTY_STRING);
                } else {
                    SearchHit[] hits = response.getHits().getHits();
                    Map<String, Object> hitMap = hits[0].getSourceAsMap();
                    String dMac = (String) hitMap.get("dMac");
                    condition.setMac(dMac);
                }
                condition.setLastModifiedTime(new Date());
                analysisPushDao.updateInterInfo(condition);
                return true;
            } catch (Exception e) {
                logger.error("修改内网IP网段信息失败error --->,", e.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("更新内网IP网段信息失败!error---{}", e.getMessage());
            return false;
        }
    }

    @Override
    public HashMap<String, Object> getPoolIpList() {

        HashMap<String, Object> resultMap = new HashMap<>();
        List<PoolIpInfoVo> voList = new ArrayList<>();
        try {
            // 1.查询数据库中存在的域名告警数据
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("target_type", "domain");
            wrapper.eq("tag_name", "MinePool");
            wrapper.select("DISTINCT target,target_type,tag_name");
            List<ThreatInfo> infoList = threatInfoDao.selectList(wrapper);
            if (CollectionUtils.isEmpty(infoList)) {
                return new HashMap<>();
            }

            List<String> threatIpList = new ArrayList<>();
            List<DomainIpVo> domainIpVoList = edgeTypeService.listParseToCnameResultByDomains(infoList.stream().map(ThreatInfo::getTarget).collect(Collectors.toList()));
            for (DomainIpVo domainIpVo : domainIpVoList) {
                String ip = domainIpVo.getIpAddr();
                String domain = domainIpVo.getDomainAddr();
                PoolIpInfoVo vo = new PoolIpInfoVo();
                if (IpUtils.isIpv4Str(ip) || IpUtils.isIpv6Str(ip)) {
                    vo.setIp(ip);
                    vo.setDomain(domain);
                    vo.setPort(0);
                    threatIpList.add(ip);
                    voList.add(vo);
                }
            }

            SearchRequest searchRequest = new SearchRequest();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            List<Integer> miningLables = new ArrayList<>();
//            miningLables.add(20036);
//            miningLables.add(20048);
//            boolQueryBuilder.filter(QueryBuilders.termsQuery("Labels",miningLables));
            boolQueryBuilder.filter(QueryBuilders.termsQuery("dIp", threatIpList));
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(10000);
            searchRequest.source(searchSourceBuilder).indices(esConnectIndex);
            SearchResponse response = esearchService.esSearch(searchRequest);
            long count = response.getHits().getTotalHits();
            // 添加端口参数
            if (count != 0) {
                SearchHit[] hits = response.getHits().getHits();
                for (SearchHit hit : hits) {
                    Map<String, Object> hitMap = hit.getSourceAsMap();
                    Integer dPort = (Integer) hitMap.get("dPort");
                    String ip = (String) hitMap.get("dIp");
                    for (PoolIpInfoVo vo : voList) {
                        if (ip.equals(vo.getIp()) && vo.getPort() == 0) {
                            vo.setPort(dPort);
                            break;
                        } else {
                            continue;
                        }
                    }
                }
                for (PoolIpInfoVo vo : voList) {
                    if (vo.getPort() == 0 || vo.getPort() == null) {
                        vo.setPort(53);
                    }
                }
            } else {
                resultMap.put("result", voList);
            }

            resultMap.put("result", voList);
            resultMap.put("count", voList.size());

            return resultMap;
        } catch (Exception e) {
            logger.error("查询矿池IP列表失败,error-->", e);
            return new HashMap<>();
        }

    }

    public Map<String, Object> filterIndexName(String index, Long startTime, Long endTime, List<Integer> sliTask) {
        BoolQueryBuilder builder = new BoolQueryBuilder();
        builder.must(QueryBuilders.wildcardQuery("index.keyword", index));

        //es_index的时间范围特殊
        delEsIndexTime(builder, startTime, endTime);

        HashMap<Integer, Integer> taskMap = new HashMap<>();
        Integer[] taskId = sliTask.toArray(new Integer[]{});
        if (sliTask.size() != 0) {
            for (int i = 0; i < sliTask.size(); i++) {
                taskMap.put(taskId[i], taskId[i]);
            }
        }
        if (taskId.length != 0) {
            builder.must(QueryBuilders.termsQuery("task", taskId));
        }

        // 创建查询请求
        SearchRequest indexRequest = new SearchRequest("es_index");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(builder).size(10000);
        indexRequest.source(searchSourceBuilder);
        //得到最大的时间
        searchSourceBuilder.aggregation(AggregationBuilders.max("maxTime").field("last_time"));


        // 发送查询请求返回结果
        SearchResponse searchResponse = esearchService.esSearch(indexRequest);

        Aggregations aggregations = searchResponse.getAggregations();
        List<String> indexNames = new ArrayList<>();
        ParsedMax p = aggregations.get("maxTime");
        Map<String, Object> mapResult = new HashMap<>();
        mapResult.put("indexNames", indexNames);
        mapResult.put("maxTime", (long) p.getValue());

        List<Map<String, Object>> indexList = new ArrayList<>();
        SearchHit[] hits = searchResponse.getHits().getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> result = hit.getSourceAsMap();
            indexList.add(result);
        }
        // 处理结果获取index字段
        Map<String, String> map = new HashMap<>();
        if (indexList.size() > 1000) {
            for (Map<String, Object> esResultMap : indexList) {
                String indexName = esResultMap.get("index").toString();
                String[] sliV = indexName.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            mapResult.put("indexNames", indexNames);
            return mapResult;
        }
        for (Map<String, Object> esResultMap : indexList) {
            String indexName = esResultMap.get("index").toString();
            indexNames.add(indexName);
        }

        return mapResult;
    }

    @Override
    public List<TaskStatisticBpsVo> getBpsForRecently(Integer days, Integer taskId) {
        if (days == null) days = 7;
        //时间计算需要+1
        days += 1;
        //这里获取N日的查询时间节点 true:要算今日
        List<Integer> queryZeroNodes = TimeUtil.getZeroNode(days, true);
        List<TaskStatisticBpsVo> resultList = new ArrayList<>();
        for (int i = 0; i < queryZeroNodes.size(); i++) {
            Integer endTime = queryZeroNodes.get(i);
            Integer startTime = queryZeroNodes.get(i + 1);
            Long bps = taskStatisticDao.getTodayBps(startTime, endTime, taskId);
            TaskStatisticBpsVo vo = new TaskStatisticBpsVo();
            if (bps == null)
                vo.setBps(0);
            else
                vo.setBps(bps);
            vo.setTime(startTime);
            resultList.add(vo);
            //判定正确的天数需要-1
            if (resultList.size() == (days - 1))
                break;
        }
        return resultList;
    }

    /**
     * 查询es_index 需要严格的时间处理
     *
     * @param builder
     * @param startTime
     * @param endTime
     */
    private void delEsIndexTime(BoolQueryBuilder builder, Long startTime, Long endTime) {
        List<QueryBuilder> musts = builder.must();
        //两个时间都有值。  需要索引的s1-s2有交集
        if (startTime > 0 && endTime > 0) {
            RangeQueryBuilder firstRange = QueryBuilders.rangeQuery("first_time");
            //gt  大于
            firstRange.gte(endTime);
            RangeQueryBuilder lastRange = QueryBuilders.rangeQuery("last_time");
            lastRange.lte(startTime);
            BoolQueryBuilder mustBuilder = new BoolQueryBuilder();
            //es的s1 或者 s2在  查询区间，即可
            mustBuilder.mustNot(firstRange);
            mustBuilder.mustNot(lastRange);
            musts.add(mustBuilder);
        }
        //开始时间有值   需要索引的s2 >  startTime
        if (startTime > 0 && endTime <= 0) {
            RangeQueryBuilder lastRange = QueryBuilders.rangeQuery("last_time");
            lastRange.gte(startTime);
            musts.add(lastRange);
        }
        //结束时间有值   需要索引的s1 <  lastTime
        if (startTime <= 0 && endTime > 0) {
            RangeQueryBuilder firstRange = QueryBuilders.rangeQuery("first_time");
            firstRange.lte(endTime);
            musts.add(firstRange);
        }
    }

}

