package com.geeksec.general.controller;


import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.condition.ModelCondition;
import com.geeksec.analysis.service.ModelService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.entity.common.ResultVo;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 2022-08-17
 */
@RestController
@RequestMapping("/model")
public class ModelController {
    private static final Logger logger = LoggerFactory.getLogger(ModelController.class);

    @Autowired
    private ModelService modelService;

    @PostMapping("/list")
    @ApiOperation("模型管理列表")
    public ResultVo getModelInfoList(@RequestBody ModelCondition condition) {
        HashMap<String, Object> result = modelService.getModelInfolList(condition);
        if (ObjectUtils.isEmpty(result)) {
            return ResultVo.success("查询模型列表数据为空");
        } else {
            return ResultVo.success(result);
        }
    }

    @PostMapping("/update")
    @ApiOperation("模型生效切换")
    public ResultVo updateModelState(@RequestBody JSONObject params){
        CommonUtil.hasAllRequired(params, "model_id,state");
        Integer modelId = params.getInteger("model_id");
        Integer state = params.getInteger("state");
        Boolean isSuccess = modelService.updateModelState(modelId,state);

        if (isSuccess){
            return ResultVo.success("模型生效状态切换成功");
        }else{
            return ResultVo.fail("模型生效状态切换失败");
        }
    }
}
