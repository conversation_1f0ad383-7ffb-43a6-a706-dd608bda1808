package com.geeksec.general.controller.analysis;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description：聚合性查询数据
 */
@RestController
@RequestMapping("/aggr/target")
public class AggrTargetController {

    private final static Logger logger = LoggerFactory.getLogger(AggrTargetController.class);

    @Autowired
    private AggrTargetService aggrTargetService;

    /**
     * IP列表
     * @param condition
     * @return
     */
    @PostMapping("/ip/list")
    public ResultVo getAggrIpList(@RequestBody AnalysisBaseCondition condition){
        try {
            return aggrTargetService.getAggrIpList(condition);
        } catch (Exception e) {
            logger.error("会话索引 IP列表聚合查询失败，e->", e);
            throw new GkException(GkErrorEnum.IP_AGGR_QUERY_ERROR);
        }
    }

    /**
     * 域名列表
     * @param condition
     * @return
     */
    @PostMapping("/domain/list")
    public ResultVo<PageResultVo> getDomainListFor(@RequestBody AnalysisBaseCondition condition) {
        try {
            return aggrTargetService.getAggrDomainList(condition);
        } catch (Exception e) {
            logger.error("会话聚合，域名列表聚合查询失败，e={}", e);
            throw new GkException(GkErrorEnum.DOMAIN_AGGR_QUERY_ERROR);
        }
    }

    /**
     * 证书列表
     * @param condition
     * @return
     */
    @PostMapping("/cert/list")
    public ResultVo<PageResultVo> getCertListFor(@RequestBody AnalysisBaseCondition condition) {
        try {
            return aggrTargetService.getAggrCertList(condition);
        } catch (Exception e) {
            logger.error("会话聚合，证书列表聚合查询失败，e={}", e);
            throw new GkException(GkErrorEnum.CERT_AGGR_QUERY_ERROR);
        }
    }

    /**
     * 指纹列表
     * @param condition
     * @return
     */
    @PostMapping("/finger/list")
    public ResultVo<PageResultVo> getFingerAggrList(@RequestBody AnalysisBaseCondition condition){
        try{
            return aggrTargetService.getAggrFingerList(condition);
        }catch (Exception e){
            logger.error("会话聚合，指纹列表聚合查询失败, e={}",e);
            throw new GkException(GkErrorEnum.FINGER_AGGR_QUERY_ERROR);
        }
    }

    /**
     * 通信信息展示(桑基图)
     */
    @PostMapping("/communication/sankey")
    public ResultVo getCommunicationSankey(@RequestBody CommunicationCondition condition){
        if (ObjectUtils.isEmpty(condition) || condition.getTaskId() == null || condition.getTaskId().size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        return aggrTargetService.getCommunicationSankey(condition);
    }

}
