package com.geeksec.general.controller;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.TagSearchCondition;
import com.geeksec.analysis.entity.vo.TagAttributeVo;
import com.geeksec.analysis.service.TagService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.CommonUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：标签相关接口层
 */
@RestController
@RequestMapping("/tag")
public class TagController {

    @Autowired
    private TagService tagService;

    /**
     * 标签库查询
     *
     * @param condition
     * @return
     */
    @PostMapping("/search")
    public ResultVo searchTag(@RequestBody TagSearchCondition condition) {
        return tagService.tagSearch(condition);
    }

    /**
     * 添加新标签
     * @param params
     * @return
     */
    @PostMapping("/add")
    public ResultVo createNewTag(@RequestBody JSONObject params){
        if (MapUtils.isEmpty(params)){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        CommonUtil.hasAllRequired(params, "tag_text,black_list,white_list");
        return tagService.addTag(params);
    }

    /**
     * 查询标签分类
     */
    @GetMapping("/listTagAttribute")
    public ResultVo<List<TagAttributeVo>> listTagAttribute() {
        return tagService.listTagAttribute();
    }

    /**
     * 获取标签推荐数据
     * @return
     */
    @GetMapping("/session/recommend")
    public ResultVo<Map<String,Object>> getRecommondTagList(){
        return tagService.getRecommendTagList();
    }

}
