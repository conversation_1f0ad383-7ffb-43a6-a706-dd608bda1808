package com.geeksec.general.controller.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.DownloadListSearchCondition;
import com.geeksec.analysis.entity.condition.DownloadPcapCondition;
import com.geeksec.analysis.service.SessionListService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.CommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: GuanHao
 * @Date: 2022/5/5 14:54
 * @Description： <Functions List>
 */
@RestController
@Api(tags = "会话分析相关接口")
@RequestMapping("/session")
public class SessionAnalysisController {

    @Autowired(required = false)
    SessionListService sessionListService;


    @PostMapping("/list")
    @ApiOperation("会话列表查询")
    public JSONObject searchSessionList(@RequestBody AnalysisBaseCondition condition) {
        // 参数效验
        List<AnalysisBaseCondition.QueryOb> query = condition.getQuery();
        if (query == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        // 当taskId不存在值时，默认查询主任务，主任务Id为0
        List<Integer> taskId = condition.getTaskId();
        if (taskId == null || taskId.isEmpty()) {
            taskId = new ArrayList<>();
            taskId.add(0);
            condition.setTaskId(taskId);
        }

        return sessionListService.sessionList(condition);
    }

    @PostMapping("/tag/list")
    @ApiOperation("标签库查询")
    public JSONObject searchTagList() {
        return sessionListService.tagLibrary();
    }

    @PostMapping("/task/list")
    @ApiOperation("任务下拉列表查询")
    public JSONObject searchTaskList() {
        return sessionListService.taskList();
    }

    @PostMapping("/protocol/app")
    @ApiOperation("应用协议下拉列表查询")
    public JSONObject searchAppProtocol() {
        return sessionListService.protocol(1);
    }

    @PostMapping("/es/field")
    @ApiOperation("ES字段查询")
    public ResultVo searchEsField() {
        return sessionListService.esField();
    }

    @PostMapping("/download/prepare/pcap")
    @ApiOperation("pcap数据下载任务创建")
    public JSONObject pcapPrepare(@RequestBody DownloadPcapCondition condition) {
        // 参数不允许为null
        Integer userId = condition.getUserId();
        if (ObjectUtils.isEmpty(userId) || userId == 0) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // 判断是否为全量下载
        if (condition.getType()){
            // 全量下载 默认下载当前条件10000条数据
            return sessionListService.downloadPrepare(condition);
        }

        // 部分下载 判断
        List<DownloadPcapCondition.Session> sessionList = condition.getSessionId();

        // sessionId参数不允许少值
        if (CollectionUtils.isEmpty(sessionList)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        sessionList.forEach(session -> {
            JSONObject jsonObject = (JSONObject) JSON.toJSON(session);
            CommonUtil.hasAllRequired(jsonObject, "sessionId,taskId,batchId,threadId,startTime,endTime,firstProto");
        });

        return sessionListService.downloadPrepare(condition);
    }

    @PostMapping("/download/pcap/path")
    @ApiOperation("获取pcap数据下载地址")
    public JSONObject downloadPcap(@RequestBody JSONObject json) {

        Integer id = json.getInteger("id");
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return sessionListService.downloadPcap(id);
    }

    @PostMapping("/download/pcap/list")
    @ApiOperation("pcap下载列表查询")
    public JSONObject downloadPcapList(@RequestBody DownloadListSearchCondition condition) {
        // 参数效验
        Integer userId = condition.getUserId();
        if (userId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        return sessionListService.downloadPcapList(condition);
    }

    @PostMapping("/delete/record")
    @ApiOperation("pcap下载任务删除接口")
    public JSONObject deletePcapDownLoadRecord(@RequestBody JSONObject json) {

        Integer id = json.getInteger("id");
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return sessionListService.deletePcapRecord(id);
    }
}
