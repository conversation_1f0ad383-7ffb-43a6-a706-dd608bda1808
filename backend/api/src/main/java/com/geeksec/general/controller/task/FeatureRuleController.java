package com.geeksec.general.controller.task;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.entity.condition.FeatureRuleCondition;
import com.geeksec.analysis.entity.condition.FeatureRuleSearchCondition;
import com.geeksec.analysis.entity.condition.FeatureRuleStateCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.vo.FeatureCsvVo;
import com.geeksec.analysis.entity.vo.FeatureRuleVo;
import com.geeksec.analysis.service.AlarmService;
import com.geeksec.analysis.service.FeatureRuleService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.FileUtil;
import com.geeksec.util.HttpUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 特征规则controller
 */
@RestController
@RequestMapping("/feature")
@Log4j2
public class FeatureRuleController {

    @Autowired
    private FeatureRuleService featureRuleService;

    @Autowired
    private AlarmService alarmService;

    @Value("${file.tmp-path}")
    public String filePath;
    @Value("${send-url.rules}")
    private String url;
    @Value("${send-url.base}")
    private String base;
    @Value("${file.template-path}")
    private String templatePath;
    @Value("${send-url.check_so}")
    private String checkSo;

    @PostMapping(value = "/config/info")
    @ApiOperation(value = "任务特征规则：创建")
    public ResultVo addRule(@RequestParam("data") String data,
                            @RequestParam(required = false, value = "files") MultipartFile[] files) {
        ObjectMapper objectMapper = new ObjectMapper();
        FeatureRuleCondition condition = null;
        try {
            condition = objectMapper.readValue(data, FeatureRuleCondition.class);
        } catch (JsonProcessingException e) {
            log.error("创建特征规则，form-data转对象异常", e);
            throw new GkException(GkErrorEnum.FEATURE_IMPORT_ERROR);
        }
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        ResultVo resultVo = delFile(condition, files);
        if (resultVo != null) {
            return resultVo;
        }
        ResultVo resultVo1 = featureRuleService.addFeatureRule(condition);
        if (resultVo1.getErr() != 0) {
            return resultVo1;
        }
        List<Integer> endList = new ArrayList<>();
        endList.add(condition.getTaskId());
        endList.add(condition.getBatchId());
        // 添加完成后，刷新告警的规则列表
        alarmService.initKnowledgeType();

        return sendPost(JSONObject.toJSONString(endList));
    }

    @DeleteMapping(value = "/config/info")
    @ApiOperation(value = "任务特征规则：删除")
    public ResultVo deleteFeatureRule(@RequestBody FilterDeleteCondition condition) {
        Integer taskId = condition.getTaskId();
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        featureRuleService.deleteFeatureRule(condition);

        // 删除特征规则成功，刷新告警的规则列表
        alarmService.initKnowledgeType();

        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(condition.getBatchId());
        sendPost(JSONObject.toJSONString(endList));
        return ResultVo.success();
    }

    @PostMapping(value = "/config/info/update")
    @ApiOperation(value = "任务特征规则：更新")
    public ResultVo updateFeatureRule(@RequestParam("data") String data,
                                      @RequestParam(required = false, value = "files") MultipartFile[] files) {
        ObjectMapper objectMapper = new ObjectMapper();
        FeatureRuleCondition condition = null;
        try {
            condition = objectMapper.readValue(data, FeatureRuleCondition.class);
        } catch (JsonProcessingException e) {
            log.error("修改特征规则，form-data转对象异常,{}", e);
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        ResultVo resultVo = delFile(condition, files);
        if (resultVo != null) {
            return resultVo;
        }
        ResultVo resultVo1 = featureRuleService.updateFeatureRule(condition);
        if (resultVo1.getErr() != 0) {
            return resultVo1;
        }
        // 更新特征规则成功，刷新告警的规则列表
        alarmService.initKnowledgeType();

        List<Integer> endList = new ArrayList<>();
        endList.add(condition.getTaskId());
        endList.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(endList));
    }

    @PostMapping("/config")
    @ApiOperation(value = "任务特征规则：列表")
    public ResultVo<PageResultVo<FeatureRuleVo>> getFeatureRules(@RequestBody FeatureRuleSearchCondition condition) {
        String sortOrder = condition.getSortOrder();
        if (!("desc".equals(sortOrder)) && (!"asc".equals(sortOrder))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String orderField = condition.getOrderField();
        if (StringUtils.isNotBlank(orderField)) {
            if (!("created_time".equals(orderField) || "total_sum_bytes".equals(orderField) || "last_size_time".equals(orderField)
                    || "rule_state".equals(orderField)
                    || "updated_time".equals(orderField))) {

                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
        }
        Integer taskId = condition.getTaskId();
        if (taskId == null || taskId < 0 || taskId > 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return ResultVo.success(featureRuleService.getFeatureRules(condition));
    }

    @GetMapping(value = "/config/info/{id}")
    @ApiOperation(value = "任务特征规则：详情")
    public ResultVo getFeatureRule(@PathVariable("id") Long id) {
        return ResultVo.success(featureRuleService.getFeatureRule(id));
    }


    @PutMapping("/config/state")
    @ApiOperation(value = "任务特征规则：失效/有效")
    public ResultVo updateRuleState(@RequestBody FeatureRuleStateCondition condition) {
        String state = condition.getRuleState();
        Long id = condition.getId();
        if (StringUtils.isEmpty(state) || !(Constants.STATE_ON.equals(state) || Constants.STATE_OFF.equals(state))) {
            return ResultVo.fail("state参数异常");
        }
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        Integer taskId = condition.getTaskId();
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        featureRuleService.updateRuleState(id, state);
        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(endList));
    }

    /**
     * 特征规则的多文件上传
     *
     * @param condition
     * @param files
     * @return
     */
    private ResultVo delFile(FeatureRuleCondition condition, MultipartFile[] files) {
        Integer libRespondOpen = condition.getLibRespondOpen();
        Map<String, Object> detailRespond = condition.getDetailRespond();
        if (condition.getDetailRespond() != null && !detailRespond.isEmpty()) {
            //复杂规则  不处理文件流程
            return null;
        }
        if (libRespondOpen != null && libRespondOpen == 1) {
            //先处理文件 不为空  需要先生成base64的字段   conf的文件名这里需要处理(so的文件名后续生成)
            for (MultipartFile multipartFile : files) {
                InputStream in = null;
                String filePath = null;
                // 获取文件名
                String fileName = multipartFile.getOriginalFilename();
                // 获取文件的后缀名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));
                if (".so".equals(suffixName)
                        || ".conf".equals(suffixName)
                        || ".json".equals(suffixName)
                        || ".xml".equals(suffixName)
                        || ".txt".equals(suffixName)) {
                    try {
                        ResultVo resultVo = FileUtil.fileUpload(this.filePath, multipartFile);
                        if (resultVo.getErr() != 0) {
                            return resultVo;
                        }
                        String newName = resultVo.getData().toString();
                        filePath = this.filePath + newName;
                        byte[] bytes = Files.readAllBytes(Paths.get(filePath));

                        //写base64的文件
                        if (".so".equals(suffixName)) {
                            //校验so文件
                            checkSo(filePath);
                            condition.setLibDataSo(Base64.encode(bytes));
                        }
                        if (".conf".equals(suffixName)
                                || ".json".equals(suffixName)
                                || ".xml".equals(suffixName)
                                || ".txt".equals(suffixName)) {
                            condition.setLibDataConf(Base64.encode(bytes));
                            //conf的文件名为上传的名字
                            condition.setLibRespondConfig(fileName);
                        }

                    } catch (Exception e) {
                        throw new GkException(GkErrorEnum.FEATURE_IMPORT_ERROR);
                    } finally {
                        //删除临时文件
                        FileUtil.fileDelete(filePath);
                        if (in != null) {
                            try {
                                in.close();
                            } catch (IOException e) {
                                log.error("上传文件失败");
                                throw new GkException(GkErrorEnum.UPLOAD_FILE_FAIL);
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    @PostMapping("import")
    public ResultVo csvImport(@RequestParam("file") MultipartFile file,
                              @RequestParam("task_id") Integer task_id,
                              @RequestParam("batch_id") Integer batch_id) {
        if (!checkRuleParam(task_id, batch_id)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String filePath = "";
        try {
            //写到临时地点
            ResultVo resultVo = FileUtil.fileUpload(this.filePath, file);
            if (resultVo.getErr() != 0) {
                return resultVo;
            }
            String newName = resultVo.getData().toString();
            filePath = this.filePath + newName;
            CsvReader csvReader = CsvUtil.getReader();
            //进行读取
            CsvData csvData = csvReader.read(new File(filePath), CharsetUtil.CHARSET_UTF_8);
            //获取行数据
            List<CsvRow> rows = csvData.getRows();
            ResultVo<FeatureCsvVo> result = featureRuleService.csvImport(task_id, rows);
            if (result.getErr() != 0) {
                return result;
            }
            FeatureCsvVo data = result.getData();
            if (data.getSucNum() == 0) {
                //没有成功的数据
                return result;
            }
            List<Integer> endList = new ArrayList<>();
            endList.add(task_id);
            endList.add(batch_id);
            ResultVo syncVo = sendPost(JSONObject.toJSONString(endList));
            if (syncVo.getErr() != 0) {
                //同步成功
                data.setSyncMag("探针同步成功");
            } else {
                data.setSyncMag("探针同步失败");
            }
            return result;
        } catch (Exception e) {
            log.error("导入CSV文件失败,error->",e);
            throw new GkException(GkErrorEnum.UPLOAD_CSV_FILE_FAIL);
        } finally {
            //删除临时文件
            FileUtil.fileDelete(filePath);
        }
    }

    @PostMapping("/getCsv")
    public ResultVo getCsv(HttpServletResponse response, @RequestBody FilterDeleteCondition condition) {
        Integer taskId = condition.getTaskId();
        if (taskId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        List<List<String>> list = featureRuleService.getCsv(condition);
        // 写数据
        CsvWriter writer = null;
        String fileName = new Date().getTime() + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(list);
            FileUtil.csvDownloadFile(response, csvFile);
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new GkException(GkErrorEnum.CSV_EXPORT_FAIL);
        } finally {
            FileUtil.fileDelete(fileName);
            if (writer != null)
                writer.close();
        }
        return ResultVo.success();
    }

    /**
     * 特征规则模板文件下载
     * @param response
     * @return
     */
    @GetMapping("/template")
    public ResultVo getTemplateCsv(HttpServletResponse response){
        // 获取文件
        String path = templatePath+Constants.FEATURE_RULE_FILE;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FEATURE_RULE_TEMPLATE_DOWNLOAD_FAIL);
            }
        } else {
            return ResultVo.fail("请先初始化特征规则模板文件");
        }
        return ResultVo.success();
    }

    @GetMapping("/template/zip")
    public ResultVo getTemplateZip(HttpServletResponse response){
        // 获取文件
        String path = templatePath+Constants.FEATURE_RULE_ZIP;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FEATURE_RULE_TEMPLATE_DOWNLOAD_FAIL);
            }
        } else {
            return ResultVo.fail("请先初始化动态库规则-模板文件");
        }
        return ResultVo.success();
    }

    private ResultVo sendPost(String json) {
        if (StringUtils.isEmpty(url)) {
            throw new GkException(GkErrorEnum.RULE_SYNC_URL_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base + url, json);
        if (resp == null) {
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
        return ResultVo.successMsg("探针同步成功");
    }

    private Boolean checkRuleParam(Integer taskId, Integer batchId) {
        if (taskId == null || taskId < 0 || taskId > 1) {
            return false;
        }
        if (batchId == null) {
            return false;
        }
        return true;
    }

    /**
     * 动态库文件检测
     * @param tempUrl  临时文件地址
     * @return
     */
    private void checkSo(String tempUrl) {
        List<String> list = new ArrayList<>();
        list.add(tempUrl);
        if (StringUtils.isEmpty(checkSo)) {
            throw new GkException(GkErrorEnum.CHECK_SO_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base + checkSo, JSON.toJSONString(list));
        if (resp == null) {
            throw new GkException(GkErrorEnum.CHECK_SO_FAIL);
        }
        try {
            Boolean message = resp.getBoolean("message");
            if(message != null && message){
                return;
            }
        }catch (Exception e){
            //尝试执行
            log.error("so check，判断message异常，e={}",e);
            throw new GkException(GkErrorEnum.CHECK_SO_FAIL);
        }
    }
}
