package com.geeksec.general.controller.auth;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.service.LoginService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.geeksec.entity.common.ResultVo;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: heeexy
 * @description: 登录相关Controller
 * @date: 2017/10/24 10:33
 */
@RestController
@Api(tags = "登陆相关接口")
public class LoginController {

    @Autowired
    private LoginService loginService;

    /**
     * 登录
     */
    @PostMapping("/login")
    public ResultVo authLogin(@RequestBody JSONObject requestJson) {
        CommonUtil.hasAllRequired(requestJson, "username,password");
        return loginService.authLogin(requestJson);
    }

    /**
     * 远程跳转登陆
     */
    @PostMapping("/remoteLogin")
    public JSONObject remoteAuthLogin(@RequestBody JSONObject params) {
        CommonUtil.hasAllRequired(params, "username,identification");
        String token = loginService.remoteAuthLogin(params);
        JSONObject result = new JSONObject();
        if (StringUtils.isNotBlank(token)) {
            result.put("token", token);
            return result;
        } else {
            return CommonUtil.errorJson(ErrorEnum.E_40002);
        }
    }

    /**
     * 登出
     * sa-token 指定弹出登录
     */
    @PostMapping("/logout")
    public ResultVo logout() {
        return loginService.logout();
    }
}
