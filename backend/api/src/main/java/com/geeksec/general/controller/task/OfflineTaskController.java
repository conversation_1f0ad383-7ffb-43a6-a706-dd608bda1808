package com.geeksec.general.controller.task;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.entity.condition.*;
import com.geeksec.analysis.entity.dto.*;
import com.geeksec.analysis.entity.vo.*;
import com.geeksec.analysis.service.*;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.condition.workbench.InternalNetCondition;
import com.geeksec.general.service.WorkBenchService;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.FileUtil;
import com.geeksec.util.HttpUtils;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

@RestController
@Api(tags = "离线分析任务管理")
@RequestMapping("/offline")
@Log4j2
public class OfflineTaskController {

    @Autowired
    private OfflineTaskService offlineTaskService;
    @Autowired
    private WorkBenchService workBenchService;
    @Autowired
    private OfflineTaskBatchService offlineTaskBatchService;
    @Value("${file.pcap-path}")
    private String targetPcapPath;
    private static final Logger logger = LoggerFactory.getLogger(OfflineTaskController.class);
    @Autowired
    private FilterRuleService filterRuleService;
    @Value("${file.template-path}")
    private String templatePath;
    @Value("${file.tmp-path}")
    public String filePath;
    @Autowired
    private FeatureRuleService featureRuleService;
    @Autowired
    private AlarmService alarmService;
    @Value("${send-url.base}")
    private String base;
    @Value("${send-url.check_so}")
    private String checkSo;

    /**********************************任务管理**********************************/

    /**
    * 查询当前任务
    */
    @GetMapping("/task/last")
    public ResultVo<OfflineTaskVo> getLastTask() {
        return ResultVo.success(offlineTaskService.getLastTask());
    }

    /**
     * 任务详情
     */
    @GetMapping("/task/get")
    public ResultVo<OfflineTaskVo> getTask(@RequestParam("task_id") Integer taskId) {
        if (taskId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return ResultVo.success(offlineTaskService.getTask(taskId));
    }

    /**
     * 任务列表
     */
    @PostMapping("/task/page")
    public ResultVo<PageVo<OfflineTaskPageVo>> pageTask(@RequestBody OfflineTaskQueryCondition condition) {
        if (condition == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return ResultVo.success(offlineTaskService.pageTask(condition));
    }

    /**
    * 创建任务
    */
    @PostMapping("/task/add")
    public ResultVo addTask(@RequestBody OfflineTaskDto dto) {
        return offlineTaskService.addTask(dto);
    }

    /**
     * 任务配置
     */
    @PostMapping("/task/update")
    public ResultVo updateTask(@RequestBody OfflineTaskDto dto) {
        return offlineTaskService.updateTask(dto);
    }

    /**
     * 删除任务
     */
    @PostMapping("/task/delete")
    public ResultVo deleteTask(@RequestBody OfflineTaskDeleteDto dto) {
        return offlineTaskService.deleteTask(dto);
    }

    /**********************************任务态势管理**********************************/

    /**
     * 查询任务态势
     */
    @PostMapping("/internal/list")
    public ResultVo<HashMap<String, Object>> listInternal(@RequestBody InternalNetCondition condition) {
        if (condition.getSortOrder() == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        HashMap<String, Object> result = workBenchService.getInternalNetList(condition);
        return ResultVo.success(result);
    }

    /**
    * 添加任务态势
    */
    @PostMapping("/internal/add")
    public JSONObject addInternal(@RequestBody AddInternalNetCondition condition) {
        return workBenchService.addInternalNetInfo(condition);
    }

    /**
     * 编辑任务态势
     */
    @PostMapping("/internal/update")
    public ResultVo updateInterInfo(@RequestBody UpdateInternalNetCondition condition) {
        if (ObjectUtils.isEmpty(condition) && condition.getId() <= 0) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        boolean success = workBenchService.updateInternalNetInfo(condition);
        return ResultVo.success(success);
    }

    /**
     * 删除任务态势
     */
    @PostMapping("/internal/delete")
    public ResultVo deleteInterInfo(@RequestBody JSONObject requestJson) {
        CommonUtil.hasAllRequired(requestJson, "ids");
        List<Integer> ids = requestJson.getObject("ids", List.class);
        boolean success = workBenchService.deleteInterInfoByIds(ids);
        return ResultVo.success(success);
    }

    /**********************************数据管理**********************************/

    /**
     * 数据管理列表
     */
    @PostMapping("/batch/page")
    public ResultVo<PageVo<OfflineTaskBatchPageVo>> pageBatch(@RequestBody OfflineTaskBatchQueryCondition condition) {
        if (condition == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return ResultVo.success(offlineTaskBatchService.pageBatch(condition));
    }

    /**
     * 查询服务器文件路径
     */
    @GetMapping("/listServerPath")
    public ResultVo<List<FileTreeNodeVo>> listServerPath(@RequestParam("directory_path") String directoryPath) {
        return ResultVo.success(offlineTaskBatchService.listServerPath(directoryPath));
    }

    /**
     * 数据导入
     */
    @PostMapping("/batch/add")
    public ResultVo addBatch(@RequestBody OfflineTaskBatchDto dto) {
        return offlineTaskBatchService.addBatch(dto);
    }

    /**
     * 取消数据导入
     */
    @PostMapping("/batch/cancel")
    public ResultVo cancelBatch(@RequestBody OfflineTaskBatchCancelDto dto) {
        return offlineTaskBatchService.cancelBatch(dto);
    }

    /**
     * 数据导入（服务器数据）
     */
    @PostMapping("/batch/server/add")
    public ResultVo addServerBatch(@RequestParam("task_id") Integer taskId,
                                   @RequestParam("batch_description") String batchDescription,
                                   @RequestParam("fullflow_state") String fullflowState,
                                   @RequestParam("flowlog_state") String flowlogState,
                                   @RequestParam(required = false, value = "file_list") List<String> fileList) {
        OfflineTaskBatchDto offlineTaskBatchDto = new OfflineTaskBatchDto();
        offlineTaskBatchDto.setBatchType(1);
        offlineTaskBatchDto.setTaskId(taskId);
        offlineTaskBatchDto.setBatchDescription(batchDescription);
        offlineTaskBatchDto.setFullflowState(fullflowState);
        offlineTaskBatchDto.setFlowlogState(flowlogState);
        List<OfflineFilePathDto> filePathList = new ArrayList<>();
        for (String filePath : fileList) {
            OfflineFilePathDto offlineFilePathDto = new OfflineFilePathDto();
            offlineFilePathDto.setServerPath(filePath);
            offlineFilePathDto.setLocalPath(filePath);
            filePathList.add(offlineFilePathDto);
        }
        offlineTaskBatchDto.setFilePathList(filePathList);
        return offlineTaskBatchService.addBatch(offlineTaskBatchDto);
    }

    /**
     * 数据导入（本地上传数据）
     */
    @PostMapping(value = "/batch/local/add")
    public ResultVo addLocalBatch(@RequestParam("task_id") Integer taskId,
                                  @RequestParam("batch_description") String batchDescription,
                                  @RequestParam("fullflow_state") String fullflowState,
                                  @RequestParam("flowlog_state") String flowlogState,
                                  @RequestParam(required = false, value = "file_list") MultipartFile[] fileList) {
        OfflineTaskBatchDto offlineTaskBatchDto = new OfflineTaskBatchDto();
        offlineTaskBatchDto.setBatchType(2);
        offlineTaskBatchDto.setTaskId(taskId);
        offlineTaskBatchDto.setBatchDescription(batchDescription);
        offlineTaskBatchDto.setFullflowState(fullflowState);
        offlineTaskBatchDto.setFlowlogState(flowlogState);
        List<OfflineFilePathDto> filePathList = new ArrayList<>();
        for (MultipartFile file : fileList) {
            ResultVo resultVo = FileUtil.fileUpload(targetPcapPath,file);
            OfflineFilePathDto offlineFilePathDto = new OfflineFilePathDto();
            String filePath = targetPcapPath+resultVo.getData();
            offlineFilePathDto.setServerPath(filePath);
            offlineFilePathDto.setLocalPath(filePath);
            filePathList.add(offlineFilePathDto);
        }
        offlineTaskBatchDto.setFilePathList(filePathList);
        return offlineTaskBatchService.addBatch(offlineTaskBatchDto);
    }

    @PostMapping("/uploadFile")
    public ResultVo uploadFile(@RequestParam("file") MultipartFile file) {
        UploadFileVo uploadFileVo = new UploadFileVo();
        try {
            ResultVo resultVo = FileUtil.fileUpload(targetPcapPath,file);
            uploadFileVo.setFilePath(targetPcapPath+resultVo.getData());
            uploadFileVo.setFileName(file.getOriginalFilename());
        } catch (Exception e) {
            logger.error("{} 上传失败!,error ->", file.getOriginalFilename(), e);
            return ResultVo.fail("上传PCAP文件失败");
        }
        return ResultVo.success(uploadFileVo);
    }

    /**********************************过滤规则管理**********************************/

    /**
    * 查询过滤规则
    */
    @PostMapping(value = "/filter/list")
    public ResultVo<PageResultVo<FilterConfigVo>> listFilter(@RequestBody FilterRuleCondition condition) {
        String orderField = condition.getOrderField();
        String sortOrder = condition.getSortOrder();
        if (condition.getTaskId() == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        if (StringUtils.isNotBlank(orderField)) {
            if (!("id".equals(orderField)
                    || "update_time".equals(orderField))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
            if (!("desc".equals(sortOrder) || "asc".equals(sortOrder))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
        }
        PageResultVo<FilterConfigVo> filterConfigList = filterRuleService.getFilterConfigList(condition);
        return ResultVo.success(filterConfigList);
    }

    /**
    * 添加过滤规则
    */
    @PostMapping(value = "/filter/add")
    public ResultVo addFilter(@RequestBody FilterConfigInCondition condition) {
        FilterInfoVo filterInfoVo = condition.getFilterInfo();
        if (filterInfoVo == null){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        filterRuleService.addConfig(condition);
        return ResultVo.success();
    }

    /**
    * 编辑过滤规则
    */
    @PutMapping(value = "/filter/update")
    public ResultVo updateFilter(@RequestBody FilterConfigInCondition condition) {
        FilterInfoVo filterInfoVo = condition.getFilterInfo();
        if (filterInfoVo == null){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        filterRuleService.updateConfig(condition);
        return ResultVo.success();
    }

    /**
    * 删除过滤规则
    */
    @DeleteMapping(value = "/filter/delete")
    public ResultVo deleteFilter(@RequestBody FilterDeleteCondition condition) {
        filterRuleService.deleteConfig(condition);
        return ResultVo.success();
    }

    /**
    * 过滤规则导入模版下载
    */
    @GetMapping("/filter/template")
    public ResultVo getFilterTemplate(HttpServletResponse response) {
        // 获取文件
        String path = templatePath + Constants.FILTER_RULE_FILE;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
            }
        } else {
            return ResultVo.fail("请先初始化过滤规则模板文件");
        }
        return ResultVo.success();
    }

    /**
    * 导出过滤规则
    */
    @PostMapping("/filter/getCsv/{task_id}")
    public ResultVo exportFilter(@PathVariable("task_id") Integer task_id, HttpServletResponse response, HttpServletRequest request) {
        List<List<String>> list = filterRuleService.getCsv(task_id);
        // 写数据
        CsvWriter writer = null;
        String fileName = new Date().getTime() + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(list);
            FileUtil.csvDownloadFile(response, csvFile);
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new GkException(GkErrorEnum.CSV_EXPORT_FAIL);
        } finally {
            FileUtil.fileDelete(fileName);
            if (writer != null)
                writer.close();
        }
        return ResultVo.success();
    }

    /**
    * 导入过滤规则
    */
    @PostMapping(value = "/filter/csvImport")
    public ResultVo<FilterCsvVo> importFilter(@RequestParam("file") MultipartFile file,
                                           @RequestParam("task_id") Integer task_id) {
        String filePath = "";
        try {
            //写到临时地点
            ResultVo resultVo = FileUtil.fileUpload(this.filePath, file);
            if (resultVo.getErr() != 0) {
                return resultVo;
            }
            String newName = resultVo.getData().toString();
            filePath = this.filePath + newName;
            CsvReader csvReader = CsvUtil.getReader();
            //进行读取
            CsvData csvData = csvReader.read(new File(filePath), CharsetUtil.CHARSET_UTF_8);
            if (csvData.getRowCount() > 10001) {
                throw new GkException(GkErrorEnum.DOWNLOAD_FILE_COUNT_OVERATE);
            }
            //获取行数据
            List<CsvRow> rows = csvData.getRows();

            ResultVo<FilterCsvVo> result = filterRuleService.addConfigByCSV(task_id, rows);
            if (result.getErr() != 0) {
                return result;
            }
            FilterCsvVo data = result.getData();
            if (data.getSucNum() == 0) {
                //没有成功的数据
                return result;
            }
            return result;
            //根据临时点 生成File
        } catch (Exception e) {
            log.error("导入过滤规则异常e={}", e);
            throw new GkException(GkErrorEnum.FILTER_RULE_IMPORT_ERROR);
        } finally {
            //删除临时文件
            FileUtil.fileDelete(filePath);
        }
    }

    /**
    * 修改命中留存/丢弃
    */
    @PutMapping("/filter/state")
    public ResultVo modifyFilterSate(@RequestBody FilterRuleCondition condition) {
        Integer state = condition.getState();
        if (condition.getTaskId() == null || state == null || state < 0 || state > 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        filterRuleService.modifyFilterSate(condition);
        return ResultVo.success();
    }

    /**
    * 获取任务过滤的命中留存/丢弃状态
    */
    @GetMapping("/filter/state")
    public ResultVo<FilterStateVo> getFilterStateByTaskId(@RequestParam Integer task_id) {
        if (task_id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        FilterStateVo filterStateByTaskId = filterRuleService.getFilterStateByTaskId(task_id);
        return ResultVo.success(filterStateByTaskId);
    }

    /**********************************特征规则管理**********************************/

    /**
    * 添加特征规则
    */
    @PostMapping(value = "/feature/add")
    public ResultVo addFeature(@RequestParam("data") String data,
                            @RequestParam(required = false, value = "files") MultipartFile[] files) {
        ObjectMapper objectMapper = new ObjectMapper();
        FeatureRuleCondition condition = null;
        try {
            condition = objectMapper.readValue(data, FeatureRuleCondition.class);
        } catch (JsonProcessingException e) {
            log.error("创建特征规则，form-data转对象异常,{}", e);
            throw new GkException(GkErrorEnum.FEATURE_RULE_ADD_ERROR);
        }
        ResultVo resultVo = delFeatureFile(condition, files);
        if (resultVo != null) {
            return resultVo;
        }
        ResultVo resultVo1 = featureRuleService.addFeatureRule(condition);
        if (resultVo1.getErr() != 0) {
            return resultVo1;
        }
        // 添加完成后，刷新告警的规则列表
        alarmService.initKnowledgeType();
        return ResultVo.success();
    }

    /**
    * 编辑特征规则
    */
    @PostMapping(value = "/feature/update")
    public ResultVo updateFeature(@RequestParam("data") String data,
                                      @RequestParam(required = false, value = "files") MultipartFile[] files) {
        ObjectMapper objectMapper = new ObjectMapper();
        FeatureRuleCondition condition = null;
        try {
            condition = objectMapper.readValue(data, FeatureRuleCondition.class);
        } catch (JsonProcessingException e) {
            log.error("修改特征规则，form-data转对象异常,{}", e);
            throw new GkException(GkErrorEnum.UPDATE_FEATURE_RULE_ERROR);
        }

        ResultVo resultVo = delFeatureFile(condition, files);
        if (resultVo != null) {
            return resultVo;
        }
        ResultVo resultVo1 = featureRuleService.updateFeatureRule(condition);
        if (resultVo1.getErr() != 0) {
            return resultVo1;
        }
        // 更新特征规则成功，刷新告警的规则列表
        alarmService.initKnowledgeType();
        return ResultVo.success();
    }

    /**
    * 删除特征规则
    */
    @DeleteMapping(value = "/feature/delete")
    public ResultVo deleteFeature(@RequestBody FilterDeleteCondition condition) {
        featureRuleService.deleteFeatureRule(condition);
        // 删除特征规则成功，刷新告警的规则列表
        alarmService.initKnowledgeType();
        return ResultVo.success();
    }



    /**
    * 查询特征规则
    */
    @PostMapping("/feature/list")
    public ResultVo<PageResultVo<FeatureRuleVo>> listFeature(@RequestBody FeatureRuleSearchCondition condition) {
        String sortOrder = condition.getSortOrder();
        if (!("desc".equals(sortOrder)) && (!"asc".equals(sortOrder))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String orderField = condition.getOrderField();
        if (StringUtils.isNotBlank(orderField)) {
            if (!("created_time".equals(orderField) || "total_sum_bytes".equals(orderField) || "last_size_time".equals(orderField)
                    || "rule_state".equals(orderField)
                    || "updated_time".equals(orderField))) {

                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
        }
        return ResultVo.success(featureRuleService.getFeatureRules(condition));
    }

    /**
    * 特征规则详情
    */
    @GetMapping(value = "/feature/get/{id}")
    public ResultVo<FeatureRuleVo> getFeatureRule(@PathVariable("id") Long id) {
        return ResultVo.success(featureRuleService.getFeatureRule(id));
    }

    /**
    * 特征规则状态变更
    */
    @PutMapping("/feature/state")
    public ResultVo updateRuleState(@RequestBody FeatureRuleStateCondition condition) {
        String state = condition.getRuleState();
        Long id = condition.getId();
        if (StringUtils.isEmpty(state) || !(Constants.STATE_ON.equals(state) || Constants.STATE_OFF.equals(state))) {
            return ResultVo.fail("state参数异常");
        }
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        featureRuleService.updateRuleState(id, state);
        return ResultVo.success();
    }

    /**
    * 特征规则导入模版下载
    */
    @GetMapping("/feature/template")
    public ResultVo getFeatureTemplate(HttpServletResponse response){
        // 获取文件
        String path = templatePath+Constants.FEATURE_RULE_FILE;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FILTER_RULE_TEMPLATE_DOWNLOAD_FAIL);
            }
        } else {
            return ResultVo.fail("请先初始化特征规则模板文件");
        }
        return ResultVo.success();
    }

    /**
    * 特征规则动态库模版下载
    */
    @GetMapping("/feature/template/zip")
    public ResultVo getFeatureTemplateZip(HttpServletResponse response){
        // 获取文件
        String path = templatePath+Constants.FEATURE_RULE_ZIP;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FEATURE_RULE_TEMPLATE_DOWNLOAD_FAIL);
            }
        } else {
            return ResultVo.fail("请先初始化动态库规则-模板文件");
        }
        return ResultVo.success();
    }

    /**
    * 导入特征规则
    */
    @PostMapping("/feature/import")
    public ResultVo csvImport(@RequestParam("file") MultipartFile file,
                              @RequestParam("task_id") Integer task_id) {
        String filePath = "";
        try {
            //写到临时地点
            ResultVo resultVo = FileUtil.fileUpload(this.filePath, file);
            if (resultVo.getErr() != 0) {
                return resultVo;
            }
            String newName = resultVo.getData().toString();
            filePath = this.filePath + newName;
            CsvReader csvReader = CsvUtil.getReader();
            //进行读取
            CsvData csvData = csvReader.read(new File(filePath), CharsetUtil.CHARSET_UTF_8);
            //获取行数据
            List<CsvRow> rows = csvData.getRows();
            ResultVo<FeatureCsvVo> result = featureRuleService.csvImport(task_id, rows);
            if (result.getErr() != 0) {
                return result;
            }
            FeatureCsvVo data = result.getData();
            if (data.getSucNum() == 0) {
                //没有成功的数据
                return result;
            }
            return result;
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.FEATURE_IMPORT_ERROR);
        } finally {
            //删除临时文件
            FileUtil.fileDelete(filePath);
        }
    }

    /**
    * 导出特征规则
    */
    @PostMapping("/feature/getCsv")
    public ResultVo getCsv(HttpServletResponse response, @RequestBody FilterDeleteCondition condition) {
        Integer taskId = condition.getTaskId();
        if (taskId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        List<List<String>> list = featureRuleService.getCsv(condition);
        // 写数据
        CsvWriter writer = null;
        String fileName = new Date().getTime() + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(list);
            FileUtil.csvDownloadFile(response, csvFile);
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new GkException(GkErrorEnum.CSV_EXPORT_FAIL);
        } finally {
            FileUtil.fileDelete(fileName);
            if (writer != null)
                writer.close();
        }
        return ResultVo.success();
    }

    /**
     * 特征规则的多文件上传
     *
     * @param condition
     * @param files
     * @return
     */
    private ResultVo delFeatureFile(FeatureRuleCondition condition, MultipartFile[] files) {
        Integer libRespondOpen = condition.getLibRespondOpen();
        Map<String, Object> detailRespond = condition.getDetailRespond();
        if (condition.getDetailRespond() != null && detailRespond.size() > 0) {
            //复杂规则  不处理文件流程
            return null;
        }
        if (libRespondOpen != null && libRespondOpen == 1) {
            //先处理文件 不为空  需要先生成base64的字段   conf的文件名这里需要处理(so的文件名后续生成)
            for (MultipartFile multipartFile : files) {
                InputStream in = null;
                String filePath = null;
                // 获取文件名
                String fileName = multipartFile.getOriginalFilename();
                // 获取文件的后缀名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));
                if (".so".equals(suffixName)
                        || ".conf".equals(suffixName)
                        || ".json".equals(suffixName)
                        || ".xml".equals(suffixName)
                        || ".txt".equals(suffixName)) {
                    try {
                        ResultVo resultVo = FileUtil.fileUpload(this.filePath, multipartFile);
                        if (resultVo.getErr() != 0) {
                            return resultVo;
                        }
                        String newName = resultVo.getData().toString();
                        filePath = this.filePath + newName;
                        byte[] bytes = Files.readAllBytes(Paths.get(filePath));

                        //写base64的文件
                        if (".so".equals(suffixName)) {
                            //校验so文件
                            ResultVo vo = checkSo(filePath);
                            if(vo!=null){
                                return vo;
                            }
                            condition.setLibDataSo(cn.hutool.core.codec.Base64.encode(bytes));
                        }
                        if (".conf".equals(suffixName)
                                || ".json".equals(suffixName)
                                || ".xml".equals(suffixName)
                                || ".txt".equals(suffixName)) {
                            condition.setLibDataConf(Base64.encode(bytes));
                            //conf的文件名为上传的名字
                            condition.setLibRespondConfig(fileName);
                        }

                    } catch (Exception e) {
                        throw new GkException(GkErrorEnum.FEATURE_RULE_DELETE_ERROR);
                    } finally {
                        //删除临时文件
                        FileUtil.fileDelete(filePath);
                        if (in != null) {
                            try {
                                in.close();
                            } catch (IOException e) {
                                log.error("上传文件失败");

                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 动态库文件检测
     * @param tempUrl  临时文件地址
     * @return
     */
    private ResultVo checkSo(String tempUrl) {
        List<String> list = new ArrayList<>();
        list.add(tempUrl);
        if (StringUtils.isEmpty(checkSo)) {
            throw new GkException(GkErrorEnum.CHECK_SO_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base + checkSo, JSON.toJSONString(list));
        if (resp == null) {
            return ResultVo.fail("检测so文件失败");
        }
        try {
            Boolean message = resp.getBoolean("message");
            if(message != null && message){
                return null;
            }
        }catch (Exception e){
            //尝试执行
            log.error("so check，判断message异常，e={}",e);
        }
        return ResultVo.fail("检测so文件失败");
    }

}
