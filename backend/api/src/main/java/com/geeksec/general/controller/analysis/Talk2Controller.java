package com.geeksec.general.controller.analysis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.NbLabelUpCondition;
import com.geeksec.analysis.entity.condition.NbRemarkUpCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.analysis.service.CertLogTemplateService;
import com.geeksec.analysis.service.Metadata2Service;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.service.VertexService;
import com.geeksec.ngbatis.vo.VertexAssociationNextVo;
import com.geeksec.ngbatis.vo.VertexAssociationVo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/analyze")
@Log4j2
public class Talk2Controller {

    @Value("${enabled.atlas}")
    private Boolean hasAtlas;

    @Autowired
    private Metadata2Service metadata2Service;

    @Autowired
    private VertexService vertexService;

    @Autowired
    private CertLogTemplateService certLogTemplateService;

    /**
     * IP 域名 证书 详情关系图(旧接口)
     * @param str
     * @param type
     * @return
     */
    @GetMapping("/ip/domain/edge")
    public ResultVo getDemo(@RequestParam("str") String str, @RequestParam("type") String type) {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationVo vertexAssociationVo = vertexService.getAssociation(str, type);
        return ResultVo.success(vertexAssociationVo);
    }

    @PostMapping("/ip/domain/edge/next")
    public ResultVo getDemoNext(@RequestBody GraphNextInfoCondition condition) {
        String str = condition.getStr();
        String type = condition.getType();
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        List<GraphNextInfoCondition.EdgeNum> edgeInfo = condition.getEdgeInfo();
        for (GraphNextInfoCondition.EdgeNum edgeNum : edgeInfo) {
            if (StringUtils.isEmpty(edgeNum.getEdge()) || edgeNum.getNum() < 1) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getAssociationNext(condition);
        return ResultVo.success(vertexAssociationNextVo);
    }

    @PostMapping("/edge/properties/next")
    public ResultVo getEdgePropertiesNext(@RequestBody GraphPropertiesNextCondition condition) {
        if (StringUtils.isEmpty(condition.getTagName()) || StringUtils.isEmpty(condition.getPropertiesName()) || StringUtils.isEmpty(condition.getPropertiesValue())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getEdgePropertiesNext(condition);
        return ResultVo.success(vertexAssociationNextVo);
    }


    @GetMapping("/tag/edge/json")
    public ResultVo getJson() throws IOException {
        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            // 读取parse_conf文件
            InputStream resourceAsStream = null;
            if (hasAtlas) {
                resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("dict/atlas/parse_conf.json");
            } else {
                resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("dict/analysis/parse_conf.json");
            }
            if (resourceAsStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(resourceAsStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }

            String jsonStr = stringBuilder.toString();
            return ResultVo.success(JSONArray.parseArray(jsonStr));
        } catch (Exception e) {
            log.error("读取parse_conf文件失败！error->", e);

        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return ResultVo.fail("图关联JSON文件未初始化");
    }

    /**
     * 查询IP详情（侧拉框）
     * @param str
     * @return
     */
    @GetMapping("/ip/info")
    public ResultVo<IpInfoVo> getIpInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getIpInfo(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("会话侧拉，IP详情，e={}", e);
            throw new GkException(GkErrorEnum.IP_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 查询证书详情
     *
     * @param str
     * @return
     */
    @GetMapping("/cert/info")
    public ResultVo getCertInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str))   {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try{
            return metadata2Service.getCertDetail(str);
        }catch (Exception e){
            log.error("证书详情查询失败,e->",e);
            throw new GkException(GkErrorEnum.CERT_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 查询当前用户的证书元数据模板样式JSON
     *
     * @param userId
     * @return
     */
    @GetMapping("/cert/log/template")
    public ResultVo getCertLogTemplate(@RequestParam(value = "user_id") Integer userId) {
        return certLogTemplateService.getUserTemplate(userId);
    }

    /**
     * 修改当前用户的证书元数据模板样式JSON
     *
     * @param params
     * @return
     */
    @PutMapping("/cert/log/template")
    public ResultVo modifyCertLogTemplate(@RequestBody JSONObject params) {
        Integer userId = params.getInteger("user_id");
        String key = params.getString("key");
        String value = params.getString("value");
        if (userId == null || StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return certLogTemplateService.modifyUserTemplate(userId, key, value);
    }

    /**
     * 查询域名详情
     * @param str
     * @return
     */
    @GetMapping("/domain/info")
    public ResultVo<DomainInfoVo> getDomainInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getDomainInfo(str);
        } catch (Exception e) {
            log.error("域名详情查询失败，e->", e);
            throw new GkException(GkErrorEnum.DOMAIN_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 查询企业详情
     * @param str
     * @return
     */
    @GetMapping("/org/info")
    public ResultVo<OrgInfoVo> getOrgInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return metadata2Service.getOrgInfo(str);
    }

    /**
     * 指纹详情
     * @param str
     * @return
     */
    @GetMapping("/sslfinger/info")
    public ResultVo<SSLFingerInfoVo> getFingerInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getSSLFingerInfo(str);
        } catch (Exception e) {
            log.error("指纹详情信息查询失败，e->", e);
            throw new GkException(GkErrorEnum.FINGER_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 应用服务详情
     * @param str
     * @return
     */
    @GetMapping("/appservice/info")
    public ResultVo<AppServiceInfoVo> getAppService(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        try {
            return metadata2Service.getAppService(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("应用服务详情信息查询失败，e={}", e);
            throw new GkException(GkErrorEnum.APPSERVICE_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 应用详情
     * @param str
     * @return
     */
    @GetMapping("/app/info")
    public ResultVo<AppInfoVo> getApp(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        try {
            return metadata2Service.getApp(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("应用详情信息查询失败，e={}", e);
            throw new GkException(GkErrorEnum.APP_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 侧拉框修改标签
     *
     * @param condition
     * @return
     */
    @PutMapping("label")
    public ResultVo updateLabels(@RequestBody NbLabelUpCondition condition) {
        try {
            return metadata2Service.updateLabels(condition);
        } catch (Exception e) {
            log.error("修改会话标签失败，e->", e);
            throw new GkException(GkErrorEnum.UPDATE_LABELS_ERROR);
        }
    }

    /**
     * Nebula侧拉框修改备注
     * @param condition
     * @return
     */
    @PutMapping("remark")
    public ResultVo updateRemark(@RequestBody NbRemarkUpCondition condition) {
        try {
            return metadata2Service.updateRemark(condition);
        } catch (Exception e) {
            log.error("修改实体备注信息失败，e->", e);
            throw new GkException(GkErrorEnum.UPDATE_REMARK_ERROR);
        }
    }

    @GetMapping("/redis/test")
    public ResultVo getRedisTest(@RequestParam("key") String key, @RequestParam("value") String value) {
        return metadata2Service.getRedisTest(key, value);
    }


}
