package com.geeksec.general.condition.workbench;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: GuanHao
 * @Date: 2022/8/16 15:12
 * @Description： <Functions List>
 */
@Data
public class InformationCondition {

    /**
     * 是否全部
     */
    @JsonProperty("whole")
    boolean whole = false;

    /**
     * 导出列表
     */
    @JsonProperty("information_list")
    List<Integer> informationList;

    /**
     * 是否升序
     */
    @JsonProperty("asc")
    private Boolean asc = true;

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    @ApiModelProperty("当前页")
    private Integer currentPage = 1;

    /**
     * 当前页展示数量
     */
    @JsonProperty("page_size")
    @ApiModelProperty("当前页展示数量")
    private Integer pageSize = 10;
}
