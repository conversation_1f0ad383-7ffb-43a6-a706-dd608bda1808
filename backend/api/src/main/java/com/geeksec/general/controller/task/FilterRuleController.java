package com.geeksec.general.controller.task;

import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.FilterConfigInCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.condition.FilterRuleCondition;
import com.geeksec.analysis.entity.vo.FilterConfigVo;
import com.geeksec.analysis.entity.vo.FilterCsvVo;
import com.geeksec.analysis.entity.vo.FilterInfoVo;
import com.geeksec.analysis.entity.vo.FilterStateVo;
import com.geeksec.analysis.service.FilterRuleService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.FileUtil;
import com.geeksec.util.HttpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 过滤规则controller
 */
@RestController
@RequestMapping("/filter")
@Api
@Log4j2
public class FilterRuleController {

    @Value("${file.tmp-path}")
    public String filePath;

    @Value("${send-url.rules}")
    private String url;

    @Value("${send-url.base}")
    private String base;

    @Value("${file.template-path}")
    private String templatePath;

    @Autowired
    private FilterRuleService filterRuleService;


    @PutMapping("/state")
    @ApiOperation(value = "修改命中留存/丢弃")
    public ResultVo modifyFilterSate(@RequestBody FilterRuleCondition condition) {
        Integer state = condition.getState();
        if (condition.getTaskId() == null || state == null || state < 0 || state > 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        filterRuleService.modifyFilterSate(condition);
        Integer taskId = condition.getTaskId();
        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(endList));
    }

    @GetMapping("/state")
    @ApiOperation(value = "获取任务过滤的命中留存/丢弃状态")
    public ResultVo<FilterStateVo> getFilterStateByTaskId(@RequestParam Integer task_id) {
        if (task_id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        FilterStateVo filterStateByTaskId = filterRuleService.getFilterStateByTaskId(task_id);
        return ResultVo.success(filterStateByTaskId);
    }

    @PostMapping(value = "/config")
    @ApiOperation(value = "任务过滤规则列表")
    public ResultVo<PageResultVo<FilterConfigVo>> getFilterConfigList(@RequestBody FilterRuleCondition condition) {
        String orderField = condition.getOrderField();
        String sortOrder = condition.getSortOrder();
        if (condition.getTaskId() == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        if (StringUtils.isNotBlank(orderField)) {
            if (!("id".equals(orderField)
                    || "update_time".equals(orderField))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
            if (!("desc".equals(sortOrder) || "asc".equals(sortOrder))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }

        PageResultVo<FilterConfigVo> filterConfigList = filterRuleService.getFilterConfigList(condition);
        return ResultVo.success(filterConfigList);
    }

    @PostMapping(value = "/config/info")
    @ApiOperation(value = "任务过滤规则：创建")
    public ResultVo addConfig(@RequestBody FilterConfigInCondition condition) {
        Integer taskId = condition.getTaskId();
        Integer batchId = condition.getBatchId();
        // 首先校验其任务号和批次号
        if (!checkRuleParam(taskId,batchId)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        FilterInfoVo filterInfoVo = condition.getFilterInfo();
        if (filterInfoVo == null){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        filterRuleService.addConfig(condition);

        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(endList));
    }

    @PutMapping(value = "/config/info")
    @ApiOperation(value = "任务过滤规则：更新")
    public ResultVo updateConfig(@RequestBody FilterConfigInCondition condition) {
        Integer taskId = condition.getTaskId();
        Integer batchId = condition.getBatchId();
        // 首先校验其任务号和批次号
        if (!checkRuleParam(taskId,batchId)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        FilterInfoVo filterInfoVo = condition.getFilterInfo();
        if (filterInfoVo == null){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        ResultVo resultVo = filterRuleService.updateConfig(condition);
        if (resultVo.getErr() != 0) {
            return resultVo;
        }
        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(batchId);
        return sendPost(JSONObject.toJSONString(endList));
    }

    @DeleteMapping(value = "/config/info")
    @ApiOperation(value = "任务过滤规则：删除")
    public ResultVo deleteConfig(@RequestBody FilterDeleteCondition condition) {
        Integer taskId = condition.getTaskId();
        if (!checkRuleParam(condition.getTaskId(), condition.getBatchId())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        filterRuleService.deleteConfig(condition);
        List<Integer> endList = new ArrayList<>();
        endList.add(taskId);
        endList.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(endList));
    }

    @PostMapping(value = "/csvImport")
    public ResultVo<FilterCsvVo> csvImport(@RequestParam("file") MultipartFile file,
                                           @RequestParam("task_id") Integer task_id,
                                           @RequestParam("batch_id") Integer batch_id) {
        if (!checkRuleParam(task_id, batch_id)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String filePath = "";
        try {
            //写到临时地点
            ResultVo resultVo = FileUtil.fileUpload(this.filePath, file);
            if (resultVo.getErr() != 0) {
                return resultVo;
            }
            String newName = resultVo.getData().toString();
            filePath = this.filePath + newName;
            CsvReader csvReader = CsvUtil.getReader();
            //进行读取
            CsvData csvData = csvReader.read(new File(filePath), CharsetUtil.CHARSET_UTF_8);
            if (csvData.getRowCount() > 10001) {
                throw new GkException(GkErrorEnum.DOWNLOAD_FILE_COUNT_OVERATE);
            }
            //获取行数据
            List<CsvRow> rows = csvData.getRows();

            ResultVo<FilterCsvVo> result = filterRuleService.addConfigByCSV(task_id, rows);
            if (result.getErr() != 0) {
                return result;
            }
            FilterCsvVo data = result.getData();
            if (data.getSucNum() == 0) {
                //没有成功的数据
                return result;
            }
            List<Integer> endList = new ArrayList<>();
            endList.add(task_id);
            endList.add(batch_id);
            ResultVo syncVo = sendPost(JSONObject.toJSONString(endList));
            if (syncVo.getErr() != 0) {
                //同步成功
                data.setSyncMag("探针同步成功");
            } else {
                data.setSyncMag("探针同步失败");
            }
            return result;
            //根据临时点 生成File
        } catch (Exception e) {
            log.error("导入过滤规则异常e={}", e);
            throw new GkException(GkErrorEnum.FILTER_RULE_IMPORT_ERROR);
        } finally {
            //删除临时文件
            FileUtil.fileDelete(filePath);
        }
    }

    @PostMapping("/getCsv/{task_id}")
    public ResultVo getCsv(@PathVariable("task_id") Integer task_id, HttpServletResponse response, HttpServletRequest request) {
        List<List<String>> list = filterRuleService.getCsv(task_id);
        // 写数据
        CsvWriter writer = null;
        String fileName = new Date().getTime() + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(list);
            FileUtil.csvDownloadFile(response, csvFile);
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new GkException(GkErrorEnum.CSV_EXPORT_FAIL);
        } finally {
            FileUtil.fileDelete(fileName);
            if (writer != null)
                writer.close();
        }
        return ResultVo.success();
    }

    @GetMapping("/template")
    public ResultVo getTemplateCsv(HttpServletResponse response) {
        // 获取文件
        String path = templatePath + Constants.FILTER_RULE_FILE;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FILTER_RULE_TEMPLATE_DOWNLOAD_FAIL);
            }
        } else {
            return ResultVo.fail("请先初始化过滤规则模板文件");
        }
        return ResultVo.success();
    }

    /**
     * 同步探针，发起请求
     * @param json
     * @return
     */
    private ResultVo sendPost(String json) {
        if (StringUtils.isEmpty(url)) {
            throw new GkException(GkErrorEnum.RULE_SYNC_URL_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base + url, json);
        if (resp == null) {
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
        return ResultVo.successMsg("探针同步成功");
    }

    private Boolean checkRuleParam(Integer taskId, Integer batchId) {
        return (taskId != null && taskId >= 0 && taskId <= 1) && batchId != null;
    }
}
