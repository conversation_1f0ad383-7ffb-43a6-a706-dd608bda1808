package com.geeksec.general.controller.alarm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.geeksec.analysis.entity.condition.*;
import com.geeksec.analysis.entity.vo.AlarmTargetAggVo;
import com.geeksec.analysis.entity.vo.AlarmTypeAggVo;
import com.geeksec.analysis.entity.vo.KnowledgeAlarmVo;
import com.geeksec.analysis.service.AlarmJudgeService;
import com.geeksec.analysis.service.AlarmService;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.CheckParamUtil;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.FileUtil;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/alarm")
@Log4j2
public class AlarmController {

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private AlarmJudgeService alarmJudgeService;


    @PostMapping("/target/agg")
    public ResultVo<AlarmTargetAggVo> getAlarmTargetAgg(@RequestBody AlarmCommonCondition condition) {
        try {
            return alarmService.getAlarmTargetAgg(condition);
        } catch (Exception e) {
            log.error("告警：指标信息异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_TARGET_AGGR_ERROR);
        }
    }

    /**
     * 告警页面- 告警攻击链路展示
     *
     * @param condition
     * @return
     */
    @PostMapping("/type/agg")
    public ResultVo<List<AlarmTypeAggVo>> getAlarmTypeAgg(@RequestBody AlarmCommonCondition condition) {
        try {
            return alarmService.getModelAlarmAttackChainAggr(condition);
        } catch (Exception e) {
            log.error("告警：类型聚合异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_ATTACK_CHAIN_QUERY_ERROR);
        }
    }

    /**
     * @return 告警知识库全量查询
     */
    @GetMapping("/knowledge")
    public ResultVo<List<KnowledgeAlarmVo>> getKnowledgeAlarmList() {
        return ResultVo.success(alarmService.getKnowledgeAlarmList());
    }

    /**
     * 获取告警列表（分类型）
     *
     * @param condition
     * @return
     */
    @PostMapping("/list")
    public ResultVo<PageResultVo<Map<String, Object>>> getAlarmList(@RequestBody AlarmListCondition condition) {
        String orderField = condition.getOrderField();
        if (!CheckParamUtil.checkPage(condition.getCurrentPage(), condition.getPageSize())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        if (StringUtil.isEmpty(orderField)) {
            condition.setOrderField("time");
        } else {
            if (!("time".equals(orderField) || "alarm_status".equals(orderField)
                    || "attack_chain_name".equals(orderField) || "attack_level".equals(orderField)
                    || "task_id".equals(orderField)
                    || "alarm_knowledge_id".equals(orderField)
                    || "alarm_type".equals(orderField))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }
        try {
            return alarmService.getAlarmList(condition);
        } catch (Exception e) {
            log.error("告警：分页列表异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_LIST_QUERY_ERROR);
        }
    }

    /**
     * 告警列表-告警详情
     *
     * @param params
     * @return
     */
    @PostMapping("/detail")
    public ResultVo<Map<String, Object>> getAlarmDetail(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            log.error("告警详情查询参数有误！！");
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try {
            String alarmId = (String) params.get("alarm_id");
            String esIndex = (String) params.get("alarm_index");
            if (StringUtils.isEmpty(alarmId) || StringUtils.isEmpty(esIndex)) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
            return alarmService.getAlarmDetail2(esIndex, alarmId);
        } catch (Exception e) {
            log.error("告警：告警详情查询失败！", e);
            throw new GkException(GkErrorEnum.ALARM_DETAIL_QUERY_ERROR);
        }
    }

    /**
     * 告警研判-绘图
     *
     * @param params
     * @return
     */
    @PostMapping("/judge")
    public ResultVo<Map<String, Object>> getAlarmJudgeGraph(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            log.error("告警研判绘图参数为空！！");
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String alarmId = (String) params.get("alarm_id");
        String esIndex = (String) params.get("alarm_index");
        if (StringUtils.isEmpty(alarmId) || StringUtils.isEmpty(esIndex)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        try {
            ResultVo detailVo = alarmService.getAlarmDetail2(esIndex, alarmId);
            Map<String, Object> alarmMap = (Map<String, Object>) detailVo.getData();

            return alarmJudgeService.createAlarmJudgeGraph(alarmMap);
        } catch (Exception e) {
            log.error("告警：告警研判绘图失败！", e);
            throw new GkException(GkErrorEnum.ALARM_JUDGE_GRAPH_ERROR);
        }
    }

    /**
     * 通过IP和对应角色进行告警研判扩展
     *
     * @return
     */
    @PostMapping("/judge/role")
    public ResultVo getAlarmJudgeGraphByRole(@RequestBody AlarmRoleJudgeCondition condition) {

        if (!CommonUtil.isAllFieldsNotNullAndNotEmpty(condition)) {
            log.error("告警研判绘图参数为空！！");
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        try {
            return alarmJudgeService.createAlarmJudgeGraphByRole(condition);
        } catch (Exception e) {
            log.error("告警：告警研判绘图失败！", e);
            throw new GkException(GkErrorEnum.ALARM_JUDGE_GRAPH_ERROR);
        }
    }

    /**
     * 更新告警文档状态
     *
     * @param condition
     * @return
     */
    @PutMapping("/info")
    public ResultVo updateDoc(@RequestBody AlarmStatusUpCondition condition) {
        try {
            return alarmService.updateDoc(condition);
        } catch (Exception e) {
            log.error("告警：更新文档异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_STATUS_UPDATE_ERROR);
        }
    }

    /**
     * 删除告警文档
     *
     * @param condition
     * @return
     */
    @DeleteMapping("/list")
    public ResultVo deleteDoc(@RequestBody Map<Integer, List<String>> condition) {
        try {
            return alarmService.deleteDoc(condition);
        } catch (Exception e) {
            log.error("告警：删除文档异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_DELETE_ERROR);
        }
    }

    @PostMapping("/deleteAll")
    public ResultVo deleteAllAlarm() {
        try {
            return alarmService.deleteAllAlarm();
        } catch (Exception e) {
            log.error("告警：删除所有告警信息", e);
            throw new GkException(GkErrorEnum.ALARM_DELETE_ERROR);
        }

    }

    /**
     * 获取告警导出列表
     *
     * @param condition
     * @param response
     * @return
     */
    @PostMapping("/getCsv")
    public ResultVo getCsv(@RequestBody AlarmListCondition condition, HttpServletResponse response) {
        ResultVo<PageResultVo<Map<String, Object>>> resultVo = alarmService.getAlarmList(condition);

        List<Map<String, Object>> records = resultVo.getData().getRecords();
        if (CollUtil.isEmpty(records)) {
            return ResultVo.success("无告警数据可进行导出");
        }

        List<List<String>> rowList = Lists.newArrayListWithCapacity(records.size());
        for (Map<String, Object> record : records) {
            List<String> row = new ArrayList<>();
            row.add(JSON.toJSONString(record));
            rowList.add(row);
        }
        // 写数据
        CsvWriter writer = null;
        String fileName = "Alarm_" + (new Date().getTime()) + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(rowList);
            FileUtil.csvDownloadFile(response, csvFile);
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new GkException(GkErrorEnum.ALARM_DATA_CSV_EXPORT_FAIL);
        } finally {
            FileUtil.fileDelete(fileName);
            if (writer != null) {
                writer.close();
            }
        }
        return ResultVo.success();
    }

    /**
     * 导出告警报告PDF
     *
     * @param condition
     * @param response
     * @param request
     * @return
     */
    @PostMapping("/export/pdf")
    public ResultVo exportAlarmReport(@RequestBody AlarmListCondition condition, HttpServletResponse response, HttpServletRequest request) {
        String orderField = condition.getOrderField();
        if (!CheckParamUtil.checkPage(condition.getCurrentPage(), condition.getPageSize())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        if (StringUtil.isEmpty(orderField)) {
            condition.setOrderField("time");
        } else {
            if (!("time".equals(orderField) || "alarm_status".equals(orderField)
                    || "attack_chain_name".equals(orderField) || "attack_level".equals(orderField)
                    || "task_id".equals(orderField)
                    || "alarm_knowledge_id".equals(orderField)
                    || "alarm_type".equals(orderField))) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }
        try {
            ResultVo resultVo = alarmService.exportAlarmReport(condition);
            String filePath = (String) resultVo.getData();
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                // 开始下载
                try {
                    FileUtil.csvDownloadFile(response, file);
                    file.delete();
                    return ResultVo.success("导出告警报告成功");
                } catch (Exception e) {
                    response.setContentType("application/json;charset-uft-8");
                    throw new GkException(GkErrorEnum.ALARM_DATA_PDF_EXPORT_FAIL);
                }
            } else {
                throw new GkException(GkErrorEnum.FILE_DOWNLOAD_PATH_EMPTY);
            }
        } catch (Exception e) {
            log.error("告警：导出告警报告失败！", e);
            throw new GkException(GkErrorEnum.ALARM_DATA_PDF_EXPORT_FAIL);
        }
    }

    /**
     * 通过告警关联会话ID检索对应会话信息，生成到告警相关PCAP下载列表
     *
     * @return
     */
    @PostMapping("/download/prepare/pcap")
    public ResultVo pcapPreparByAlarmSession(@RequestBody AlarmPcapDownloadCondition condition) {

        List<String> alarmSessionList = condition.getAlarmSessionList();
        String alarmType = condition.getAlarType();
        Long alarmTime = condition.getAlarmTime();
        Integer userId = condition.getUserId();
        if (CollectionUtil.isEmpty(alarmSessionList) || StringUtil.isEmpty(alarmType) || alarmTime == 0L || ObjectUtils.isEmpty(userId)){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        try {
            return alarmService.prepareAlarmSessionPcap(userId, alarmSessionList, alarmType, alarmTime);
        } catch (Exception e) {
            log.error("告警：下载pcap异常,e={}", e);
            throw new GkException(GkErrorEnum.ALARM_PCAP_DOWNLOAD_TASK_FAIL);
        }
    }

    private List<List<String>> createAlarmCsv(List<Map<String, Object>> records) {
        List<List<String>> rows = new ArrayList<>();
        // 表头创建
        List<String> title = new ArrayList<>();
        title.add("告警名称");
        title.add("告警对象");
        title.add("受害方");
        title.add("攻击方");
        title.add("处理状态");
        title.add("攻击类型");
        title.add("威胁级别");
        title.add("任务名称");
        title.add("创建时间");
        rows.add(title);

        for (Map<String, Object> record : records) {
            List<String> rowData = new ArrayList<>();
            // 告警名称
            rowData.add((String) record.get("target_name"));
        }


        return null;
    }


}
