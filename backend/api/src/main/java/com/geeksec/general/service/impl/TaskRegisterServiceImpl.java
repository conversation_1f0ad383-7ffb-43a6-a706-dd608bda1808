package com.geeksec.general.service.impl;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.condition.TaskRegisterCondition;
import com.geeksec.analysis.dao.DownloadTaskDao;
import com.geeksec.analysis.dao.DownloadTaskRegisterDao;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.entity.DownloadTask;
import com.geeksec.analysis.entity.DownloadTaskRegister;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.MetadataAggInfoCondition;
import com.geeksec.analysis.entity.condition.MetadataCondition;
import com.geeksec.analysis.entity.condition.SessionAggInfoCondition;
import com.geeksec.analysis.entity.vo.DownloadTaskRegisterVo;
import com.geeksec.analysis.entity.vo.SessionAggInfoVo;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.Metadata2Service;
import com.geeksec.analysis.service.MetadataService;
import com.geeksec.analysis.service.SessionListService;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.TaskRegisterService;
import com.geeksec.util.CommonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: GuanHao
 * @Date: 2022/5/19 11:26
 * @Description： <Functions List>
 */
@Service
public class TaskRegisterServiceImpl implements TaskRegisterService {
    private static final Logger LOG = LoggerFactory.getLogger(TaskRegisterServiceImpl.class);

    @Autowired(required = false)
    DownloadTaskRegisterDao taskRegisterDao;

    @Autowired
    DownloadTaskDao downloadTaskDao;

    @Autowired
    UserDao userDao;

    @Autowired
    TagInfoDao tagInfoDao;

    @Autowired
    private SessionListService sessionListService;

    @Value("${file.session-path}")
    private String sessionPath;
    @Value("${file.pcap-download-path}")
    private String pcapDownloadPath;
    @Autowired
    private MetadataService metadataService;

    @Autowired
    private AggrTargetService aggrTargetService;

    @Autowired
    Metadata2Service metadata2Service;

    // 下一条预执行的任务ID
    private Integer nextId;

    @Value("${enabled.task_scheduled}")
    private Boolean enabledTaskScheduled;

    private static final List<String> PKT_FIELD_LIST = Arrays.asList("sPayloadBytes", "sPayloadNum", "dPayloadBytes", "dPayloadNum");

    @Autowired
    private TokenService tokenService;

    /**
     * 数据准备任务
     */
    @Scheduled(fixedDelay = 5000)
    @Autowired
    public void dataPrepareJob() {
        if (enabledTaskScheduled) {
            ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
                    20,
                    50,
                    60,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>()
            );
            threadPoolExecutor.execute(() -> {
                dataPrepare();
            });
            threadPoolExecutor.shutdown();
        }
    }

    public void dataPrepare() {
        if (StringUtils.isEmpty(sessionPath)) {
            LOG.error(GkErrorEnum.TEMP_FOLDER_LOCATION_NOT_CONFIG.getMsg());
            return;
        }
        if (nextId != null) {
            // 不存在待下载任务
            return;
        }
        // 查询早创建的，任务类型为待执行的记录
        DownloadTaskRegister register = taskRegisterDao.getNextTask();
        if (register == null) {
            return;
        }
        nextId = register.getId();
        LOG.info("会话分析任务扫描，本次执行任务id={},任务type={}", register.getId(), register.getType());
        try {
            // 更新状态为处理中
            DownloadTaskRegister registerUpdate = new DownloadTaskRegister(register.getId(), 2);
            taskRegisterDao.updateById(registerUpdate);
            // 任务类型，1走会话列表下载，2走聚合下载
            Integer taskType = register.getTaskType();
            String path;
            ResultVo vo = null;
            switch (taskType) {
                case 1:
                    // 会话列表
                    vo = sessionListPreparation(register);
                    break;
                case 2:
                    // 会话聚合列表
                    vo = aggregationListPreparation(register);
                    break;
                case 3:
                    // 元数据列表导出 三种类型调用同一个方法
                    vo = metadataListPreparation(register);
                    break;
                case 4:
                    // ssl_agg;
                case 5:
                    // 元数据聚合导出 调用同一个方法
                    vo = metadataAggListPreparation(register);
                    break;
                case 6:
                    // IP列表导出
                    vo = targetAggrPreparation(register, "IP");
                    break;
                case 7:
                    // 域名列表导出
                    vo = targetAggrPreparation(register, "DOMAIN");
                    break;
                case 8:
                    // 证书列表导出
                    vo = targetAggrPreparation(register, "CERT");
                    break;
                case 9:
                    // 指纹列表查询
                    vo = targetAggrPreparation(register, "FINGERPRINT");
                    break;
                default:
                    vo = ResultVo.fail("任务类型异常");
            }

            if (vo.getErr() != 0) {
                DownloadTaskRegister downloadTaskRegister = new DownloadTaskRegister();
                downloadTaskRegister.setId(register.getId());
                downloadTaskRegister.setType(-1);
                downloadTaskRegister.setErrorMsg(vo.getMsg());
                taskRegisterDao.updateById(downloadTaskRegister);
                return;
            }
            path = vo.getData().toString();
            // 向数据库中写入地址、存储时间,并更新状态为可下载
            DownloadTaskRegister pathRegister = new DownloadTaskRegister(register.getId(), 3);
            pathRegister.setPath(path);
            // 往后推24小时得出删除时间
            Calendar calendar = Calendar.getInstance();
            Date date = new Date();
            calendar.setTime(date);
            calendar.add(Calendar.HOUR, 24);
            pathRegister.setDeleteTime(calendar.getTimeInMillis() / 1000);
            pathRegister.setUpdateTime(date.getTime() / 1000);
            taskRegisterDao.updateById(pathRegister);
        } catch (Exception e) {
            LOG.error("数据准备过程中出现异常: ", e);
            // 状态修改为错误
            try {
                DownloadTaskRegister downloadTaskRegister = new DownloadTaskRegister(register.getId(), -1);
                downloadTaskRegister.setErrorMsg("查询异常");
                taskRegisterDao.updateById(downloadTaskRegister);
            } catch (Exception e1) {
                LOG.error("数据准备出错，并且更新数据库状态为：[ -1 ]，也失败：{}", register);
            }
        } finally {
            nextId = null;
        }
    }

    @Override
    public JSONObject taskRegister(TaskRegisterCondition condition) {
        LOG.info("创建下载任务：{}", condition);
        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        long startTime = System.currentTimeMillis();
        DownloadTaskRegister taskRegister = new DownloadTaskRegister();
        // 设置user id
        taskRegister.setUserId(condition.getUserId());
        // 设置query
        JSONObject conditionQuery = condition.getCondition();

        taskRegister.setQuery(JSON.toJSONString(conditionQuery));
        // 记录生成时间
        taskRegister.setCreateTime(System.currentTimeMillis() / 1000);
        // task_type
        taskRegister.setTaskType(condition.getTaskType());
        // type ==1 表示待执行
        taskRegister.setType(1);
        try {
            taskRegisterDao.insert(taskRegister);
        } catch (Exception e) {
            LOG.error("任务创建失败: ", e);
            throw new GkException(GkErrorEnum.FILE_DOWNLOAD_FAIL);
        }

        LOG.info("下载任务创建成功，用时：{} 秒。", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson();
    }

    @Override
    public JSONObject searchList(TaskRegisterCondition condition) {
        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        LOG.info("开始查询下载日志:{}", condition);
        List<DownloadTaskRegisterVo> resultList = new ArrayList<DownloadTaskRegisterVo>();
        long startTime = System.currentTimeMillis();

        // 查询
        List<DownloadTaskRegister> pageList = new ArrayList<>();
        PageHelper.startPage(condition.getPage(), condition.getLimit());
        try {
            pageList = taskRegisterDao.listTaskRegister(condition);
        } catch (Exception e) {
            LOG.error("查询失败: ", e);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);
        }

        // 若列表为null则直接返回
        if (ObjectUtils.isEmpty(pageList) || pageList.size() == 0) {
            return CommonUtil.successJson();
        }
        // 获取用户列表
        HashMap<Long, UserInfoVo> userMap = new HashMap<>();
        List<UserInfoVo> userList = userDao.listAll();
        for (UserInfoVo userInfoVo : userList) {
            userMap.put(userInfoVo.getId(), userInfoVo);
        }

        PageInfo<DownloadTaskRegister> info = new PageInfo<>(pageList);
        // 遍历结果
        int front = 0;
        for (DownloadTaskRegister record : pageList) {
            DownloadTaskRegisterVo taskRegisterVo = parseTaskRegisterVo(record);
            // 排名计数
            if (taskRegisterVo.getType() == 1) {
                taskRegisterVo.setFrontNum(front);
                front++;
            }

            // 获取用户
            UserInfoVo user = userMap.getOrDefault(Long.valueOf(record.getUserId().toString()), null);
            taskRegisterVo.setUserName(user.getUsername());

            // 计算资源删除时间
            Long deleteData = record.getDeleteTime();
            if (ObjectUtils.isNotEmpty(deleteData)) {
                long thisTime = System.currentTimeMillis() / 1000;
                Long deleteTime = (deleteData - thisTime) / 3600;
                if (deleteTime > 0) {
                    taskRegisterVo.setDeleteTime(deleteTime);
                } else {
                    taskRegisterVo.setDeleteTime(0L);
                }
            }

            // 判断当前下载任务是否为元数据导出
            if (record.getTaskType() == 3) {
                // 从查询条件里面拿
                JSONObject query = JSON.parseObject(record.getQuery());
                // 将JSONObject 转为 MetadataCondition
                MetadataCondition metadataCondition = JSON.toJavaObject(query, MetadataCondition.class);
                String dataKey = metadataCondition.getDataKey();
                taskRegisterVo.setMetadataType(dataKey);
            }

            resultList.add(taskRegisterVo);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("limit", condition.getLimit());
        result.put("page", condition.getPage());
        result.put("record", resultList);
        result.put("total", info.getTotal());
        LOG.info("查询结束，用时：{}", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson(result);
    }

    @Override
    public JSONObject deleteTask(Integer id) {
        LOG.info("删除下载任务,id 为：[ {} ].", id);
        long startTime = System.currentTimeMillis();

        DownloadTaskRegister update = new DownloadTaskRegister();
        update.setId(id);
        update.setStatus(0);

        // 获取详情
        DownloadTaskRegister register = null;
        try {
            register = taskRegisterDao.selectById(id);
        } catch (Exception e) {
            LOG.error("Id 为[ {} ]的任务详情查询失败:", id, e);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);
        }

        Integer type = register.getType();
        // type = 3表示数据准备完成
        if (type == 3) {
            LOG.info("待删除任务的状态为：[ 待下载 ]，尝试删除对应的保存文件。");

            if (!deleteDownloadTaskRegisterFile(register)) {
                LOG.error("文件删除失败，任务Id为:[ {} ].", id);
                throw new GkException(GkErrorEnum.DELETE_DOWNLOAD_TASK_FAILED);
            }

            LOG.info("文件删除成功。");
            update.setType(4);
        } else if (type == 2) {
            LOG.warn("尝试删除任务id为:[ {} ]的任务，检测到该任务处于[ 准备中 ]的状态，删除失败。", id);
            throw new GkException(GkErrorEnum.DELETE_DOWNLOAD_TASK_FAILED);
        }

        try {
            taskRegisterDao.updateById(update);
        } catch (Exception e) {
            LOG.error("Id为:[ {} ]的记录删除状态更新失败:{}", id, e);
            throw new GkException(GkErrorEnum.DELETE_DOWNLOAD_TASK_FAILED);
        }

        LOG.info("删除结束，用时：{}秒", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson();
    }

    @Override
    public DownloadTaskRegister downloadData(Integer id) {
        // 获取地址
        DownloadTaskRegister register = null;
        try {
            register = taskRegisterDao.selectById(id);

            // 计算下载次数 并写入数据库
            int downloadCount = register.getDownloadCount() + 1;
            register.setDownloadCount(downloadCount);
            taskRegisterDao.updateById(register);
        } catch (Exception e) {
            LOG.error("数据库查询失败:", e);
            // 读取失败或更改下次次数失败，钧不下载文件
            return new DownloadTaskRegister();
        }

        return register;
    }

    private static DownloadTaskRegisterVo parseTaskRegisterVo(DownloadTaskRegister taskRegister) {
        DownloadTaskRegisterVo registerVo = new DownloadTaskRegisterVo();
        registerVo.setCreateTime(taskRegister.getCreateTime());
        registerVo.setDeleteTime(taskRegister.getDeleteTime());
        registerVo.setDownloadCount(taskRegister.getDownloadCount());
        registerVo.setId(taskRegister.getId());
        registerVo.setPath(taskRegister.getPath());
        registerVo.setQuery(taskRegister.getQuery());
        registerVo.setTaskType(taskRegister.getTaskType());
        registerVo.setType(taskRegister.getType());
        registerVo.setUpdateTime(taskRegister.getUpdateTime());
        registerVo.setUserId(taskRegister.getUserId());

        JSONObject jsonObject = JSON.parseObject(taskRegister.getQuery());
        Map<String, Map<String, Object>> queryMap = new HashMap<>();
        queryMap.put("and", new HashMap<>());
        queryMap.put("not", new HashMap<>());
        JSONArray queryArray = jsonObject.getJSONArray("query");
        List<AnalysisBaseCondition.QueryOb> queryObList = JSONArray.parseArray(queryArray.toJSONString(), AnalysisBaseCondition.QueryOb.class);
        for (AnalysisBaseCondition.QueryOb queryOb : queryObList) {
            if (queryOb.getBoolSearch().equals("and")) {
                // 正选条件项
                List<AnalysisBaseCondition.SearchInfo> searchInfoList = queryOb.getSearch();
                for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                    String queryField = searchInfo.getTarget();
                    List<Object> vals = new ArrayList<>();
                    vals.addAll(searchInfo.getVal());
                    Map<String, List<Object>> singleQuery = new HashMap<>();
                    singleQuery.put(queryField, vals);
                    Map<String, Object> includeMap = queryMap.get("and");
                    if (includeMap.containsKey(queryMap)) {
                        // 当前字段已经有查询条件，添加
                        List<Object> existTargets = (List<Object>) includeMap.get(queryField);
                        existTargets.addAll(vals);
                        includeMap.put(queryField, existTargets);
                    } else {
                        // 无当前字段，创建map
                        includeMap.put(queryField, vals);
                    }
                    queryMap.put("and", includeMap);
                }
            } else if (queryOb.getBoolSearch().equals("not")) {
                // 反选条件项
                // 正选条件项
                List<AnalysisBaseCondition.SearchInfo> searchInfoList = queryOb.getSearch();
                for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                    String queryField = searchInfo.getTarget();
                    List<Object> vals = new ArrayList<>();
                    vals.addAll(searchInfo.getVal());
                    Map<String, List<Object>> singleQuery = new HashMap<>();
                    singleQuery.put(queryField, vals);
                    Map<String, Object> excludeMap = queryMap.get("not");
                    if (excludeMap.containsKey(queryMap)) {
                        // 当前字段已经有查询条件，添加
                        List<Object> existTargets = (List<Object>) excludeMap.get(queryField);
                        existTargets.addAll(vals);
                        excludeMap.put(queryField, existTargets);
                    } else {
                        // 无当前字段，创建map
                        excludeMap.put(queryField, vals);
                    }
                    queryMap.put("not", excludeMap);
                }
            }
        }
        if (MapUtils.isEmpty(queryMap)) {
            registerVo.setShowQuery(org.apache.commons.lang3.StringUtils.EMPTY);
        } else {
            String jsonStr = JSONObject.toJSONString(queryMap);
            registerVo.setShowQuery(jsonStr);
        }
        return registerVo;
    }

    /**
     * 会话列表导出数据准备
     *
     * @param register 对象
     * @return 地址
     */
    private ResultVo sessionListPreparation(DownloadTaskRegister register) throws JsonProcessingException {
        String query = register.getQuery();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        AnalysisBaseCondition analysisBaseCondition = objectMapper.readValue(query, AnalysisBaseCondition.class);
        analysisBaseCondition.setIsTask(true);
        JSONObject jsonObject = sessionListService.sessionList(analysisBaseCondition);
        int err = jsonObject.getIntValue("err");
        if (err != 0) {
            throw new GkException(GkErrorEnum.SESSION_LOG_EXPORT_QUERY_ERROR);
        }
        JSONObject data = jsonObject.getJSONObject("data");
        // 获取到data里的records也就是会话记录
        JSONArray records = data.getJSONArray("records");
        if (records.isEmpty()) {
            return ResultVo.fail("查询数据为空");
        }
        HashMap<String, Object> maxSizeJson = new HashMap<>();
        int maxSize = 0;
        //找出字段最多的es对象
        for (Object record : records) {
            HashMap<String, Object> map = (HashMap<String, Object>) record;
            if (map.size() > maxSize) {
                maxSizeJson = map;
                maxSize = map.size();
            }
        }
        List<List<String>> rows = assembleRows(records);
        //写数据
        ResultVo vo = delFile(register.getId(), rows, "session_");
        return vo;
    }

    private List<List<String>> assembleRows(JSONArray records) {
        List<List<String>> rows = new ArrayList<>(records.size());
        // 获取预先定义的日志导出中英文对照
        Map<String, String> expTitlesMap = getSessionExpTitle();
        // 第一行的title应该是Map里的每一个value组成的中文
        List<String> titles = new ArrayList<>(expTitlesMap.values());
        rows.add(titles);
        // 数据值字典从map的key中获取
        List<String> keys = new ArrayList<>(expTitlesMap.keySet());

        //循环并得到key列表,对照List<String>为Map的key
        for (Object record : records) {
            List<String> values = new ArrayList<>();
            HashMap<String, Object> esValue = (HashMap<String, Object>) record;
            HashMap<String, Object> pktMap = (HashMap<String, Object>) esValue.get("pkt");
            for (String key : keys) {
                Object object;
                // 如果是pkt字段，直接取pktMap里的值
                if (PKT_FIELD_LIST.contains(key)) {
                    object = pktMap.get(key);
                } else if ("Labels".equals(key)) {
                    // 将List<String>转为List<Integer>
                    // 最后要放入列中的标签名称字符串
                    List<String> labelList = (List<String>) esValue.get(key);
                    if (CollectionUtils.isEmpty(labelList)){
                        object = "[]";
                    }else{
                        object = tagInfoDao.getTagTextByTagList(labelList);
                    }
                }else {
                    object = Optional.ofNullable(esValue.get(key))
                            .orElse("");
                }
                values.add(object.toString());
            }
            rows.add(values);
        }
        return rows;
    }

    private ResultVo delFile(Integer id, List<List<String>> rows, String suffixName) {
        // 写数据
        CsvWriter writer = null;
        String fileName = suffixName + id + ".csv";
        String path = sessionPath + fileName;
        try {
            // 写入CSV文件时编码为中文
            writer = CsvUtil.getWriter(path, CharsetUtil.CHARSET_GBK);
            //创建csv
            writer.write(rows);
            return ResultVo.success(path);
        } catch (Exception e) {
            LOG.error("导出会话列表数据失败,写入CSV文件失败", e);
            throw new GkException(GkErrorEnum.CSV_EXPORT_FAIL);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 聚合导出数据准备
     *
     * @param register 对象
     * @return 地址
     */
    private ResultVo aggregationListPreparation(DownloadTaskRegister register) throws JsonProcessingException {
        String query = register.getQuery();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SessionAggInfoCondition condition = objectMapper.readValue(query, SessionAggInfoCondition.class);
        ResultVo<PageResultVo<SessionAggInfoVo>> resultVo = metadataService.getSessionAggList(condition);
        if (resultVo.getErr() != 0) {
            return ResultVo.fail("查询数据异常");
        }
        PageResultVo<SessionAggInfoVo> pageVo = resultVo.getData();
        long total = pageVo.getTotal();
        if (total < 1) {
            return ResultVo.fail("查询数据为空");
        }

        List<SessionAggInfoVo> records = pageVo.getRecords();
        List<List<String>> rows = Lists.newArrayListWithCapacity(records.size());

        // 日志聚合产出文件字段按照条件进行封装
        List<String> fieldNames = condition.getFieldNames();

        rows.add(fieldNames);
        for (SessionAggInfoVo vo : records) {
            List<String> values = new ArrayList<>();
            String s = JSON.toJSONString(vo);
            JSONObject esValue = JSONObject.parseObject(s);
            for (String key : fieldNames) {
                Object o = Optional.ofNullable(esValue.get(key))
                        .orElse("");
                values.add(o.toString());
            }
            rows.add(values);
        }
        //写数据
        ResultVo vo = delFile(register.getId(), rows, "session_agg_");
        return vo;
    }

    /**
     * 元数据聚合列表导出
     *
     * @param register 对象
     * @return 地址
     */
    private ResultVo metadataAggListPreparation(DownloadTaskRegister register) throws JsonProcessingException {
        String query = register.getQuery();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MetadataAggInfoCondition condition = objectMapper.readValue(query, MetadataAggInfoCondition.class);
        ResultVo<PageResultVo<JSONObject>> resultVo = metadataService.getMetadataAggList(condition);
        if (resultVo.getErr() != 0) {
            return ResultVo.fail("查询数据异常");
        }
        PageResultVo<JSONObject> pageResultVo = resultVo.getData();
        Long total = pageResultVo.getTotal();
        if (total < 1) {
            return ResultVo.fail("查询数据为空");
        }
        List<JSONObject> records = pageResultVo.getRecords();

        String dataKey = condition.getDataKey();
        List<String> fieldNames = condition.getFieldNames();
        fieldNames.add("cnt");
        List<List<String>> rows = Lists.newArrayListWithCapacity(records.size());
        rows.add(fieldNames);
        for (JSONObject record : records) {
            List<String> values = new ArrayList<>();
            for (String key : fieldNames) {
                values.add(record.getOrDefault(key, "").toString());
            }
            rows.add(values);
        }
        String pathName;
        switch (dataKey) {
            case "HTTP":
                pathName = "http_agg_";
                break;
            case "SSL":
                pathName = "ssl_agg_";
                break;
            case "DNS":
                pathName = "dns_agg_";
                break;
            default:
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        //写数据
        ResultVo vo = delFile(register.getId(), rows, pathName);
        return vo;
    }

    private ResultVo metadataListPreparation(DownloadTaskRegister register) throws JsonProcessingException {
        String query = register.getQuery();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MetadataCondition condition = objectMapper.readValue(query, MetadataCondition.class);
        ResultVo<PageResultVo<JSONObject>> resultVo = metadataService.getMetadataList(condition);

        if (resultVo.getErr() != 0) {
            throw new GkException(GkErrorEnum.METADATA_LIST_QUERY_ERROR);
        }

        PageResultVo<JSONObject> pageResultVo = resultVo.getData();
        Long total = pageResultVo.getTotal();

        if (total == 0) {
            return ResultVo.successMsg("当前条件下查询元数据列表为空");
        }

        List<JSONObject> records = pageResultVo.getRecords();
        String dataKey = condition.getDataKey();
        List<String> titles = getMetadataExpTitle(dataKey);

        List<List<String>> rows = new ArrayList<>(records.size());
        rows.add(titles);

        for (JSONObject jsonObject : records) {
            List<String> values = new ArrayList<>();
            HashMap<String, Object> clientMap = new HashMap<>();
            HashMap<String, Object> serverMap = new HashMap<>();
            if (dataKey.equals("HTTP")) {
                clientMap = (HashMap<String, Object>) jsonObject.get("Client");
                serverMap = (HashMap<String, Object>) jsonObject.get("Server");
            }

            for (String key : titles) {
                Object object;
                if ("Client.Content-Type".equals(key) || "Client.Accept".equals(key) || "Clilent.Host".equals(key) || "Client.User-Agent".equals(key) || "Client.Title".equals(key)) {
                    String title = key.split("\\.")[1];
                    object = clientMap.get(title) == null ? "" : clientMap.get(title);
                } else if ("Server.Content-Type".equals(key) || "Server.Accept-Ranges".equals(key) || "Server.Content-Encoding".equals(key) || "Server.Content-Length".equals(key) || "Server.Title".equals(key)) {
                    String title = key.split("\\.")[1];
                    object = serverMap.get(title) == null ? "" : serverMap.get(title);
                } else {
                    object = Optional.ofNullable(jsonObject.get(key))
                            .orElse("");
                }
                values.add(object.toString());
            }
            rows.add(values);
        }

        ResultVo vo = delFile(register.getId(), rows, dataKey + "_METADATA_");

        return vo;
    }

    private ResultVo targetAggrPreparation(DownloadTaskRegister register, String targetType) throws JsonProcessingException {
        try {
            LOG.info("开始进行{}聚合列表数据准备,register-->{}", targetType, register);
            String query = register.getQuery();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            AnalysisBaseCondition condition = objectMapper.readValue(query, AnalysisBaseCondition.class);
            // 全量 10000条导出
            condition.setCurrentPage(1);
            condition.setPageSize(10000);
            ResultVo resultVo = null;

            switch (targetType) {
                case "IP":
                    resultVo = aggrTargetService.getAggrIpList(condition);
                    break;
                case "DOMAIN":
                    resultVo = aggrTargetService.getAggrDomainList(condition);
                    break;
                case "CERT":
                    resultVo = aggrTargetService.getAggrCertList(condition);
                    break;
                case "FINGERPRINT":
                    resultVo = aggrTargetService.getAggrFingerList(condition);
                    break;
                default:
                    return ResultVo.success();
            }
            Integer err = resultVo.getErr();
            if (ObjectUtil.isEmpty(resultVo.getData())) {
                //数据为空
                return ResultVo.success("导出数据为空");
            }
            if (err != 0) {
                throw new GkException(GkErrorEnum.FILE_DOWNLOAD_FAIL);
            }
            PageResultVo pageResultVo = (PageResultVo) resultVo.getData();
            List<Object> voList = pageResultVo.getRecords();

            if (CollectionUtils.isEmpty(voList)) {
                return ResultVo.fail("查询数据为空");
            }

            // 开始生成CSV表头
            HashMap<String, Object> maxSizeJson = new HashMap<>();
            JSONArray records = JSONArray.parseArray(JSON.toJSONString(voList));
            int maxSize = 0;
            for (int i = 0; i < records.size(); i++) {
                JSONObject jsonObject = records.getJSONObject(i);
                HashMap<String, Object> map = new HashMap<>();
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    map.put(entry.getKey(), entry.getValue());
                }

                if (map.size() > maxSize) {
                    maxSizeJson = map;
                    maxSize = map.size();
                }
            }

            List<List<String>> rows = new ArrayList<>(records.size());
            Set<String> fields = maxSizeJson.keySet();
            // 去掉不展示的字段
            if (targetType.equals("IP")) {
                fields.remove("sPayloadBytes");
                fields.remove("dPayloadBytes");
            }
            ArrayList<String> titleRow = new ArrayList<>(fields);
            rows.add(titleRow);

            // 填装数据
            for (Object record : records) {
                ArrayList<String> values = new ArrayList<>();

                JSONObject jsonObject = (JSONObject) record;
                HashMap<String, Object> rowValues = new HashMap<>();
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    rowValues.put(entry.getKey(), entry.getValue());
                }

                for (String field : titleRow) {
                    Object v = Optional.ofNullable(rowValues.get(field)).orElse("");
                    values.add(v.toString());
                }
                rows.add(values);
            }

            return delFile(register.getId(), rows, targetType.toLowerCase() + "_list_");
        } catch (Exception e) {
            LOG.error("生成{}列表聚合数据失败!error->{}", targetType, e);
            throw new GkException(GkErrorEnum.FILE_DOWNLOAD_FAIL);
        }

    }

    /**
     * 定时清理超时数据,5分钟执行一次。
     */
    @Scheduled(fixedDelay = 300000)
    private void cleanTimeoutData() {
        //删除日志导出数据
        this.deleteDownloadTaskRegister();
        //删除pcap下载数据
        this.deleteDownloadTask();
    }

    public void deleteDownloadTask(){
        QueryWrapper<DownloadTask> queryWrapper = new QueryWrapper<>();
        // 删除时间小于delete_time的
        long thisTime = System.currentTimeMillis() / 1000;
        queryWrapper.le("end_time", thisTime);
        queryWrapper.ne("state", 5);
        // 查询需要被删除数据的记录
        List<DownloadTask> downloadTasks = null;
        try {
            downloadTasks = downloadTaskDao.selectList(queryWrapper);
        } catch (Exception e) {
            LOG.error("定时删除模块，数据库查询失败:{}", e.getMessage());
        }

        // 开始改变数据状态为删除数据
        if (downloadTasks != null && downloadTasks.size() > 0) {
            for (DownloadTask downloadTask : downloadTasks) {
                deleteDownloadTaskFile(downloadTask);
            }
        }
    }

    public void deleteDownloadTaskRegister(){
        QueryWrapper<DownloadTaskRegister> queryWrapper = new QueryWrapper<>();
        // 删除时间小于delete_time的
        long thisTime = System.currentTimeMillis() / 1000;
        queryWrapper.le("delete_time", thisTime);
        queryWrapper.ne("type", 4);
        // 查询需要被删除数据的记录
        List<DownloadTaskRegister> downloadTaskRegisters = null;
        try {
            downloadTaskRegisters = taskRegisterDao.selectList(queryWrapper);
        } catch (Exception e) {
            LOG.error("定时删除模块，数据库查询失败:{}", e.getMessage());
        }

        // 开始改变数据状态为删除数据
        if (downloadTaskRegisters != null && downloadTaskRegisters.size() > 0) {
            for (DownloadTaskRegister downloadTaskRegister : downloadTaskRegisters) {
                deleteDownloadTaskRegisterFile(downloadTaskRegister);
            }
        }
    }

    private Boolean deleteDownloadTaskFile(DownloadTask downloadTask) {
        Integer id = downloadTask.getId();
        DownloadTask update = new DownloadTask();
        update.setId(id);
        String path = pcapDownloadPath+downloadTask.getPath();
        if (path != null) {
            File file = new File(path);
            if (file.exists()) {
                file.delete();
            }
        }
        try {
            update.setState(5);
            downloadTaskDao.updateById(update);
        } catch (Exception e) {
            LOG.error("Id为：[ {} ]数据库状态更新失败: ", id, e);
            return false;
        }
        return true;
    }

    private Boolean deleteDownloadTaskRegisterFile(DownloadTaskRegister downloadTaskRegister) {
        long thisTime = System.currentTimeMillis() / 1000;
        Integer id = downloadTaskRegister.getId();
        DownloadTaskRegister update = new DownloadTaskRegister(id, thisTime);
        String path = downloadTaskRegister.getPath();
        if (path != null) {
            File file = new File(path);
            if (file.exists()) {
                file.delete();
            }
        }
        try {
            update.setType(4);
            taskRegisterDao.updateById(update);
        } catch (Exception e) {
            LOG.error("Id为：[ {} ]数据库状态更新失败: ", id, e);
            return false;
        }

        return true;
    }


    private Map<String, String> getSessionExpTitle() {
        // 组装会话导出的日志表头以及中文对照
        Map<String, String> expTitleMap = new LinkedHashMap<>();
        expTitleMap.put("SessionId", "会话ID");
        expTitleMap.put("taskName", "任务名称");
        expTitleMap.put("sIp", "客户端IP");
        expTitleMap.put("sPort", "客户端端口");
        expTitleMap.put("sMac", "客户端MAC");
        expTitleMap.put("sIpCountry", "客户端IP所在国家");
        expTitleMap.put("sIpCity", "客户端IP所在城市");
        expTitleMap.put("sIpSubdivisions", "客户端IP所在省份");
        expTitleMap.put("sIpLongitude", "客户端IP经度");
        expTitleMap.put("sIpLatitude", "客户端IP纬度");
        expTitleMap.put("sPayloadBytes","客户端发送字节数");
        expTitleMap.put("sPayloadNum", "客户端发送包数");
        expTitleMap.put("sSSLFinger", "客户端SSL指纹");
        expTitleMap.put("sHTTPFinger", "客户端HTTP指纹");
        expTitleMap.put("sMinHopCount", "客户端发送TTL最小距离");
        expTitleMap.put("sMaxHopCount", "客户端发送TTL最大距离");
        expTitleMap.put("sInitialTTL", "客户端发送原始TTL");
        expTitleMap.put("dIp", "服务端IP");
        expTitleMap.put("dPort", "服务端端口");
        expTitleMap.put("dMac", "服务端MAC");
        expTitleMap.put("dIpCountry", "服务端IP所在国家");
        expTitleMap.put("dIpCity", "服务端IP所在城市");
        expTitleMap.put("dIpSubdivisions", "服务端IP所在省份");
        expTitleMap.put("dIpLongitude", "服务端IP经度");
        expTitleMap.put("dIpLatitude", "服务端IP纬度");
        expTitleMap.put("dPayloadBytes", "服务端发送字节数");
        expTitleMap.put("dPayloadNum", "服务端发送包数");
        expTitleMap.put("dSSLFinger", "服务端SSL指纹");
        expTitleMap.put("dHTTPFinger", "服务端HTTP指纹");
        expTitleMap.put("dMinHopCount", "服务端发送TTL最小距离");
        expTitleMap.put("dMaxHopCount", "服务端发送TTL最大距离");
        expTitleMap.put("Labels", "标签");
        expTitleMap.put("ProName", "IP协议");
        expTitleMap.put("AppName", "应用协议");
        expTitleMap.put("ThreadId", "线程ID");
        expTitleMap.put("StartTime", "会话开始时间");
        expTitleMap.put("EndTime", "会话结束时间");
        expTitleMap.put("Duration", "会话持续时间");
        expTitleMap.put("TotalBytes", "会话总字节数");
        return expTitleMap;

    }


    private List<String> getMetadataExpTitle(String dataKey) {
        List<String> titles = new ArrayList<>();
        // 公共表头
        titles.add("SessionId");
        titles.add("sIp");
        titles.add("sPort");
        titles.add("dIp");
        titles.add("dPort");
        switch (dataKey) {
            case "HTTP":
                titles.add("Url");
                titles.add("Act");
                titles.add("Host");
                titles.add("Response");
                titles.add("Client.Content-Type");
                titles.add("Client.Accept");
                titles.add("Clilent.Host");
                titles.add("Client.User-Agent");
                titles.add("Client.Title");
                titles.add("Server.Content-Type");
                titles.add("Server.Accept-Ranges");
                titles.add("Server.Content-Encoding");
                titles.add("Server.Content-Length");
                titles.add("Server.Title");
                break;
            case "DNS":
                titles.add("Flags");
                titles.add("Que");
                titles.add("Ans");
                titles.add("Auth");
                titles.add("Add");
                titles.add("Query");
                titles.add("Answer");
                titles.add("Domain");
                titles.add("DomainIp");
                break;
            case "SSL":
                titles.add("cSSLVersion");
                titles.add("CH_Version");
                titles.add("CH_Time");
                titles.add("CH_Ciphersuit");
                titles.add("CH_CiphersuitNum");
                titles.add("CH_ExtentionNum");
                titles.add("CH_ServerName");
                titles.add("CH_ServerNameType");
                titles.add("CH_SessionTicket");
                titles.add("CH_ALPN");
                titles.add("sCertHash");
                titles.add("sCertNum");
                titles.add("sSSLFinger");
                titles.add("sSSLVersion");
                titles.add("SH_Version");
                titles.add("SH_Time");
                titles.add("SH_Cipersuite");
                titles.add("SH_CompressionMethod");
                titles.add("SH_ALPN");
                titles.add("SH_ExtentionNum");
                titles.add("SH_Extention");
                titles.add("dCertHash");
                titles.add("dCertHashStr");
                titles.add("dCertNum");
                titles.add("dSSLFinger");
                break;

        }

        return titles;
    }
}
