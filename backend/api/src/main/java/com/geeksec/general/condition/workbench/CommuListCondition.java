package com.geeksec.general.condition.workbench;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommuListCondition {

    /**
     * 是否升序
     */
    @JsonProperty("asc")
    private Boolean asc;

    /**
     * 展示数目
     */
    @JsonProperty("limit")
    private Integer limit;

    /**
     * 当前页数
     */
    @JsonProperty("page")
    private Integer page;

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private List<HashMap<String, Object>> query;

    /**
     * 查询任务ID集合
     */
    @JsonProperty("task_id")
    private List<Integer> taskId;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    private Long startTime = 0L;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private Long endTime = 0L;

    /**
     * 是否是首页~ true = 首页
     */
    @JsonProperty("is_front_Page")
    private Boolean isFrontPage = false;

    /**
     * 0:探针系统   1:挖矿
     */
    @JsonProperty("sys_type")
    private Integer sysType = 0;
}
