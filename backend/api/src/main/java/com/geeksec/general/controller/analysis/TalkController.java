package com.geeksec.general.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.*;
import com.geeksec.analysis.entity.vo.LabelAggVo;
import com.geeksec.analysis.entity.vo.RangeSessionVo;
import com.geeksec.analysis.entity.vo.SessionAggInfoVo;
import com.geeksec.analysis.service.MetadataService;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/anay")
@Log4j2
public class TalkController {

    @Autowired
    private MetadataService metadataService;

    /**
     * 元数据列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/pro/metadata")
    public ResultVo getProMetadata(@RequestBody MetadataCondition condition) {
        try {
            return metadataService.getMetadataList(condition);
        } catch (Exception e) {
            log.error("元数据列表请求异常，e={}", e.getMessage());
            throw new GkException(GkErrorEnum.METADATA_LIST_QUERY_ERROR);
        }
    }

    /**
     * 标签聚合 列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/tag/list")
    public ResultVo<LabelAggVo> getLabelsInfoList(@RequestBody AnalysisBaseCondition condition) {
        try {
            return metadataService.getLabelsInfoList(condition);
        } catch (Exception e) {
            log.error("标签聚合列表，e={}", e);
            throw new GkException(GkErrorEnum.TAG_AGGR_QUERY_ERROR);
        }

    }

    /**
     * 时间分段会话连接图
     *
     * @param condition
     * @return
     */
    @PostMapping("/range/list")
    public ResultVo<List<RangeSessionVo>> getRangeSessionList(@RequestBody RangeSessionCondition condition) {
        return metadataService.getRangeSessionList(condition);
    }

    /**
     * 会话分析：会话-聚合列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/session/agg")
    public ResultVo<PageResultVo<SessionAggInfoVo>> getSessionAggList(@RequestBody SessionAggInfoCondition condition) {
        try {
            return metadataService.getSessionAggList(condition);
        } catch (Exception e) {
            log.error("会话分析聚合类，e={}", e.getMessage());
            throw new GkException(GkErrorEnum.SESSION_AGGR_QUERY_ERROR);
        }
    }

    /**
     * 会话分析：元数据-聚合列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/metadata/agg")
    public ResultVo<PageResultVo<JSONObject>> getMetadataAggList(@RequestBody MetadataAggInfoCondition condition) {
        try {
            return metadataService.getMetadataAggList(condition);
        } catch (Exception e) {
            log.error("元数据聚合类，e={}", e);
            throw new GkException(GkErrorEnum.SESSION_METADATA_AGGR_ERROR);
        }
    }
}
