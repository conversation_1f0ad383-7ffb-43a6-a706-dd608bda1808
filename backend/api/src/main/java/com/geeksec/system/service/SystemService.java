package com.geeksec.system.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.vo.ProductInfoVo;
import com.geeksec.analysis.entity.vo.SystemInfoVo;
import com.geeksec.system.condition.CleanCondition;


/**
 * 系统类操作接口
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2022/3/9 9:27
 * <p>
 * shutdown()       关闭主机
 * reboot()         重启主机
 * changePassword() 修改密码
 */
public interface SystemService {

    /**
     * 关闭主机
     *
     * @return 执行状态
     */
    JSONObject shutdown();


    /**
     * 重启主机
     *
     * @return 执行状态
     */
    JSONObject reboot();


    /**
     * 修改密码
     *
     * @return 执行状态
     */
    JSONObject changePassword(String userName, String password);


    /**
     * 获取磁盘使用情况
     *
     * @return 磁盘信息
     */
    JSONObject getDiskInfoData();

    /**
     * 系统信息获取
     *
     * @return
     */
    SystemInfoVo getSystemInfo();

    /**
     * 产品信息获取
     *
     * @return
     */
    ProductInfoVo getProductInfo();

    /**
     * 数据清理
     *
     * @param condition 条件
     * @return 是否成功“
     */
    JSONObject cleanData(CleanCondition condition);

    /**
     * 系统重置
     *
     * @return
     */
    JSONObject systemReset(JSONObject json);

    /**
     * 数据清理进度
     *
     * @return
     */
    JSONObject cleanDataSchedule();

    /**
     * 更新磁盘
     *
     * @return
     */
    JSONObject diskChange();

    /**
     * 重组磁盘
     *
     * @return
     */
    JSONObject diskRebuild();

    /**
     * 准备挂载磁盘
     *
     * @return
     */
    JSONObject diskMountReady();

    /**
     * 挂载磁盘
     *
     * @return
     */
    JSONObject diskMountData();


    /**
     * 动态库文件检测
     *
     * @param ruleId 规则ID
     * @return
     */
    JSONObject checkSo(Integer ruleId);

    /**
     * docker 动态库文件检测
     *
     * @param path 地址
     * @return
     */
    JSONObject dockerCheckSo(String path);


    /**
     * 查询磁盘是否处于重组转态
     *
     * @return
     */
    JSONObject checkDiskStatus();

    /**
     * 获取侧拉框展示字段
     * @return
     */
    JSONObject getDiskField();
}
