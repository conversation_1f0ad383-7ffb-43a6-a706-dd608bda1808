package com.geeksec.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.vo.ProductInfoVo;
import com.geeksec.analysis.entity.vo.SystemInfoVo;
import com.geeksec.authentication.entity.dto.session.SessionUserInfo;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.authentication.service.UserService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.Md5Util;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.system.condition.CleanCondition;
import com.geeksec.system.service.SystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：系统相关接口
 */
@RestController
@Api(tags = "系统控制接口")
@RequestMapping("/system")
public class SystemController {

    @Autowired(required = false)
    private SystemService systemService;

    @Autowired(required = false)
    private UserService userService;

    @Autowired
    private TokenService tokenService;

    /**
     * 关闭主机操作
     * @param passwordJson
     * @return
     */
    @PostMapping("/shutdown")
    public JSONObject shutdown(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        CommonUtil.hasAllRequired(passwordJson, "password");

        if (isNotAdmin(passwordJson.getString("password"))) {
            return CommonUtil.errorJson(ErrorEnum.E_10010);
        }

        return systemService.shutdown();
    }


    /**
     * 重启主机操作
     * @param passwordJson
     * @return
     */
    @PostMapping("/reboot")
    public JSONObject reboot(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        CommonUtil.hasAllRequired(passwordJson, "password");

        if (isNotAdmin(passwordJson.getString("password"))) {
            return CommonUtil.errorJson(ErrorEnum.E_10010);
        }

        return systemService.reboot();
    }


    @PostMapping("/change/password")
    @ApiOperation("用户密码修改")
    public JSONObject changePassword(@RequestBody JSONObject requestJson) {
        Integer userId = tokenService.getUserInfoByToken();
        String userName = userService.getUserNameById(Long.valueOf(userId));
        CommonUtil.hasAllRequired(requestJson, "new_password,password");

        // 前端接收的新密码
        String newPassword = requestJson.getString("new_password");

        // 新密码不允许为null
        if (StringUtils.isEmpty(newPassword)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // 使用当前用户到数据库中去查询数据
        UserInfoVo userData = userService.queryUserData(userName);

        // 判断输入密码是否正确
        String passwordMD5 = Md5Util.encodeMd5(requestJson.getString("password"));
        if (!userData.getPassword().equals(passwordMD5)) {
            return CommonUtil.errorJson(ErrorEnum.E_10010);
        }

        // 判断用户当前密码与数据库密码是否一致
        String newPasswordMD5 = Md5Util.encodeMd5(newPassword);
        if (newPasswordMD5.equals(userData.getPassword())) {
            return CommonUtil.errorJson(ErrorEnum.E_10012);
        }

        return systemService.changePassword(userName, newPassword);
    }


    @PostMapping("/disk/info")
    @ApiOperation("查询磁盘使用信息")
    public JSONObject getDiskInfoData() {
        return systemService.getDiskInfoData();
    }

    @PostMapping("/disk/change")
    @ApiOperation("更新磁盘")
    public JSONObject diskChange(@RequestBody JSONObject passwordJson) {
        CommonUtil.hasAllRequired(passwordJson, "password");

        if (isNotAdmin(passwordJson.getString("password"))) {
            return CommonUtil.errorJson(ErrorEnum.E_10010);
        }
        return systemService.diskChange();
    }

    @PostMapping("/disk/rebuild")
    @ApiOperation("重组磁盘")
    public JSONObject diskRebuild() {
        return systemService.diskRebuild();
    }

    @PostMapping("/disk/mount/ready")
    @ApiOperation("准备挂载磁盘")
    public JSONObject diskMountReady(){
        return systemService.diskMountReady();
    }

    @PostMapping("/disk/mount/data")
    @ApiOperation("挂载磁盘")
    public JSONObject diskMountData(){
        return systemService.diskMountData();
    }

    @PostMapping("/disk/status")
    @ApiOperation("查询磁盘是否处于重组状态")
    public JSONObject checkDiskStatus() {
        return systemService.checkDiskStatus();
    }

    @PostMapping("/disk/field")
    @ApiOperation("更换磁盘或读取磁盘")
    public JSONObject getdiskField(){
        return systemService.getDiskField();
    }

    @PostMapping("/info")
    @ApiOperation("系统信息查询")
    public JSONObject systemInfo() {
        HashMap<String, String> result = new HashMap<>(2);
        // 查询系统相关信息 & 产品相关信息
        SystemInfoVo systemInfo = systemService.getSystemInfo();
        ProductInfoVo productInfo = systemService.getProductInfo();
        // 数据为null表示异常
        if ((productInfo == null) || (systemInfo == null)) {
            throw new GkException(GkErrorEnum.FAIL);
        }
        // 加入返回值
        result.put("system", JSONObject.toJSONString(systemInfo));
        result.put("product", JSONObject.toJSONString(productInfo));
        return CommonUtil.successJson(result);
    }

    @PostMapping("/clean/data")
    @ApiOperation("数据清理")
    public JSONObject cleanData(@RequestBody CleanCondition condition) {
        // 参数效验
        Integer taskId = condition.getTaskId();
        Integer userId = condition.getUserId();
        List<String> cleanList = condition.getCleanList();
        if (taskId == null || cleanList == null || cleanList.size() < 1 || userId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        return systemService.cleanData(condition);
    }

    @PostMapping("/reset")
    @ApiOperation("系统重置")
    public JSONObject systemRest(@RequestBody JSONObject json) {
        CommonUtil.hasAllRequired(json, "user_id");
        return systemService.systemReset(json);
    }

    @PostMapping("/clean/data/schedule")
    @ApiOperation("数据清理进度")
    public JSONObject cleanDataSchedule() {
        return systemService.cleanDataSchedule();
    }

    @PostMapping("/check/so")
    @ApiOperation("动态库文件检测")
    public JSONObject checkSo(@RequestBody JSONObject json) {
        CommonUtil.hasAllRequired(json, "rule_id");

        Integer ruleId;
        try {
            ruleId = json.getInteger("rule_id");
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.FAIL);
        }

        return systemService.checkSo(ruleId);
    }

    @PostMapping("/check/docker/so")
    @ApiOperation("docker 动态库文件检测")
    public JSONObject dockerCheckSo(@RequestBody JSONObject json) {
        CommonUtil.hasAllRequired(json, "path");
        return systemService.dockerCheckSo(json.getString("path"));
    }


    /**
     * parse user token.
     *
     * @return user token.
     */
    private SessionUserInfo parseToken() {
        String token = MDC.get("token");
        return tokenService.getUserInfoFromCache(token);
    }


    /**
     * 是否管理员
     *
     * @param password
     * @return
     */
    private boolean isNotAdmin(String password) {
        Integer userId = tokenService.getUserInfoByToken();

        if (userId!=1) {
            return true;
        }

        // 获取password MD5形式
        String passwordMD5 = Md5Util.encodeMd5(password);
        // 获取管理员账户
        UserInfoVo admin = userService.queryUserData("root");
        return admin == null || !admin.getPassword().equals(passwordMD5);
    }
}
