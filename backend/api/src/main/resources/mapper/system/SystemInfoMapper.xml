<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.SystemInfoDao">

    <select id="getSystemInfo" resultType="com.geeksec.analysis.entity.vo.SystemInfoVo">
        select si.hostname, si.osinfo, si.timeS, st.time
        from (select hostname, osinfo, timeS from sys_info order by id desc limit 1) as si
                 join
                 (select `time` from tb_system_time order by id desc limit 1) as st
    </select>

    <select id="getProductInfo" resultType="com.geeksec.analysis.entity.vo.ProductInfoVo">
        select pi.product, pi.version, pi.SN, si.privileged_time
        from (select product, version, SN from tb_product_info) as pi
                join
             (select privileged_time from sys_info order by id desc limit 1) as si
    </select>

    <update id="modifyShutdownValue" parameterType="int">
        update th_analysis.tb_valset set val_id = #{value} where valset_id = 'Shutdown_Restart'
    </update>
</mapper>
