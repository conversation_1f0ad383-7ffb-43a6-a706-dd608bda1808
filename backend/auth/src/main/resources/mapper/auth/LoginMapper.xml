<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.geeksec.authentication.dao.LoginDao">
    <select id="checkUser" resultType="com.geeksec.authentication.entity.vo.LoginUserInfoVo">
        SELECT u.id userId,u.username,u.show_username showUsername,groub_id groubId
        FROM tb_user u
        WHERE u.username = #{username}
          AND u.password = #{password}
          AND u.status = 1
    </select>

    <resultMap id="userInfoMap" type="com.geeksec.authentication.entity.dto.session.SessionUserInfo">
        <id column="id" property="userId"/>
        <result column="username" property="username"/>
        <result column="show_username" property="showUsername"/>
        <collection property="roleIds" ofType="java.lang.Integer">
            <constructor>
                <arg column="roleId"/>
            </constructor>
        </collection>
        <collection property="menuList" ofType="java.lang.String">
            <constructor>
                <arg column="menuCode"/>
            </constructor>
        </collection>
        <collection property="permissionList" ofType="java.lang.String">
            <constructor>
                <arg column="permissionCode"/>
            </constructor>
        </collection>
        <collection property="routerList" ofType="java.lang.String">
            <constructor>
                <arg column="routerPath"/>
            </constructor>
        </collection>
    </resultMap>

    <select id="getUserInfo" parameterType="string" resultMap="userInfoMap">
        select tu.id,
               tu.username,
               tu.show_username,
               tu.web_show,
               tu.created_time,
               tu.last_modified_time,
               tug.role_id         roleId,
               tg.role_name,
               tp.menu_code         menuCode,
               tp.permission_code   permissionCode,
               tp.router_path   routerPath
        from tb_user tu
        LEFT JOIN tb_user_role tug on tu.id = tug.user_id
        LEFT JOIN tb_role_permission tgp on tgp.role_id = tug.role_id
        LEFT JOIN tb_permission tp on tgp.permission_id = tp.id
        LEFT JOIN tb_role tg on tg.role_id = tug.role_id
        WHERE tu.username = #{username} and tu.status = 1
    </select>

    <select id="getAllMenu" resultType="java.lang.String">
        select distinct(menu_code)
        from tb_permission;
    </select>

    <select id="getAllPermissionCode" resultType="java.lang.String">
        select distinct(permission_code)
        from tb_permission;
    </select>

    <select id="getAllRemoteKey" resultType="java.lang.String">
        select api_key from tb_user_remote_key;
    </select>

    <select id="checkUserExist" resultType="java.lang.Boolean">
        select count(1) from tb_user where username = #{username};
    </select>

    <insert id="insertRemoteKey">
        insert into tb_user_remote_key (api_key,created_time) values (#{remoteKey},now())
    </insert>
</mapper>
