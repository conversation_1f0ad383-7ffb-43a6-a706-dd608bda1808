<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.geeksec.authentication.dao.RoleDao">

    <resultMap id="roleMap" type="com.geeksec.authentication.entity.vo.RoleInfoVo">
        <id column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="remarks" property="remarks"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <resultMap id="permissionMap" type="com.geeksec.authentication.entity.dto.permission.PermissionDto">
        <id column="id" property="id"/>
        <result column="url" property="url"/>
        <result column="parent_menu" property="parentMenu"/>
        <result column="permission_code" property="permissionCode"/>
        <result column="permission_name" property="permissionName"/>
        <result column="required_permission" property="requiredPermission"/>
    </resultMap>

    <insert id="addUserRole">
        insert into tb_user_role (user_id,role_id) values
        <foreach collection="roleIds" item="item" index="index" separator=",">
            (#{userId},#{item})
        </foreach>
    </insert>


    <delete id="removeUserAllRoleRelated">
        delete from tb_user_role
        where user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            ${item}
        </foreach>
    </delete>

    <delete id="removeRoleAllRelated">
        delete from tb_role_permission
        where role_id = ${roleId}
    </delete>

    <delete id="deleteRole">
        delete from tb_role
        where role_id in
        <foreach collection="roleIds" open="(" separator="," close=")" item="item">
            ${item}
        </foreach>
    </delete>

    <delete id="batchRemoveRoleAllRelated">
        delete from tb_role_permission
        where role_id in
        <foreach collection="roleIds" open="(" separator="," close=")" item="item">
            ${item}
        </foreach>
    </delete>

    <select id="listRole" resultMap="roleMap">
        select *
        from tb_role
        <where>
            status = 1
            <if test="condition.query != null and condition.query != ''">
                and role_name LIKE CONCAT('%',#{condition.query,jdbcType=VARCHAR},'%')
            </if>
        </where>
        order by created_time ${condition.sortOrder}
    </select>

    <select id="getPermissionById" resultType="java.lang.Integer">
        select
        permission_id
        from tb_role_permission
        where role_id = #{roleId}
    </select>

    <select id="getAllPermission" resultMap="permissionMap">
        select * from tb_permission where status = 1;
    </select>

    <select id="getMenuInfoByLevelAndParent" resultType="java.util.HashMap">
        select id,menu_code, menu_name, router_path,parent_menu
        from tb_permission
        where level = #{level}
        <if test="parentMenu != null and parentMenu != ''">
            and parent_menu = #{parentMenu}
        </if>
        order by id asc
    </select>

    <select id="getRoleById" resultMap="roleMap">
        select * from tb_role where role_id = #{roleId}
    </select>

    <select id="listAllRole" resultMap="roleMap">
        select * from tb_role where status = 1;
    </select>

    <select id="getExistRoleName" resultType="java.lang.Integer">
        select count(1) from tb_role where role_name = #{roleName}
    </select>

    <select id="getPermissionByParent" resultMap="permissionMap">
    select *
    from tb_permission
    where level = 0
    and parent_menu = #{menuName}
    order by id asc
    </select>

    <select id="getRoleUserCount" resultType="java.lang.Integer">
        select count(0) from tb_user_role
        where role_id in
        <foreach collection="roleIds" item="role" separator="," open="(" close=")">
            ${role}
        </foreach>
    </select>

    <insert id="batchAddUserRoleRelated">
        insert into tb_user_role
        (user_id, role_id) values
        <foreach collection="roleIds" item="item" separator=",">
            (#{userId},#{item})
        </foreach>
    </insert>


    <insert id="addRole">
        insert into tb_role
        (role_name,remarks,status,created_time,last_modified_time) values
        (#{roleName},#{remarks},1,now(),now())
    </insert>

    <update id="updateRoleById">
        update tb_role
        <trim prefix="set">
            <if test="condition.roleName != null and condition.roleName != ''">role_name = #{condition.roleName},</if>
            <if test="condition.remarks != null and condition.remarks != ''">remarks = #{condition.remarks},</if>
            last_modified_time = now()
            where role_id = #{condition.roleId}
        </trim>
    </update>


    <insert id="addRolePermissionRelated">
        insert into tb_role_permission (role_id,permission_id,created_time,last_modified_time)
        values
        <foreach collection="permissionList" item="item" separator=",">
            (#{roleId},#{item},now(),now())
        </foreach>
    </insert>

    <insert id="batchInsertMenuInfo"  useGeneratedKeys="true" keyProperty="id">
        insert into tb_permission (menu_code,menu_name,router_path,url,permission_code,permission_name,level,parent_menu,required_permission,status,created_time,last_modified_time)
        values
        <foreach collection="menuList" item="item" separator="," >
            (#{item.menuCode},#{item.menuName},#{item.routerPath},#{item.url},#{item.permissionCode},#{item.permissionName},${item.level},#{item.parentMenu},#{item.requiredPermission},${item.status},now(),now())
        </foreach>
    </insert>


</mapper>
