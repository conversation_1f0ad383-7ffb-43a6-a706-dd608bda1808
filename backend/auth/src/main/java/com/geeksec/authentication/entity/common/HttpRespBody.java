package com.geeksec.authentication.entity.common;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 *
 */
public class HttpRespBody<T> implements Serializable{

	private static final long serialVersionUID = 1L;
	private static final int OK = 1;
	private static final int ERROR =  0 ;

	 @ApiModelProperty(value = "返回CODE:1为正常，0位异常")
	private int code ;
	 @ApiModelProperty(value = "返回数据")
	private T data;

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public HttpRespBody(int code, T data) {
		super();
		this.code = code;
		this.data = data;
	}

	public static HttpRespBody<String> okBody() {
		return okBody("ok");
	}

	public static <T> HttpRespBody<T> okBody(T data) {
		HttpRespBody<T> httpRespBody = new HttpRespBody<T>(OK,data);
		return httpRespBody;
	}

	public static HttpRespBody<String> errorBody() {
		return errorBody("操作失败，请联系管理员");
	}

	public static <T> HttpRespBody<T> errorBody(T data) {
		HttpRespBody<T> httpRespBody = new HttpRespBody<T>(ERROR,data);
		return httpRespBody;
	}
}
