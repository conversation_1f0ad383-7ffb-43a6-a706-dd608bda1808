package com.geeksec.authentication.util;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class JsonUtils {

    /**
     * 读取json文件
     */
    public static String readJsonFile(String path) {
        Path filePath = Paths.get(path);
        StringBuilder data = new StringBuilder();
        try {
            for (String line : Files.readAllLines(filePath)) {
                data.append(line);
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return data.toString();
    }

    /**
     * 写出json文件
     */
    public static void writeJsonFile(String newJsonString, String path) {
        try (FileWriter fw = new FileWriter(path)) {
            fw.write(newJsonString + System.lineSeparator());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
