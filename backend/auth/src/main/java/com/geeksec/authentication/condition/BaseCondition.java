package com.geeksec.authentication.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class BaseCondition {

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    private Integer currentPage = 1;

    /**
     * 当前页展示数量
     */
    @JsonProperty("page_size")
    private Integer pageSize = 10;

    /**
     * 升降序
     */
    @JsonProperty("sort_order")
    private String sortOrder = "desc";
}
