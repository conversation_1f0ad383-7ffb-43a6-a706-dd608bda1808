package com.geeksec.authentication.util.constants;

/**
 * @author: heeexy
 * @date: 2017/10/24 10:16
 */
public enum ErrorEnum {
    /*
     * 错误信息
     * */
    E_400("400", "请求处理异常"),
    E_500("500", "请求方式有误,请检查 GET/POST"),
    E_501("501", "请求路径不存在"),
    E_502("502", "权限不足"),
    E_10001("10001","新增角色信息为空"),
    E_10002("10002","修改角色信息为空"),
    E_10003("10003","修改用户信息失败"),
    E_10004("10004","获取角色所拥有权限失败"),
    E_10005("10005","当前修改角色不存在"),
    E_10008("10008", "用户删除失败"),
    E_10009("10009", "账户已存在,请勿重复创建"),
    E_10010("10010", "账号/密码错误"),
    E_10011("10011","角色已存在，请勿重复创建"),
    E_10012("10012","新密码不能与当前密码一致"),
    E_10013("10013","当前密码错误，请重新输入"),
    E_10014("10014","用户名重复，无法修改当前用户"),
    E_10015("10013","删除角色包含管理员角色，不可删除"),
    E_10016("10014","删除角色中有用户正在使用当前角色，删除失败"),
    E_10017("10017","不可删除管理员角色"),
    E_10018("10018","数据清理中，不可执行此操作"),


    E_20011("20011", "登陆已过期,请重新登陆"),
    E_30001("30001","角色不拥有此功能权限"),
    E_40001("40001","登录超时"),
    E_40002("40002","远程登录口令错误"),
    E_90003("90003", "缺少必填参数");

    private final String errorCode;

    private final String errorMsg;

    ErrorEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return Integer.valueOf(errorCode);
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}
