package com.geeksec.authentication.condition.role;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class RoleCondition {

    @JsonProperty("role_id")
    private Long roleId;

    @JsonProperty("role_name")
    private String roleName;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("status")
    private int status;

    @JsonProperty("permission_list")
    private List<Integer> permissionList;
}
