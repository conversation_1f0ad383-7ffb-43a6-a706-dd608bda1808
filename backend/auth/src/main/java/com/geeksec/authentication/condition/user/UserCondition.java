package com.geeksec.authentication.condition.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class UserCondition {

    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("username")
    private String username;

    @JsonProperty("show_username")
    private String showUsername;

    @JsonProperty("password")
    private String password;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("email")
    private String email;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("role_ids")
    private List<Integer> roleIds;

    @JsonProperty("remarks")
    private String remarks;

}
