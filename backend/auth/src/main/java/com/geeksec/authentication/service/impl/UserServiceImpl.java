package com.geeksec.authentication.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.authentication.condition.user.UserCondition;
import com.geeksec.authentication.condition.user.UserQueryCondition;
import com.geeksec.authentication.dao.RoleDao;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.UserService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.Md5Util;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("auth-db")
public class UserServiceImpl implements UserService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Autowired
    private UserDao userDao;

    @Autowired
    private RoleDao roleDao;


    @Override
    public HashMap<String,Object> listUser(UserQueryCondition condition) {

        logger.info("根据条件查询当前可用用户列表,query -->{}", condition.getQuery());

        String query = condition.getQuery();
        int page = condition.getCurrentPage();
        int pageSize = condition.getPageSize();
        String sortOrder = condition.getSortOrder();

        List<UserInfoVo> userList = new ArrayList<>();
        try {
            Integer count = userDao.countUser();
            userList = userDao.listUser(condition);
            List<UserInfoVo> result = null;
            // 手动分页
            if ((pageSize * (page -1 ) + pageSize) > userList.size()){
                result = userList.subList(pageSize * (page -1),userList.size());
            }else{
                result = userList.subList(pageSize * (page -1),(pageSize * (page - 1) + pageSize));
            }
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("list",result);
            resultMap.put("total",count);
            return resultMap;
        } catch (Exception e) {
            logger.error("根据条件查询用户列表失败,error-->{}", e);
            return null;
        }
    }

    @Override
    public JSONObject addUser(UserCondition condition) {

        // 判断是否有同名用户
        int exist = userDao.getExistUserName(condition.getUsername());
        if (exist > 0) {
            return CommonUtil.errorJson(ErrorEnum.E_10009);
        }

        // 存入MD5加密后密码格式
        String passwordMd5 = Md5Util.encodeMd5(condition.getPassword());
        condition.setPassword(passwordMd5);

        try {
            // 添加用户
            userDao.addUser(condition);
            long userId = condition.getUserId();
            // 添加用户权限
            roleDao.addUserRole(userId, condition.getRoleIds());
        } catch (Exception e) {
            logger.error("新增用户失败,error--->{}", e.getMessage());
        }

        return CommonUtil.successJson("添加用户" + condition.getUsername() + "成功！");
    }

    @Override
    public JSONObject updateUser(UserCondition condition) {
        logger.info("修改userId为{}的用户信息", condition.getUserId());
        // 判断是否为超级管理员用户(用户组ID为101)
        List<Integer> roleIds = condition.getRoleIds();
        if (roleIds.contains(101)) {
            return CommonUtil.successJson("不可修改超级管理员用户信息");
        }

        String currentName = userDao.getUserNameById(condition.getUserId());
        // 判断预修改用户名是否存在
        List<String> userNames = userDao.getAllUsername();
        for (String username : userNames){
            if (username.equals(condition.getUsername()) && !username.equals(currentName)){
                return CommonUtil.errorJson(ErrorEnum.E_10014);
            }
        }

        if (StringUtils.hasText(condition.getPassword())){
            String password = Md5Util.encodeMd5(condition.getPassword());
            condition.setPassword(password);
        }else {
            condition.setPassword(null);
        }

        try {
            //1.修改用户信息
            userDao.updateUser(condition);
            //2.删除原有用户与角色关联并更新
            List<Long> userList = new ArrayList<>();
            userList.add(condition.getUserId());
            // 若修改的用户角色列为空，则不新增权限列表
            if (roleIds.size() >0){
                roleDao.removeUserAllRoleRelated(userList);
                roleDao.batchAddUserRoleRelated(roleIds,condition.getUserId());
            }
        } catch (Exception e) {
            logger.error("修改用户信息失败,error--->{}",e.getMessage());
            return CommonUtil.errorJson(ErrorEnum.E_10003);
        }

        return CommonUtil.successJson("修改用户成功!");
    }

    @Override
    public JSONObject deleteUser(List<Long> userIds) {
        logger.info("删除userIds为{}的用户",userIds);

        if (userIds.contains(101)) {
            // 管理员用户不可删除
            return CommonUtil.errorJson(ErrorEnum.E_10015);
        }

        try{
            userDao.batchDeleteUser(userIds);
            roleDao.removeUserAllRoleRelated(userIds);
        }catch (Exception e){
            logger.error("删除用户失败！,{}",e.getMessage());
            return CommonUtil.errorJson(ErrorEnum.E_10008);
        }
        return CommonUtil.successJson("删除用户成功");
    }

    @Override
    public UserInfoVo queryUserData(String username) {
        return userDao.queryUserData(username);
    }

    @Override
    public UserInfoVo queryUserData(Integer userId) {
        return userDao.getUserInfo(userId);
    }

    @Override
    public ResultVo getLoginUserInfo(String token) {
        // 获取到当前登录用户的登录ID
        Object loginObject = StpUtil.getLoginIdByToken(token);
        if (ObjectUtil.isEmpty(loginObject)) {
            throw new GkException(GkErrorEnum.LOGIN_EXPIRE);
        }
        Integer loginId = Integer.valueOf(loginObject.toString());
        UserInfoVo userInfoVo = userDao.getUserInfoById(loginId);

        return ResultVo.success(userInfoVo);
    }

    @Override
    public String getUserNameById(Long userId) {
        return userDao.getUserNameById(userId);
    }
}
