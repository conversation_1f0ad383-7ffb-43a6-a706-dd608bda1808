package com.geeksec.authentication.config.filter;

import cn.dev33.satoken.stp.StpUtil;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: [角色权限]控制拦截器
 */
@Aspect
@Slf4j
@Component
@Order(2)
public class PermissionAspect {

    /**
     * 固定前置token
     */
    @Value("${static.token}")
    private String standarToken;

    @Autowired
    TokenService tokenService;

    List<String> methodWithoutVerify = Lists.newArrayList("authLogin", "remoteAuthLogin", "generateApikey", "remoteTargetDetail", "getAppDict", "searchEsField");


    @Before("@annotation(com.geeksec.authentication.config.annotation.RequiresPermissions)" +
            "||@annotation(org.springframework.web.bind.annotation.PostMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.GetMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.PostMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.DeleteMapping)")
    public void before(JoinPoint joinPoint) {
        log.debug("开始校验登陆状态");

        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        String methodName = method.getName();
        String token = MDC.get("token");

        // 免登录 判断方法和token
        List<String> methodsWithoutVerify = Arrays.asList("authLogin", "searchEsField", "getAppDict");
        if (StringUtils.isEmpty(token) && methodsWithoutVerify.contains(methodName)) {
            return;
        }

        if (token.equals(standarToken)) {
            return;
        }

        if (!methodWithoutVerify.contains(methodName)) {
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId == null) {
                throw new GkException(GkErrorEnum.LOGIN_EXPIRE);
            }
        }
    }
}
