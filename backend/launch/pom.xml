<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta-backend</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>launch</artifactId>
    
    <dependencies>
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>common</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>auth</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>api</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>es-search</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ws</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>analysis</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ngbatis</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

    </dependencies>


    <build>
        <finalName>GeeksecApiApplication</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>config/**</exclude>
                </excludes>
            </resource>

            <resource>
                <directory>src/main/resources/config/${profiles.name}/${profiles.active}</directory>
                <!--targetPath是指定目标文件打包到哪个文件夹下,这里默认就是放到resources下,也就是打包后的class文件夹下-->
                <!--<targetPath>config</targetPath>-->
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.5.RELEASE</version>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.geeksec.GeeksecApiApplication</mainClass>
                    <layout>ZIP</layout>
                    <executable>true</executable>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
<!--                <version>1.4.13</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                            <goal>push</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <dockerInfoDirectory>docker</dockerInfoDirectory>-->
<!--                    <repository>hb.gs.lan/nta/geeksec-be</repository>-->
<!--                    <tag>${project.version}</tag>-->
<!--                    <buildArgs>-->
<!--                        <JAR_FILE>launch/target/${project.build.finalName}.jar</JAR_FILE>-->
<!--                    </buildArgs>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <profiles>
        <profile>
            <!-- 分析平台（开发）-->
            <id>nta-dev</id>
            <properties>
                <profiles.name>nta</profiles.name>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <!-- 分析平台（测试）-->
            <id>nta-test</id>
            <properties>
                <profiles.name>nta</profiles.name>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 分析平台（生产）-->
            <id>nta-prod</id>
            <properties>
                <profiles.name>nta</profiles.name>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>
</project>