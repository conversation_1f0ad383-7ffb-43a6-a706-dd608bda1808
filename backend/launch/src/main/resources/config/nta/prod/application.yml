server:
  port: 19000
logging:
  config: classpath:logback.xml
spring:
  profiles:
    active: nta-prod
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
management:
  endpoints:
    web:
      exposure:
        include: health
elasticsearch:
  es_connect_index: connectinfo*
mybatis-plus:
  mapper-locations: classpath:*/mapper/**.xml
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: geeksec-token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 604800
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: false

# 异步线程配置
# 配置核心线程数
thread_pool:
  core_pool_size: 8
  # 配置最大线程数
  max_pool_size: 16
  # 配置队列大小
  queue_capacity: 1000
  # 配置线程池中的线程的名称前缀
  name_prefix: async

# 屏蔽产品ID(分析平台产品 屏蔽探针 1)
shield_pro_type: 1

#各项功能启用开关
enabled:
  atlas: false
  nebula: true
  redis: true
  task_scheduled: true
