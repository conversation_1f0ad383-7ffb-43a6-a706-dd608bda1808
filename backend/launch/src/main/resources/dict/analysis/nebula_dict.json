{"IP": {"type": "vertex", "desc": "IP地址"}, "ATTACKER": {"type": "vertex", "desc": "攻击者"}, "VICTIM": {"type": "vertex", "desc": "受害者"}, "MAC": {"type": "vertex", "desc": "MAC地址"}, "APPSERVICE": {"type": "vertex", "desc": "应用服务"}, "DOMAIN": {"type": "vertex", "desc": "域名"}, "FDOMAIN": {"type": "vertex", "desc": "锚域名"}, "CERT": {"type": "vertex", "desc": "TLS证书"}, "ORG": {"type": "vertex", "desc": "组织机构"}, "SSLFINGER": {"type": "vertex", "desc": "TLS指纹"}, "UA": {"type": "vertex", "desc": "User-Agent"}, "DEVICE": {"type": "vertex", "desc": "设备类型"}, "OS": {"type": "vertex", "desc": "操作系统"}, "APP": {"type": "vertex", "desc": "应用"}, "LABEL": {"type": "vertex", "desc": "标签"}, "URL": {"type": "vertex", "desc": "URL地址"}, "ISSUER": {"type": "vertex", "desc": "证书颁发机构"}, "SUBJECT": {"type": "vertex", "desc": "证书主体"}, "src_bind": {"type": "edge", "src": "IP", "dst": "MAC", "desc": "源地址映射"}, "r_src_bind": {"type": "edge", "src": "MAC", "dst": "IP", "desc": "映射为源地址"}, "dst_bind": {"type": "edge", "src": "IP", "dst": "MAC", "desc": "目标地址映射"}, "r_dst_bind": {"type": "edge", "src": "MAC", "dst": "IP", "desc": "映射为目标地址"}, "connect_ip": {"type": "edge", "src": "IP", "dst": "IP", "desc": "网络连接发起方"}, "r_connect_ip": {"type": "edge", "src": "IP", "dst": "IP", "desc": "网络连接接收方"}, "connect_mac": {"type": "edge", "src": "MAC", "dst": "MAC", "desc": "链路层连接发起方"}, "r_connect_mac": {"type": "edge", "src": "MAC", "dst": "MAC", "desc": "链路层连接接收方"}, "client_query_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "DNS查询域名"}, "r_client_query_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名被DNS查询"}, "client_query_dns_server": {"type": "edge", "src": "IP", "dst": "IP", "desc": "向DNS服务器查询"}, "r_client_query_dns_server": {"type": "edge", "src": "IP", "dst": "IP", "desc": "接收DNS查询"}, "dns_server_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "DNS服务器解析域名"}, "r_dns_server_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名被DNS服务器解析"}, "parse_to": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名解析到IP"}, "r_parse_to": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "IP对应的域名"}, "cname": {"type": "edge", "src": "DOMAIN", "dst": "DOMAIN", "desc": "CNAME指向别名"}, "r_cname": {"type": "edge", "src": "DOMAIN", "dst": "DOMAIN", "desc": "别名的CNAME源"}, "cname_result": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "CNAME解析到IP"}, "r_cname_result": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "IP对应的CNAME"}, "client_ssl_connect_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "TLS客户端访问域名"}, "r_client_ssl_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名接收TLS请求"}, "server_ssl_connect_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "TLS服务端提供域名"}, "r_server_ssl_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名由TLS服务端提供"}, "server_use_cert": {"type": "edge", "src": "IP", "dst": "CERT", "desc": "服务端使用证书"}, "r_server_use_cert": {"type": "edge", "src": "CERT", "dst": "IP", "desc": "证书被服务端使用"}, "client_use_cert": {"type": "edge", "src": "IP", "dst": "CERT", "desc": "客户端使用证书"}, "r_client_use_cert": {"type": "edge", "src": "CERT", "dst": "IP", "desc": "证书被客户端使用"}, "client_connect_cert": {"type": "edge", "src": "IP", "dst": "CERT", "desc": "客户端验证证书"}, "r_client_connect_cert": {"type": "edge", "src": "CERT", "dst": "IP", "desc": "证书被客户端验证"}, "client_use_sslfinger": {"type": "edge", "src": "IP", "dst": "SSLFINGER", "desc": "客户端使用TLS指纹"}, "r_client_use_sslfinger": {"type": "edge", "src": "SSLFINGER", "dst": "IP", "desc": "TLS指纹被客户端使用"}, "server_use_sslfinger": {"type": "edge", "src": "IP", "dst": "SSLFINGER", "desc": "服务端使用TLS指纹"}, "r_server_use_sslfinger": {"type": "edge", "src": "SSLFINGER", "dst": "IP", "desc": "TLS指纹被服务端使用"}, "sni_bind": {"type": "edge", "src": "DOMAIN", "dst": "CERT", "desc": "SNI域名绑定证书"}, "r_sni_bind": {"type": "edge", "src": "CERT", "dst": "DOMAIN", "desc": "证书绑定SNI域名"}, "sslfinger_connect_domain": {"type": "edge", "src": "SSLFINGER", "dst": "DOMAIN", "desc": "TLS指纹关联域名"}, "r_sslfinger_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "SSLFINGER", "desc": "域名关联TLS指纹"}, "sslfinger_connect_cert": {"type": "edge", "src": "SSLFINGER", "dst": "CERT", "desc": "TLS指纹关联证书"}, "r_sslfinger_connect_cert": {"type": "edge", "src": "CERT", "dst": "SSLFINGER", "desc": "证书关联TLS指纹"}, "ua_connect_domain": {"type": "edge", "src": "UA", "dst": "DOMAIN", "desc": "UA请求域名"}, "r_ua_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "UA", "desc": "域名接收UA请求"}, "client_use_ua": {"type": "edge", "src": "IP", "dst": "UA", "desc": "客户端UA标识"}, "r_client_use_ua": {"type": "edge", "src": "UA", "dst": "IP", "desc": "UA标识客户端"}, "client_http_connect_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "HTTP客户端访问域名"}, "r_client_http_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名接收HTTP请求"}, "server_http_connect_domain": {"type": "edge", "src": "IP", "dst": "DOMAIN", "desc": "HTTP服务端提供域名"}, "r_server_http_connect_domain": {"type": "edge", "src": "DOMAIN", "dst": "IP", "desc": "域名由HTTP服务端提供"}, "ua_belong_device": {"type": "edge", "src": "UA", "dst": "DEVICE", "desc": "UA包含设备信息"}, "r_ua_belong_device": {"type": "edge", "src": "DEVICE", "dst": "UA", "desc": "设备信息包含在UA"}, "ua_belong_os": {"type": "edge", "src": "UA", "dst": "OS", "desc": "UA包含系统信息"}, "r_ua_belong_os": {"type": "edge", "src": "OS", "dst": "UA", "desc": "系统信息包含在UA"}, "ua_belong_app": {"type": "edge", "src": "UA", "dst": "APP", "desc": "UA包含应用信息"}, "r_ua_belong_app": {"type": "edge", "src": "APP", "dst": "UA", "desc": "应用信息包含在UA"}, "cert_belong_app": {"type": "edge", "src": "CERT", "dst": "APP", "desc": "证书关联应用"}, "r_cert_belong_app": {"type": "edge", "src": "APP", "dst": "CERT", "desc": "应用关联证书"}, "ip_belong_app": {"type": "edge", "src": "IP", "dst": "APP", "desc": "IP关联应用"}, "r_ip_belong_app": {"type": "edge", "src": "APP", "dst": "IP", "desc": "应用关联IP"}, "domain_belong_app": {"type": "edge", "src": "DOMAIN", "dst": "APP", "desc": "域名关联应用"}, "r_domain_belong_app": {"type": "edge", "src": "APP", "dst": "DOMAIN", "desc": "应用关联域名"}, "sslfinger_belong_app": {"type": "edge", "src": "SSLFINGER", "dst": "APP", "desc": "TLS指纹关联应用"}, "r_sslfinger_belong_app": {"type": "edge", "src": "APP", "dst": "SSLFINGER", "desc": "应用关联TLS指纹"}, "domain_belong_to": {"type": "edge", "src": "DOMAIN", "dst": "FDOMAIN", "desc": "域名属于锚域名"}, "r_domain_belong_to": {"type": "edge", "src": "FDOMAIN", "dst": "DOMAIN", "desc": "锚域名包含子域名"}, "client_app": {"type": "edge", "src": "IP", "dst": "APPSERVICE", "desc": "客户端访问应用服务"}, "r_client_app": {"type": "edge", "src": "APPSERVICE", "dst": "IP", "desc": "应用服务被客户端访问"}, "app_server": {"type": "edge", "src": "APPSERVICE", "dst": "IP", "desc": "应用服务部署在服务器"}, "r_app_server": {"type": "edge", "src": "IP", "dst": "APPSERVICE", "desc": "服务器提供应用服务"}, "ip_belong_to_org": {"type": "edge", "src": "IP", "dst": "ORG", "desc": "IP归属组织"}, "r_ip_belong_to_org": {"type": "edge", "src": "ORG", "dst": "IP", "desc": "组织拥有IP"}, "domain_belong_to_org": {"type": "edge", "src": "DOMAIN", "dst": "ORG", "desc": "域名归属组织"}, "r_domain_belong_to_org": {"type": "edge", "src": "ORG", "dst": "DOMAIN", "desc": "组织拥有域名"}, "fDomain_belong_to_org": {"type": "edge", "src": "FDOMAIN", "dst": "ORG", "desc": "锚域名归属组织"}, "r_fDomain_belong_to_org": {"type": "edge", "src": "ORG", "dst": "FDOMAIN", "desc": "组织拥有锚域名"}, "cert_belong_to_org": {"type": "edge", "src": "CERT", "dst": "ORG", "desc": "证书归属组织"}, "r_cert_belong_to_org": {"type": "edge", "src": "ORG", "dst": "CERT", "desc": "组织拥有证书"}, "ip_url_related": {"type": "edge", "src": "IP", "dst": "URL", "desc": "IP提供URL服务"}, "r_ip_url_related": {"type": "edge", "src": "URL", "dst": "IP", "desc": "URL由IP提供服务"}, "domain_url_related": {"type": "edge", "src": "DOMAIN", "dst": "URL", "desc": "域名包含在URL中"}, "r_domain_url_related": {"type": "edge", "src": "URL", "dst": "DOMAIN", "desc": "URL包含域名"}, "cert_url_related": {"type": "edge", "src": "CERT", "dst": "URL", "desc": "证书保护URL"}, "r_cert_url_related": {"type": "edge", "src": "URL", "dst": "CERT", "desc": "URL受证书保护"}, "subject_related": {"type": "edge", "src": "CERT", "dst": "SUBJECT", "desc": "证书包含主体信息"}, "r_subject_related": {"type": "edge", "src": "SUBJECT", "dst": "CERT", "desc": "主体信息包含在证书中"}, "issuer_related": {"type": "edge", "src": "CERT", "dst": "ISSUER", "desc": "证书包含颁发者信息"}, "r_issuer_related": {"type": "edge", "src": "ISSUER", "dst": "CERT", "desc": "颁发者信息包含在证书中"}, "father_cert": {"type": "edge", "src": "CERT", "dst": "CERT", "desc": "证书的父证书"}, "same_sign_related": {"type": "edge", "src": "CERT", "dst": "CERT", "desc": "同一颁发者的证书"}, "cert_sign_cert": {"type": "edge", "src": "CERT", "dst": "CERT", "desc": "证书签发子证书"}, "r_cert_sign_cert": {"type": "edge", "src": "CERT", "dst": "CERT", "desc": "证书被父证书签发"}, "special_business_port_service": {"type": "edge", "src": "CERT", "dst": "APPSERVICE", "desc": "证书保护应用服务"}, "r_special_business_port_service": {"type": "edge", "src": "APPSERVICE", "dst": "CERT", "desc": "应用服务受证书保护"}, "cert_validate_domain": {"type": "edge", "src": "CERT", "dst": "DOMAIN", "desc": "证书验证域名"}, "r_cert_validate_domain": {"type": "edge", "src": "DOMAIN", "dst": "CERT", "desc": "域名被证书验证"}, "cert_validate_fDomain": {"type": "edge", "src": "CERT", "dst": "FDOMAIN", "desc": "证书验证锚域名"}, "r_cert_validate_fDomain": {"type": "edge", "src": "FDOMAIN", "dst": "CERT", "desc": "锚域名被证书验证"}, "cert_related_ip": {"type": "edge", "src": "CERT", "dst": "IP", "desc": "证书部署在服务器"}, "r_cert_related_ip": {"type": "edge", "src": "IP", "dst": "CERT", "desc": "服务器部署了证书"}, "has_label": {"type": "edge", "src": "CERT/IP/DOMAIN", "dst": "LABEL", "desc": "实体具有标签"}, "r_has_label": {"type": "edge", "src": "LABEL", "dst": "CERT/IP/DOMAIN", "desc": "标签属于实体"}, "make_attack": {"type": "edge", "src": "ATTACKER", "dst": "VICTIM", "desc": "攻击者攻击目标"}, "r_make_attack": {"type": "edge", "src": "VICTIM", "dst": "ATTACKER", "desc": "目标被攻击者攻击"}, "sni_bind_fdomain": {"type": "edge", "src": "DOMAIN", "dst": "FDOMAIN", "desc": "SNI域名绑定锚域名"}, "r_sni_bind_fdomain": {"type": "edge", "src": "FDOMAIN", "dst": "DOMAIN", "desc": "锚域名绑定SNI域名"}, "cert_related_ua": {"type": "edge", "src": "CERT", "dst": "UA", "desc": "证书与UA共同出现"}, "r_cert_related_ua": {"type": "edge", "src": "UA", "dst": "CERT", "desc": "UA与证书共同出现"}}