[{"type": "IP", "argv": "IP", "handle": ["src_bind", "dst_bind", "connect_ip", "client_query_domain", "client_query_dns_server", "dns_server_domain", "client_ssl_connect_domain", "server_ssl_connect_domain", "server_use_cert", "client_use_cert", "client_connect_cert", "server_use_sslfinger", "client_use_sslfinger", "client_http_connect_domain", "server_http_connect_domain", "client_app", "client_use_ua", "ip_belong_to_org", "ip_url_related", "ip_belong_app"], "Rhandle": ["connect_ip", "client_query_dns_server", "parse_to", "cname_result", "app_server", "cert_related_ip"]}, {"type": "MAC", "argv": "MAC", "handle": ["connect_mac"], "Rhandle": ["src_bind", "dst_bind", "connect_mac"]}, {"type": "DOMAIN", "argv": "DOMAIN", "handle": ["parse_to", "cname", "cname_result", "domain_belong_to", "sni_bind", "domain_belong_to_org", "domain_url_related", "domain_belong_app"], "Rhandle": ["client_query_domain", "dns_server_domain", "cname", "client_ssl_connect_domain", "server_ssl_connect_domain", "sslfinger_connect_domain", "ua_connect_domain", "client_http_connect_domain", "server_http_connect_domain", "cert_validate_domain"]}, {"type": "APPSERVICE", "argv": "APPSERVICE", "handle": ["app_server"], "Rhandle": ["client_app", "special_business_port_service"]}, {"type": "ISSUER", "argv": "ISSUER", "handle": [], "Rhandle": ["issuer_related"]}, {"type": "SUBJECT", "argv": "SUBJECT", "handle": [], "Rhandle": ["subject_related"]}, {"type": "SSLFINGER", "argv": "SSLFINGER", "handle": ["sslfinger_connect_domain", "sslfinger_connect_cert", "sslfinger_belong_app"], "Rhandle": ["client_use_sslfinger", "server_use_sslfinger"]}, {"type": "CERT", "argv": "CERT", "handle": ["cert_belong_to_org", "cert_url_related", "special_business_port_service", "issuer_related", "subject_related", "cert_belong_app", "cert_sign_cert", "cert_validate_domain", "cert_validate_fDomain", "cert_related_ip"], "Rhandle": ["server_use_cert", "client_use_cert", "client_connect_cert", "sni_bind", "sslfinger_connect_cert", "cert_sign_cert"]}, {"type": "ORG", "argv": "ORG", "handle": [], "Rhandle": ["ip_belong_to_org", "domain_belong_to_org", "fDomain_belong_to_org", "cert_belong_to_org"]}, {"type": "APP", "argv": "APP", "handle": [], "Rhandle": ["ua_belong_app", "cert_belong_app", "domain_belong_app", "ip_belong_app", "sslfinger_belong_app"]}, {"type": "FDOMAIN", "argv": "FDOMAIN", "handle": ["fDomain_belong_to_org"], "Rhandle": ["domain_belong_to", "cert_validate_fDomain"]}, {"type": "UA", "argv": "UA", "handle": ["ua_connect_domain", "ua_belong_app", "ua_belong_os", "ua_belong_device"], "Rhandle": ["client_use_ua"]}, {"type": "DEVICE", "argv": "DEVICE", "handle": [], "Rhandle": ["ua_belong_device"]}, {"type": "OS", "argv": "OS", "handle": [], "Rhandle": ["ua_belong_os"]}, {"type": "ATTACK", "argv": "ATTACK", "handle": ["make_attack"], "Rhandle": []}, {"type": "VICTIM", "argv": "VICTIM", "handle": [], "Rhandle": ["make_attack"]}, {"type": "URL", "argv": "URL", "handle": [], "Rhandle": ["ip_url_related", "domain_url_related", "cert_url_related"]}]