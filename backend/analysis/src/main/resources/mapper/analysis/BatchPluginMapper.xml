<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.BatchPluginDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.BatchPlugin">
        <id column="id" property="id" />
        <result column="batch_id" property="batchId" />
        <result column="plugin_id" property="pluginId" />
        <result column="should_log_def" property="shouldLogDef" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_id, plugin_id, should_log_def
    </sql>

    <select id="selectByBatchId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from tb_batch_plugin where batch_id = #{batchId}
    </select>

    <update id="updateList">
        update tb_batch_plugin set should_log_def=#{shouldLogDef}
        where batch_id = #{batchId}
        <if test="shouldLogDef == 0">
            <!-- shouldLogDef表示关闭 type==1是会话   2是协议  用in是考虑会话后续可能会加ID-->
            <if test="type==1">
                AND plugin_id in (1120)
            </if>
            <if test="type==2">
                AND plugin_id not in (1120)
            </if>
        </if>
        <if test="shouldLogDef == 1">
            <if test="type==1">
                AND plugin_id in (1120)
            </if>
            <if test="type==2">
                <if test="pluginIds!=null and pluginIds.size()>0">
                    AND plugin_id in
                    <foreach collection="pluginIds" item="pluginId" open="(" separator="," close=")">
                        #{pluginId}
                    </foreach>
                </if>
            </if>
        </if>
    </update>

</mapper>
