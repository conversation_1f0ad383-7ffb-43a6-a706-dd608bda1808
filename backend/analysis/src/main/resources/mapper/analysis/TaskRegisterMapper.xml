<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.DownloadTaskRegisterDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.DownloadTaskRegister">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="path" property="path" />
        <result column="query" property="query" />
        <result column="type" property="type" />
        <result column="download_count" property="downloadCount" />
        <result column="delete_time" property="deleteTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="task_type" property="taskType" />
        <result column="error_msg" property="errorMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, path, query, type, download_count, delete_time, update_time, create_time, task_type,error_msg
    </sql>

    <select id="getNextTask" resultMap="BaseResultMap">
        select * from tb_download_task_register where status = 1 and type = 1
        order by id asc limit 1
    </select>

    <select id="listTaskRegister" resultType="com.geeksec.analysis.entity.DownloadTaskRegister">
        select * from tb_download_task_register where status = 1
        <if test="searchList != null and searchList.size()>0">
            and task_type in
            <foreach collection="searchList" item="searchList" open="(" close=")" separator=",">
                #{searchList}
            </foreach>
        </if>
        order by id desc
    </select>
</mapper>
