<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.geeksec.analysis.dao.AtlasDao">
    <insert id="craeteAtlasRecord">
        insert into tb_atlas_history (atlas_type,atlas_condition) value (#{atlasType}, #{atlasCondition})
    </insert>

    <select id="queryAtlasHistory" resultType="java.util.Map">
        select * from tb_atlas_history
        where 1 = 1
        <if test="atlasType != null">
            and atlas_type = #{atlasType}
        </if>
        order by created_time ${orderBy}
        limit ${limit} offset ${offset}
    </select>

    <select id="countAtlasHistory" resultType="java.lang.Integer">
        select count(1) from tb_atlas_history
        where 1 = 1
        <if test="atlasType != null">
            and atlas_type = #{atlasType}
        </if>
    </select>

    <delete id="deleteAtlasHistory" parameterType="list">
        delete from tb_atlas_history where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteAllAtlasHistory">
        TRUNCATE TABLE tb_atlas_history;
    </delete>
</mapper>
