<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.geeksec.analysis.dao.TaskAnalysisDao">

    <select id="checkFlowId" resultType="java.lang.Integer">
        select id from tb_network_flow
        where state = 1
        and id in
        <foreach collection="nets" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <update id="updateFlow" parameterType="com.geeksec.analysis.entity.TaskAnalysis">
        update tb_task_analysis
        set
        task_state =#{taskState},
        netflow = #{netflow}
        <if test="suspendTimes!=null">
            ,suspend_times = suspend_times+1
            ,last_suspend_time = now()
        </if>
        where task_id = #{taskId}
    </update>

    <select id="getLastTaskId" resultType="java.lang.Integer">
        SELECT
            task_id
        FROM
            tb_task_analysis
        ORDER BY
            task_id DESC
            LIMIT 1
    </select>

    <update id="updateTask" parameterType="com.geeksec.analysis.entity.dto.OfflineTaskDto">
        update tb_task_analysis
        set
        task_name =#{taskName},
        task_remark = #{taskDescription}
        where task_id = #{taskId}
    </update>

    <update id="updateTaskId" parameterType="java.lang.Integer">
        update tb_task_analysis
        set
            task_id = id
        where id = #{id}
    </update>

    <update id="deleteTask" parameterType="com.geeksec.analysis.entity.dto.OfflineTaskDeleteDto">
        update tb_task_analysis
        set
            delete_status = 1
        where task_id = #{taskId}
    </update>

    <select id="pageTask" resultType="com.geeksec.analysis.entity.vo.OfflineTaskPageVo">
        SELECT
        task_id taskId,
        task_name taskName,
        task_remark taskDescription
        FROM
        tb_task_analysis
        WHERE
        delete_status = 0
        and task_type = 2
        <if test="taskName != null and taskName != ''">
            AND (task_name like concat('%', #{taskName}, '%') or task_remark like concat('%', #{taskName}, '%'))
        </if>
        ORDER BY
        task_id DESC
    </select>

    <select id="getTask" resultType="com.geeksec.analysis.entity.vo.OfflineTaskVo">
        SELECT
        t.task_id taskId,
        t.task_name taskName,
        t.task_remark taskDescription,
        DATE_FORMAT( t.create_time, '%Y-%m-%d %H:%i:%s' ) createTime,
        DATE_FORMAT( t.update_time, '%Y-%m-%d %H:%i:%s' ) updateTime,
        SUM(b.batch_bytes) as dataTotal,
        SUM(b.batch_session) as sessionTotal,
        SUM(b.batch_alarm) as alarmTotal,
        SUM(b.importrarnt_target) as highTargetTotal,
        SUM(b.filter_data_total) as filterDataTotal,
        SUM(b.rule_hits_data_total) as ruleHitsDataTotal,
        SUM(b.whitelist_filter_total) as whitelistFilterTotal
        FROM
        tb_task_analysis t
        LEFT JOIN tb_task_batch b ON t.task_id = b.task_id
        WHERE
        t.delete_status = 0
        AND t.task_type = 2
        AND t.task_id = #{taskId}
    </select>

    <select id="getLastTask" resultType="com.geeksec.analysis.entity.vo.OfflineTaskVo">
        SELECT
            t.task_id taskId,
            t.task_name taskName,
            t.task_remark taskDescription,
            DATE_FORMAT( t.create_time, '%Y-%m-%d %H:%i:%s' ) createTime,
            DATE_FORMAT( t.update_time, '%Y-%m-%d %H:%i:%s' ) updateTime,
            SUM(b.batch_bytes) as dataTotal,
            SUM(b.batch_session) as sessionTotal,
            SUM(b.batch_alarm) as alarmTotal,
            SUM(b.importrarnt_target) as highTargetTotal,
            SUM(b.filter_data_total) as filterDataTotal,
            SUM(b.rule_hits_data_total) as ruleHitsDataTotal,
            SUM(b.whitelist_filter_total) as whitelistFilterTotal
        FROM
            tb_task_analysis t
                LEFT JOIN tb_task_batch b ON t.task_id = b.task_id
        WHERE
            t.delete_status = 0
          AND t.task_type = 2
        GROUP BY
            t.task_id,
            t.task_name,
            t.task_remark,
            t.create_time,
            t.update_time
        ORDER BY
            create_time DESC
            LIMIT 1
    </select>

    <select id="getTaskAnalysis" resultType="com.geeksec.analysis.entity.TaskAnalysis">
        SELECT
            *
        FROM
            tb_task_analysis
        WHERE
            task_id = #{taskId}
    </select>


</mapper>
