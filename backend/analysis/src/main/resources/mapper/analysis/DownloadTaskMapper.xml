<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.DownloadTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.DownloadTask">
        <result column="id" property="id"/>
        <result column="path" property="path"/>
        <result column="query" property="query"/>
        <result column="type" property="type"/>
        <result column="session_id" property="sessionId"/>
        <result column="state" property="state"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, path, query, type, session_id, batch_id, state, created_time
    </sql>

    <select id="getPcapPath" resultType="java.lang.String">
        select path from tb_download_task where id=#{taskId} and state = 1 and status = 1;
    </select>

    <select id="listdownloadPcapList" resultType="com.geeksec.analysis.entity.vo.DownloadPcapAdminVo">
        SELECT id,user_id,query,show_query,type,state,created_time,end_time,state,path,status
        from tb_download_task where status = 1
        order by id desc
    </select>

</mapper>
