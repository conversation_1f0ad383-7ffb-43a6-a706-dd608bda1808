<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.FilterConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.FilterConfig">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="ip" property="ip" />
        <result column="filter_json" property="filterJson" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="hash" property="hash" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, ip, filter_json, created_time, updated_time, hash, type
    </sql>

    <select id="getCount" resultType="java.lang.Integer">
        select count(*) from tb_filter_config where task_id = #{taskId}
    </select>

    <select id="getList" parameterType="com.geeksec.analysis.entity.condition.FilterRuleCondition"
            resultType="com.geeksec.analysis.entity.vo.FilterConfigVo">
        select <include refid="Base_Column_List"></include>
        from tb_filter_config
        where task_id = #{taskId} AND status = 1
        <if test="orderField !=null and orderField!=''">
            <choose>
                <when test="orderField=='ip'">
                    order by inet_aton(ip)
                    ${sortOrder}
                </when>
                <otherwise>
                    order by ${orderField} ${sortOrder}
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="getOneByHash" resultType="com.geeksec.analysis.entity.FilterConfig" parameterType="com.geeksec.analysis.entity.condition.FilterConfigInCondition">
        select <include refid="Base_Column_List"></include>
        from tb_filter_config
        <where>
            AND status = 1
            <if test="id!=null">
                AND id != #{id}
            </if>
            <if test="taskId!=null">
                AND task_id = #{taskId}
            </if>
            <if test="hash!=null and hash !=''">
                AND hash = #{hash}
            </if>
        </where>
        limit 1
    </select>

    <update id="deleteBySome" parameterType="com.geeksec.analysis.entity.condition.FilterDeleteCondition">
        update tb_filter_config set status = 0
        where
        task_id = #{taskId}
        <if test="ids != null and ids.size()>0">
            AND id in
            <foreach collection="ids" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="addByList">
        insert into tb_filter_config (task_id,ip,filter_json,created_time,updated_time,hash,`type`) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.taskId},#{item.ip},#{item.filterJson},#{item.createdTime},#{item.updatedTime},#{item.hash},#{item.type})
        </foreach>
    </insert>
</mapper>
