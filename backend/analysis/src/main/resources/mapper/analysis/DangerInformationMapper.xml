<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.DangerInformationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.DangerInformation">
        <id column="id" property="id" />
        <result column="information" property="information" />
        <result column="information_type" property="informationType" />
        <result column="source" property="source" />
        <result column="danger_label" property="dangerLabel" />
        <result column="time" property="time" />
        <result column="created_time" property="createdTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, information, information_type, source, danger_label, time, created_time
    </sql>
    <insert id="insertBatch">
        insert into tb_danger_information(information,information_type,source,danger_label,`time`,created_time) values
        <foreach collection="list" item="DangerInformation" separator=",">
            (#{DangerInformation.information}, #{DangerInformation.informationType}, #{DangerInformation.source}, #{DangerInformation.dangerLabel}, #{DangerInformation.time}, #{DangerInformation.createdTime})
        </foreach>
    </insert>

</mapper>
