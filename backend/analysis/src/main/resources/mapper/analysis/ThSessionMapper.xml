<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.geeksec.analysis.dao.ThSessionDao">
    <update id="deletePcapDownloadRecordAndData">
        update tb_download_task
        set status=0,
            state=4
        where id = #{taskId}
    </update>

    <delete id="deletePcapDownloadRecord">
        update tb_download_task
        set status=0
        where id = #{taskId}
    </delete>

    <select id="listTask" resultType="com.geeksec.analysis.entity.TbTaskAnalysis">
        select * from tb_task_analysis where task_id in
        <foreach collection="list" item="taskIdList" open="(" close=")" separator=",">
            #{taskIdList}
        </foreach>
    </select>

    <select id="listIpPosition" resultType="com.geeksec.analysis.entity.TbUseIpPosition">
        select * from tb_use_ip_position where ip in
        <foreach collection="list" item="ipList" open="(" close=")" separator=",">
            #{ipList}
        </foreach>
    </select>

    <select id="listTagInfo" resultType="com.geeksec.analysis.entity.TbTagInfo">
        select * from tb_tag_info where tag_type != 0 and tag_id in
        <foreach collection="list" item="tagList" open="(" close=")" separator=",">
            #{tagList}
        </foreach>
    </select>

    <select id="listTagAttribute" resultType="com.geeksec.analysis.entity.vo.TagLibraryVo">
        select DISTINCT tti.tag_id,
                        tti.tag_text,
                        tti.tag_target_type,
                        tti.tag_explain,
                        tti.black_list,
                        tti.white_list,
                        ifnull(tta.attribute_name, '') as attributeName
        from tb_tag_info tti
                 left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
                 left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        where tti.tag_type = 1;
    </select>

    <select id="listAllTagAttribute" resultType="com.geeksec.analysis.entity.vo.TagLibraryVo">
        select DISTINCT tti.tag_id,
                        tti.tag_text,
                        tti.tag_target_type,
                        tti.tag_explain,
                        tti.black_list,
                        tti.white_list,
                        ifnull(tta.attribute_name, '') as attributeName
        from tb_tag_info tti
                 left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
                 left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        where tti.tag_type = 1;
    </select>

    <select id="listTaskAll" resultType="com.geeksec.analysis.entity.TbTaskAnalysis">
        SELECT
            *
        FROM
            tb_task_analysis
        WHERE
            delete_status = 0
          AND task_id NOT IN ( 0, 1 )
        UNION ALL
        SELECT
            *
        FROM
            tb_task_analysis
        WHERE
            delete_status = 0
          AND task_id IN ( 0, 1 )
        order by task_id asc
    </select>

    <select id="listProtocol" resultType="com.geeksec.analysis.entity.vo.ProtocolVo">
        select pro_id, pro_name, pro_value
        from app_pro_value
        where type = #{type};
    </select>

    <select id="getEsField" resultType="java.lang.String">
        select es_field
        from tb_es_field;
    </select>
    <select id="getTaskRecord" resultType="com.geeksec.analysis.entity.DownloadTask">
        select *
        from tb_download_task
        where id = #{taskId}
    </select>


</mapper>
