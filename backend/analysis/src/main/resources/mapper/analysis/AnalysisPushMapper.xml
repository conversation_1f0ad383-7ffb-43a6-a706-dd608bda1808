<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.geeksec.analysis.dao.AnalysisPushDao">
    <select id="selectAlarm" resultType="java.util.HashMap">
        select * from tb_alarm;
    </select>

    <select id="selectNetWorkConfigList" resultType="java.util.HashMap">
        select
        distinct ifnull(device_name, '/') as device_name, ifnull(state, 0) as state, mac
        from tb_network_config
    </select>

    <select id="selectNetWorkConfigByMac" resultType="java.util.HashMap">
        select distinct ifnull(device_name, '/') as device_name, ifnull(state, 0) as state, mac
        from tb_network_config
        where mac = #{mac}
        limit 0,1;
    </select>

    <select id="selectMacInfoList" resultType="java.util.HashMap">
        select
        organization_name , assignment
        from tb_mac_info_value
    </select>

    <select id="getInternalNet" resultType="com.geeksec.analysis.entity.vo.InternalNetVo">
        select
        * from tb_internal_net
        where task_id = ${taskId}
        order by
        <choose>
            <when test="orderField == 'inter_ip' and orderField != null ">
                inet_aton(inter_ip) ${order}
            </when>
            <when test="orderField == 'ip_mask' and orderField != null">
                inet_aton(ip_mask) ${order}
            </when>
            <when test="orderField == 'mac' and orderField != null">
                mac ${order}
            </when>
            <otherwise>
                ${orderField} ${order}
            </otherwise>
        </choose>
        limit ${limit} offset ${offset}
    </select>

    <select id="getInternalNetCount" resultType="integer">
        select count(*) from tb_internal_net where task_id = ${taskId};
    </select>

    <select id="existInternalIp" resultType="java.lang.Integer">
        select count(*) from tb_internal_net where inter_ip = #{ip}
    </select>

    <insert id="addInternalInfo">
        insert into tb_internal_net (task_id,inter_ip,ip_mask,mac,created_time,last_modified_time)
        values (#{condition.taskId},#{condition.interIp},#{condition.ipMask},#{condition.mac},#{condition.createdTime},#{condition.createdTime})
    </insert>

    <delete id="batchDeleteInterInfo">
        delete from tb_internal_net
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            ${item}
        </foreach>
    </delete>

    <update id="updateInterInfo">
        update tb_internal_net
        <trim prefix="set" suffixOverrides=",">
            <if test="condition.interIp != null">
                inter_ip = #{condition.interIp},
            </if>
            <if test="condition.ipMask != null">
                ip_mask = #{condition.ipMask},
            </if>
            <if test="condition.mac != null">
                mac = #{condition.mac}
            </if>
        </trim>
        ,last_modified_time = #{condition.lastModifiedTime}
        where id = ${condition.id}
    </update>

</mapper>
