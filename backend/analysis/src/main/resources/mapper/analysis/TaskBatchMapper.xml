<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.TaskBatchDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.TaskBatch">
        <id column="batch_id" property="batchId" />
        <result column="task_id" property="taskId" />
        <result column="batch_remark" property="batchRemark" />
        <result column="fullflow_state" property="fullflowState" />
        <result column="flowlog_state" property="flowlogState" />
        <result column="data_type" property="dataType" />
        <result column="topology_state" property="topologyState" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="data_begin_time" property="dataBeginTime" />
        <result column="data_end_time" property="dataEndTime" />
        <result column="batch_bytes" property="batchBytes" />
        <result column="batch_session" property="batchSession" />
        <result column="batch_alarm" property="batchAlarm" />
        <result column="importrarnt_target" property="importrarntTarget" />
        <result column="batch_dir" property="batchDir" />
        <result column="report_path" property="reportPath" />
        <result column="screening_conditions" property="screeningConditions" />
        <result column="avg_byte_pt_ps" property="avgBytePtPs" />
        <result column="max_byte_pt_ps" property="maxBytePtPs" />
        <result column="addr" property="addr" />
        <result column="task_update" property="taskUpdate" />
        <result column="full_flow_should_log_def" property="fullFlowShouldLogDef" />
        <result column="parse_proto_should_log_def" property="parseProtoShouldLogDef" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        batch_id, task_id, batch_remark, fullflow_state, flowlog_state, data_type, topology_state, begin_time, end_time, data_begin_time, data_end_time, batch_bytes, batch_session, batch_alarm, importrarnt_target, batch_dir, report_path, screening_conditions, avg_byte_pt_ps, max_byte_pt_ps, addr, task_update, full_flow_should_log_def, parse_proto_should_log_def, state
    </sql>

    <select id="getTaskBatchInfoByTaskId" resultMap="BaseResultMap">
        select *
        from tb_task_batch
        where task_id = ${taskId}
    </select>

    <select id="getTaskDDosState" resultType="java.util.HashMap">
        select * from tb_ddos_state
        where task_id = ${taskId}
        limit 0,1
    </select>

    <select id="getTaskAnalysis" resultType="com.geeksec.analysis.entity.TaskAnalysis">
        select * from tb_task_analysis
        where task_id = ${taskId}
    </select>

    <update id="updateTaskDDosState">
        update tb_ddos_state set state=#{state}
        where task_id=#{taskId}
    </update>

    <select id="listTaskBatchItem" resultType="com.geeksec.analysis.entity.TaskBatch">
        SELECT
        task_id taskId,
        batch_id batchId,
        end_time endTime,
        batch_status batchStatus
        FROM
        tb_task_batch
        WHERE
        1 = 1
        <choose>
            <when test="taskIdList != null and taskIdList.size()>0 ">
                AND task_id IN
                <foreach item="taskId" index="index" collection="taskIdList" open="(" separator=","
                         close=")">
                    #{taskId}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <select id="listBatchByTaskId" resultType="com.geeksec.analysis.entity.vo.OfflineTaskBatchProgressVo">
        SELECT
        batch_id batchId,
        batch_status batchStatus,
        batch_progress batchProgress,
        end_time endTime
        FROM
        tb_task_batch
        where
        1 = 1
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        ORDER BY
        batch_id ASC
    </select>

    <select id="pageBatch" resultType="com.geeksec.analysis.entity.vo.OfflineTaskBatchPageVo">
        SELECT
            tb.batch_id batchId,
            FROM_UNIXTIME(tb.begin_time, '%Y-%m-%d %H:%i:%s') startTime,
            FROM_UNIXTIME(tb.end_time, '%Y-%m-%d %H:%i:%s') endTime,
            tb.batch_status batchStatus,
            tb.batch_type batchType,
            ( SELECT GROUP_CONCAT( local_path ORDER BY local_path ASC SEPARATOR ',' ) FROM tb_offline_task_batch_file WHERE tb_offline_task_batch_file.batch_id = tb.batch_id ) filePath,
            tb.pcap_num pcapNum
        FROM
            tb_task_batch tb
        WHERE
            task_id = #{taskId}
        ORDER BY
            tb.batch_id DESC
    </select>

    <select id="countIncompleteTask" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            tb_task_batch
        WHERE
            task_id = #{taskId}
          AND batch_status IN (1,2)
    </select>

    <update id="cancelBatch">
        update tb_task_batch set batch_status= 4
        where batch_id=#{batchId}
    </update>

    <select id="getServiceNameByBatchId" resultType="java.lang.String">
        SELECT
            service_name
        FROM
            tb_batch_offline_thd
        WHERE
            batch_id = #{batchId}
            LIMIT 1
    </select>

</mapper>
