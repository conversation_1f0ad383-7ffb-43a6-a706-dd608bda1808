<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.ModelInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.TbModelInfo">
        <id column="model_id" property="modelId"/>
        <result column="model_name" property="modelName"/>
        <result column="model_algorithm" property="modelAlgorithm"/>
        <result column="remark" property="remark"/>
        <result column="state" property="state"/>
        <result column="update_time" property="updateTime"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        model_id, model_name, model_algorithm, remark, state, update_time, created_time
    </sql>

    <select id="getList" resultMap="BaseResultMap">
        select * from tb_model_info
        where 1=1
        <if test="condition.modelName != null and condition.modelName != ''">
            and model_name like CONCAT('%',#{condition.modelName},'%')
        </if>
        order by
        <choose>
            <when test="condition.getOrderFiled() == 'model_id' and condition.getOrderFiled() != null">
                model_id ${condition.sortOrder}
            </when>
            <when test="condition.getOrderFiled() == 'model_name' and condition.getOrderFiled() != null">
                CONVERT(model_name using gbk) ${condition.sortOrder}
            </when>
            <when test="condition.getOrderFiled() == 'model_algorithm' and condition.getOrderFiled() != null">
                CONVERT(model_algorithm using gbk) ${condition.sortOrder}
            </when>
            <otherwise>
                update_time ${condition.sortOrder}
            </otherwise>
        </choose>
        limit ${limit} offset ${offset}
    </select>

    <update id="updateModelState">
        update tb_model_info
        set state = ${state}
        where model_id = ${modelId};
    </update>

</mapper>
