package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 离线任务列表
 */
@Data
public class OfflineTaskPageVo {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 任务名
     */
    @JsonProperty("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @JsonProperty("task_description")
    private String taskDescription;

    /**
     * 任务状态（1-执行中；2-空闲；）
     */
    @JsonProperty("task_status")
    private Integer taskStatus;

    /**
     * 最近导入完成时间
     */
    @JsonProperty("last_import_time")
    private String lastImportTime;
}
