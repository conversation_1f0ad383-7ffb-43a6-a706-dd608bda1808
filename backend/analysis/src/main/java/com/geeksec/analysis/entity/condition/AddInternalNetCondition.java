package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description：内网IP添加condition
 */
@Data
public class AddInternalNetCondition {

    /**
     * IP地址(支持IPv4和IPv6)
     */
    @JsonProperty("inter_ip")
    private String interIp;

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    /**
     * IP掩码
     */
    @JsonProperty("ip_mask")
    private String ipMask;

    /**
     * MAC地址（自动关联）
     */
    @JsonProperty("mac")
    private String mac;

    /**
     * 添加时间
     */
    @JsonProperty("created_time")
    private Date createdTime;
}
