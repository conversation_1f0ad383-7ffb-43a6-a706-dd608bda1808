package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class BlockChainVo {

    /**
     * 区块链地址
     */
    @JsonProperty(value = "addr")
    private String addr;

    /**
     * 来源
     */
    @JsonProperty(value = "chain_source")
    private String chainSource;

    /**
     * 余额
     */
    @JsonProperty(value = "balance_account")
    private Long balanceAccount;

    /**
     * 黑名单权重
     */
    @JsonProperty(value = "black_list")
    private Integer blackList;

    /**
     * 白名单权重
     */
    @JsonProperty(value = "white_list")
    private Integer whiteList;

    /**
     * 标签集合
     */
    private List<Integer> labels;
}
