package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.NbLabelUpCondition;
import com.geeksec.analysis.entity.condition.NbRemarkUpCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.entity.common.ResultVo;

import java.io.UnsupportedEncodingException;

/**
 * 分析平台的会话分析
 */
public interface Metadata2Service {

    /**
     * 侧啦：IP详情
     * @param ip
     * @return
     */
    ResultVo getIpInfo(String ip) throws UnsupportedEncodingException;

    /**
     * 证书详情
     * @param cert
     * @return
     */
    ResultVo<CertInfoVo> getCertInfo(String cert);

    /**
     * 证书详情 四合一
     */
    ResultVo getCertDetail(String certId);

    /**
     * 域名详情
     * @param domain
     * @return
     */
    ResultVo getDomainInfo(String domain) throws UnsupportedEncodingException;

    /**
     * 编辑标签（证书的标签修改含有ES修改的部分）
     * @param condition
     * @return
     */
    ResultVo updateLabels(NbLabelUpCondition condition);

    /**
     * 编辑备注
     * @param condition
     * @return
     */
    ResultVo updateRemark(NbRemarkUpCondition condition);

//    /**
//     * 获取告警研判绘图
//     * @param id
//     * @return
//     */
//    ResultVo getAlarmGraph(String id);

    ResultVo getRedisTest(String key,String value);

    /**
     * 获取企业信息
     * @param str
     * @return
     */
    ResultVo<OrgInfoVo> getOrgInfo(String str);

    /**
     * 获取指纹信息
     * @param str
     * @return
     */
    ResultVo<SSLFingerInfoVo> getSSLFingerInfo(String str);

    /**
     * 获取应用服务详情
     * @param str
     * @return
     */
    ResultVo<AppServiceInfoVo> getAppService(String str);

    /**
     * 获取服务详情
     * @param str
     * @return
     */
    ResultVo<AppInfoVo> getApp(String str);

}
