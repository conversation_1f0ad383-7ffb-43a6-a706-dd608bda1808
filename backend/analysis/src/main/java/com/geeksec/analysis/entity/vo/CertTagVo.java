package com.geeksec.analysis.entity.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: jerryzhou
 * @date: 2024/1/17 11:29
 * @Description: 证书标签展示类
 **/
@Data
public class CertTagVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @JsonProperty("tag_id")
    private Integer tagId;

    /**
     * 标签含义
     */
    @JsonProperty("tag_text")
    private String tagText;

    /**
     * 黑名单权值
     */
    @JsonProperty("black_list")
    private Integer blackList;

    /**
     * 白名单权值
     */
    @JsonProperty("white_list")
    private Integer whiteList;

    /**
     * 证书描述（中文）
     */
    @JsonProperty("tag_remark")
    private String tagRemark;

    /**
     * 标签等级( danger warning success info)
     */
    @JsonProperty("tag_level")
    private String tagLevel;
}
