package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
* 文件路径树Vo
*/
@Data
public class FileTreeNodeVo {

    /**
    * 文件路径
    */
    @JsonProperty("file_path")
    private String filePath;

    /**
    * 文件名
    */
    @JsonProperty("file_name")
    private String fileName;

    /**
    * 文件类型（false-文件；true-文件夹）
    */
    @JsonProperty("disabled")
    private Boolean disabled;


}
