package com.geeksec.analysis.entity.storage;

import lombok.Data;

import java.util.Set;

@Data
public class IPListVo {

    private String ip;

    // 如果是dIp  那么对应es的dPayloadBytes字段
    // 对前端而言  s就是发送   下面的d字段同理
    /**
     * 当前发送字节数
     */
    private long sPayloadBytes = 0L;

    /**
     * 当前接收字节数
     */
    private long dPayloadBytes = 0L;

    /**
     * 会话数量
     */
    private long sessionCount;

    /**
     * 首次发现时间
     */
    private Long startTimeMin = 0L;

    /**
     * 末次发现时间
     */
    private Long startTimeMax = 0L;

    /**
     * 关联的告警个数
     */
    private long alarmCount = 0L;

    /**
     * 开放服务  应用服务--IP---->服务归属)
     */
    private long openServiceNum;

    /**
     * 全局访问服务  IP-应用服务-->访问服务)
     */
    private long accessServiceNum;

    /**
     * 威胁权重
     */
    private Integer blackList;

    /**
     * 地理位置：city
     */
    private String city;

    /**
     * 地理位置：country
     */
    private String country;

    /**
     * 关联域名
     */
    private int domainNum;

    /**
     * 关联证书
     */
    private int certNum;

    /**
     * 标签列表
     */
    private Set<Long> labels;
}
