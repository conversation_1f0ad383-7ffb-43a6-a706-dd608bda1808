package com.geeksec.analysis.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_rule")
public class RuleEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID ---规则表---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */
    @TableField("rule_id")
    private Integer ruleId;

    /**
     * 规则级别
     */
    @TableField("rule_level")
    private Integer ruleLevel;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 规则描述
     */
    @TableField("rule_desc")
    private String ruleDesc;

    /**
     * 规则状态
     */
    @TableField("rule_state")
    private String ruleState;

    /**
     * 数据量
     */
    @TableField("rule_size")
    private Long ruleSize;

    /**
     * 命中数据总量
     */
    @TableField("total_sum_bytes")
    private Long totalSumBytes;

    /**
     * 最新命中时间
     */
    @TableField("last_size_time")
    private Integer lastSizeTime;

    /**
     * 采集模式
     */
    @TableField("capture_mode")
    private Integer captureMode;

    /**
     * json字符串
     */
    @TableField("rule_json")
    private String ruleJson;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Integer createdTime;

    /**
     * 修改时间
     */
    @TableField("updated_time")
    private Integer updatedTime;

    /**
     * 规则hash
     */
    @TableField("rule_hash")
    private String ruleHash;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    @TableField("rule_family")
    private Integer ruleFamily;

    /**
     * 每秒限速
     */
    @TableField("byte_ps")
    private Long bytePs;

    /**
     * 存储大小，负数不限
     */
    @TableField("save_bytes")
    private Long saveBytes;

    /**
     * pb留存
     */
    @TableField("pb_drop")
    private Integer pbDrop;

    /**
     * pcap留存
     */
    @TableField("pcap_drop")
    private Integer pcapDrop;

    /**
     * 开启动态库响应
     */
    @TableField("lib_respond_open")
    private Integer libRespondOpen;

    /**
     * 库路径
     */
    @TableField("lib_respond_lib")
    private String libRespondLib;

    /**
     * 库配置路径
     */
    @TableField("lib_respond_config")
    private String libRespondConfig;

    /**
     * 末尾响应
     */
    @TableField("lib_respond_session_end")
    private Long libRespondSessionEnd;

    /**
     * 包数
     */
    @TableField("lib_respond_pkt_num")
    private Long libRespondPktNum;

}
