package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: GuanHao
 * @Date: 2022/5/11 10:07
 * @Description： <Functions List>
 */
@Data
@TableName("tb_use_ip_position")
public class TbUseIpPosition {

    @TableField("task_id")
    private Integer taskId;

    @TableField("ip")
    private String ip;

    @TableField("net_mesh")
    private String netMesh;

    @TableField("country")
    private String country;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("remark")
    private String remark;

    @TableField("created_time")
    private Long createdTime;

    @TableField("updated_time")
    private Long updatedTime;
}
