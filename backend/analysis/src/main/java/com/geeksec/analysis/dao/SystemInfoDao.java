package com.geeksec.analysis.dao;

import com.geeksec.analysis.entity.vo.ProductInfoVo;
import com.geeksec.analysis.entity.vo.SystemInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/27 10:50
 * @Description： <Functions List>
 */
public interface SystemInfoDao {

    /**
     * 获取系统信息
     *
     * @return
     */
    SystemInfoVo getSystemInfo();

    /**
     * 获取产品信息
     *
     * @return
     */
    ProductInfoVo getProductInfo();

    /**
     * 修改机器关机/重启操作值
     */
    void modifyShutdownValue(@Param("value") Integer value);
}
