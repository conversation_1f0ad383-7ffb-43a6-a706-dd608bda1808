package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：应用展示实体VO
 */
@Data
public class AppInfoVo {
    /**
     * 应用名称
     */
    @JsonProperty(value = "app_name")
    private String appName;

    /**
     * 应用版本
     */
    @JsonProperty(value = "app_version")
    private String appVersion;

    /**
     * 标签集合
     */
    private List<Integer> labels;

}
