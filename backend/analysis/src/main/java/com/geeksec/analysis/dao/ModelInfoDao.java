package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.condition.ModelCondition;
import com.geeksec.analysis.entity.TbModelInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface ModelInfoDao extends BaseMapper<TbModelInfo> {

    /**
     * 通过条件查询模型列表
     * @param condition
     * @param limit
     * @param offset
     * @return
     */
    List<TbModelInfo> getList(@Param("condition") ModelCondition condition, @Param("limit") Integer limit, @Param("offset") Integer offset);

    /**
     * 修改模型开关状态
     * @return
     * @param modelId
     * @param state
     */
    int updateModelState(@Param("modelId") Integer modelId,@Param("state") Integer state);
}
