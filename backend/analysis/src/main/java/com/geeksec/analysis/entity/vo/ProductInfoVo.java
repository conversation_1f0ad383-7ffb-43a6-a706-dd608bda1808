package com.geeksec.analysis.entity.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/27 10:20
 * @Description： <Functions List>
 */
@Data
public class ProductInfoVo {

    /**
     * 产品
     */
    @JsonProperty("product")
    private String product;

    /**
     * 版本
     */
    @JsonProperty("version")
    private String version;

    /**
     * SN码
     */
    @JsonProperty("SN")
    private String SN;

    /**
     * 产品到期时间
     */
    @JsonProperty("privileged_time")
    private String privilegedTime;
}
