package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_download_task_register")
public class DownloadTaskRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 日志保存路径
     */
    @TableField("path")
    private String path;

    /**
     * 查询条件
     */
    @TableField("query")
    private String query;

    /**
     * 日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误
     */
    @TableField("type")
    private Integer type;

    /**
     * 下载次数
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 任务类型：1 日志下载
     */
    @TableField("task_type")
    private Integer taskType;

    @TableField("status")
    private Integer status;

    /**
     * 任务失败的原因
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 修改时间修改构造器
     *
     * @param id id
     */
    public DownloadTaskRegister(Integer id, Long updateTime) {
        this.id = id;
        this.updateTime = updateTime;
    }

    /**
     * 状态修改构造器
     *
     * @param id id
     */
    public DownloadTaskRegister(Integer id, Integer type) {
        this.id = id;
        this.type = type;
    }

    public DownloadTaskRegister() {
    }
}
