package com.geeksec.analysis.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class ModelCondition extends BaseCondition {

    /**
     * 模型名称
     */
    @JsonProperty("model_name")
    private String modelName;

    /**
     * 排序字段
     */
    @JsonProperty("order_filed")
    private String orderFiled;
}
