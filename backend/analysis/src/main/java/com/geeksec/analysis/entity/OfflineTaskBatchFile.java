package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
*@description: 离线任务批次关联文件
*@author: shiwenxu
*@createtime: 2024/3/11 14:25
**/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_offline_task_batch_file")
public class OfflineTaskBatchFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 批次类型（1-服务器数据；2-数据上传；）
     */
    private Integer batchType;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件本地路径
     */
    private String localPath;

}
