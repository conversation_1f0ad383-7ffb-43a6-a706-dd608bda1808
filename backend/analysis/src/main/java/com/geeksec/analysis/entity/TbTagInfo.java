package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: GuanHao
 * @Date: 2022/5/11 10:10
 * @Description： <Functions List>
 */
@Data
@TableName("tb_tag_info")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TbTagInfo {

    @TableId(value = "tag_id", type = IdType.AUTO)
    @TableField("tag_id")
    private Integer tagId;

    @TableField("tag_type")
    private Integer tagType;

    @TableField("tag_remark")
    private String tagRemark;

    @TableField("tag_explain")
    private String tagExplain;

    @TableField("tag_attr")
    private Integer tagAttr;

    @TableField("tag_text")
    private String tagText;

    @TableField("tag_num")
    private Integer tagNum;

    @TableField("tag_target_type")
    private Integer tagTargetType;

    @TableField("default_black_list")
    private Integer defaultBlackList;

    @TableField("default_white_list")
    private Integer defaultWhiteList;

    @TableField("black_list")
    private Integer blackList;

    @TableField("white_list")
    private Integer whiteList;

    @TableField("created_time")
    private Long createdTime;

    @TableField("last_created_time")
    private Long lastCreatedTime;

    @TableField("tag_family")
    private Integer tagFamily;

    @TableField("tag_class")
    private Integer tagClass;

    @TableField("user_id")
    private Integer userId;

}
