package com.geeksec.analysis.entity.condition;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AlarmCommonCondition {

    @JsonProperty("alarm_ids")
    /** 告警名称 */
    private List<Integer> alarmIds;

    private Long left;

    private Long right;

    @JsonProperty("task_ids")
    private List<Integer> taskIds;

    /**
     * 告警对象 名字s
     */
    @JsonProperty("target_name")
    private String targetName;

    /**
     * 受害方  5个字段都要查
     */
    private String victim;

    @JsonProperty("attack_levels")
    //低危（威胁权重范围[61,80]）
    //中危（威胁权重范围[81,90]）
    //高危（威胁权重范围[91,100]）
    private List<String> attackLevels;

    /**
     * 攻击方  只查ip
     */
    @JsonProperty("attacker_ip")
    private String attackerIp;

    @JsonProperty("alarm_status_list")
    private List<Integer> alarmStatusList;


    /**
     * 是否升序
     */
    @JsonProperty("asc")
    private Boolean asc = false;

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    @ApiModelProperty("当前页")
    private Integer currentPage = 1;

    /**
     * 当前页展示数量
     */
    @JsonProperty("page_size")
    @ApiModelProperty("当前页展示数量")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @JsonProperty("order_field")
    private String orderField;


    //csv下载可能勾选
    private List<String> ids;
}
