package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.OfflineTaskQueryCondition;
import com.geeksec.analysis.entity.dto.OfflineTaskDeleteDto;
import com.geeksec.analysis.entity.dto.OfflineTaskDto;
import com.geeksec.analysis.entity.vo.OfflineTaskPageVo;
import com.geeksec.analysis.entity.vo.OfflineTaskVo;
import com.geeksec.analysis.entity.vo.PageVo;
import com.geeksec.entity.common.ResultVo;

/**
* 离线任务服务
*/
public interface OfflineTaskService {

    /**
     * 查询当前任务
     */
    OfflineTaskVo getLastTask();

    /**
     * 任务详情
     */
    OfflineTaskVo getTask(Integer taskId);

    /**
     * 任务列表
     */
    PageVo<OfflineTaskPageVo> pageTask(OfflineTaskQueryCondition condition);

    /**
    * 创建任务
    */
    ResultVo addTask(OfflineTaskDto dto);

    /**
     * 任务配置
     */
    ResultVo updateTask(OfflineTaskDto dto);

    /**
     * 删除任务
     */
    ResultVo deleteTask(OfflineTaskDeleteDto dto);

}
