package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
* 标签分类Vo
*/
@Data
public class TagAttributeVo {

    /**
     * 分类等级
     */
    @JsonProperty("attribute_level")
    private Integer attributeLevel;

    /**
    * 分类id
    */
    @JsonProperty("attribute_id")
    private Integer attributeId;

    /**
    * 分类名称
    */
    @JsonProperty("attribute_name")
    private String attributeName;

    /**
    * 下级分类
    */
    @JsonProperty("children")
    private List<TagAttributeVo> tagAttributeVoList;

}
