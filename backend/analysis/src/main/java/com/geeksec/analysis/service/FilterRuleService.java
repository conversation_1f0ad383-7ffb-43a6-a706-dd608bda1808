package com.geeksec.analysis.service;

import cn.hutool.core.text.csv.CsvRow;
import com.geeksec.analysis.entity.condition.FilterConfigInCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.condition.FilterRuleCondition;
import com.geeksec.analysis.entity.vo.FilterConfigVo;
import com.geeksec.analysis.entity.vo.FilterCsvVo;
import com.geeksec.analysis.entity.vo.FilterStateVo;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;

public interface FilterRuleService {

    /**
     * 过滤规则：命中留存/命中丢弃
     * @param condition
     * @return
     */
    Integer modifyFilterSate(FilterRuleCondition condition);

    /**
     * 获取任务过滤的命中留存/丢弃状态
     * @param taskId
     * @return
     */
    FilterStateVo getFilterStateByTaskId(Integer taskId);

    /**
     * 获取过滤规则列表
     * @param condition`
     * @return
     */
    PageResultVo<FilterConfigVo> getFilterConfigList(FilterRuleCondition condition);

    /**
     * 新增过滤规则（new）
     * @param filterConfigInCondition
     */
    void addConfig(FilterConfigInCondition filterConfigInCondition);

    /**
     * 更新过滤规则
     * @param filterConfigInCondition
     * @return
     */
    ResultVo updateConfig(FilterConfigInCondition filterConfigInCondition);

    /**
     * 删除过滤规则
     * @param condition
     * @return
     */
    ResultVo deleteConfig(FilterDeleteCondition condition);

    ResultVo addConfig(Integer taskId, List<CsvRow> rows);

    List<List<String>> getListByTaskId(Integer taskId);

    List<List<String>> getCsv(Integer taskId);

    ResultVo<FilterCsvVo> addConfigByCSV(Integer taskId, List<CsvRow> rows);
}
