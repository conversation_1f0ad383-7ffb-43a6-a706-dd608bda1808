package com.geeksec.analysis.entity.session;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class SessionTagEntity {

    /**
     * 标签ID
     */
    @JsonProperty("tag_id")
    private Integer tagId;

    /**
     * 标签名称
     */
    @JsonProperty("tag_text")
    private String tagText;

    /**
     * 黑名单权值
     */
    @JsonProperty("black_list")
    private Integer blackList;

    /**
     * 白名单权值
     */
    @JsonProperty("white_list")
    private Integer whiteList;

    /**
     * 标签等级
     */
    @JsonProperty("tag_level")
    private String tagLevel;

}
