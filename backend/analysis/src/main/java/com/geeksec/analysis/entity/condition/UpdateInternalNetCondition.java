package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class UpdateInternalNetCondition {
    /**
     * 主键ID
     */
    @JsonProperty(value = "id")
    private Integer id;

    /**
     * 内网IP
     */
    @JsonProperty(value = "inter_ip")
    private String interIp;

    /**
     * 任务ID
     */
    @JsonProperty(value = "task_id")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty(value ="batch_id")
    private Integer batchId;

    /**
     * 子网掩码
     */
    @JsonProperty(value = "ip_mask")
    private String ipMask;

    /**
     * mac地址
     */
    @JsonProperty(value = "mac")
    private String mac;

    /**
     * 最后修改时间
     */
    @JsonProperty(value = "last_modified_time")
    private Date lastModifiedTime;
}
