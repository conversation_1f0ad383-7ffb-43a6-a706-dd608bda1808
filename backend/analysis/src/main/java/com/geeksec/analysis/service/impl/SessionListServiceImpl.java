package com.geeksec.analysis.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.DownloadTaskDao;
import com.geeksec.analysis.dao.FeatureRuleDao;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.dao.ThSessionDao;
import com.geeksec.analysis.entity.*;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.DownloadListSearchCondition;
import com.geeksec.analysis.entity.condition.DownloadPcapCondition;
import com.geeksec.analysis.entity.vo.*;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.QueryHistoryService;
import com.geeksec.analysis.service.SessionListService;
import com.geeksec.analysis.service.TagService;
import com.geeksec.analysis.utils.ESQueryUtil;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.InstanceofUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.CollectionUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Author: GuanHao
 * @Date: 2022/5/5 14:55
 * @Description： <Functions List>
 */
@DS("nta-db")
@Service
public class SessionListServiceImpl implements SessionListService {
    private static final Logger LOG = LoggerFactory.getLogger(SessionListServiceImpl.class);

    private static final Map<Integer, Object> ATTRIBUTE_TAG_MAP = new HashMap<Integer, Object>() {{
        put(1, "威胁");
        put(2, "功能描述");
        put(3, "合法性");
        put(4, "行为描述");
        put(5, "APT");
        put(6, "远程控制");
        put(7, "基础属性");
        put(8, "指纹描述");
        put(9, "代理");
        put(10, "加密流量检测");
    }};

    @Autowired
    EsearchService esearchService;

    @Autowired
    FeatureRuleDao featureRuleDao;

    @Autowired
    ThSessionDao thSessionDao;

    @Autowired(required = false)
    DownloadTaskDao downloadTaskDao;

    @Autowired
    private TagService tagService;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private AggrTargetService aggrTargetService;

    // 产品ID 0.全部 1.探针 2.分析平台 3.其他
    @Value("${shield_pro_type}")
    private Integer shieldProType;

    @Autowired
    UserDao userDao;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Autowired
    private QueryHistoryService queryHistoryService;
    @Autowired
    private TokenService tokenService;

    @Override
    public JSONObject sessionList(AnalysisBaseCondition sessionCondition) {
        LOG.info("会话分析 - 会话列表查询，condition={}", sessionCondition);
        long startTime = System.currentTimeMillis();

        HashMap<String, Object> resultMap = new HashMap<>();

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(sessionCondition);
        if (CollectionUtils.isEmpty(indexNames)) {
            AnalysisBaseCondition.TimeRange timeRange = sessionCondition.getTimeRange();
            Long leftTime = timeRange.getLeft() == null ? 0 : timeRange.getLeft();
            Long rightTime = timeRange.getRight() == null ? 0 : timeRange.getRight();
            // 日志展示查询时间范围,将时间秒变成对应日期
            Date startDate = DateUtil.date(leftTime * 1000);
            Date endDate = DateUtil.date(rightTime * 1000);
            LOG.info("当前时间范围内无会话数据内容：{} - {}", DateUtil.format(startDate, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss"));
            resultMap.put("records", new ArrayList<>());
            resultMap.put("total", 0);
            return CommonUtil.successJson(resultMap);
        }
        String[] indexArray = indexNames.toArray(new String[indexNames.size()]);

        //先判断元数据查询initMateDataQuery
        Map<String, BoolQueryBuilder> map = ESQueryUtil.mateDataQuery(sessionCondition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.isEmpty()) {
            resultMap.put("records", new ArrayList<>());
            resultMap.put("total", 0);
            return CommonUtil.successJson(resultMap);
        }

        // ES语句拼装 与 查询
        BoolQueryBuilder queryBuilder = esAssemblySession(sessionCondition);
        if (sessionIdsByMateDataQuery != null && !sessionIdsByMateDataQuery.isEmpty()) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        // 处理标签相关的查询
        queryBuilder = aggrTargetService.getTargetTagCondition(sessionCondition, "ALL", queryBuilder);
        if (queryBuilder == null) {
            // 无结果
            resultMap.put("records", new ArrayList<>());
            resultMap.put("total", 0);
            return CommonUtil.successJson(resultMap);
        }

        // 4.组装对connectInfo ID集合的查询
        SearchRequest searchRequest = new SearchRequest(indexArray);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        try {
            searchSourceBuilder.query(queryBuilder);
            CountRequest countRequest = new CountRequest(indexArray, searchSourceBuilder);
            CountResponse countResponse = esearchService.esSearchForCount(countRequest);
            long count = countResponse.getCount();

            // 返回页数
            if (count == 0) {
                resultMap.put("records", new ArrayList<>());
                resultMap.put("total", 0);
                return CommonUtil.successJson(resultMap);
            }
            // 分页
            Integer currentPage = sessionCondition.getCurrentPage();
            Integer size = sessionCondition.getPageSize();
            List<Map<String, Object>> list;
            if (size == -1) {
                size = 20000;
                currentPage = 1;
                list = new ArrayList<>();
                //每次查询2W的数据
                while (true) {
                    List<Map<String, Object>> listFor = queryList(size, currentPage, sessionCondition, searchSourceBuilder, searchRequest);
                    if (listFor.isEmpty()) {
                        break;
                    }
                    list.addAll(listFor);
                    if (currentPage * size >= esLimit) {
                        //for循环保险
                        break;
                    }
                    currentPage++;
                }
            } else {
                list = queryList(size, currentPage, sessionCondition, searchSourceBuilder, searchRequest);
            }

            // 写入总量
            resultMap.put("total", count);
            resultMap.put("records", list);
            Long costTime = System.currentTimeMillis() - startTime;
            // 查询成功后，判断当前的查询条件中是否有标签查询，在Redis中保存其查询次数和记录
            if(!sessionCondition.getIsTask()){
                tagService.updateTagRecommend(sessionCondition.getQuery());
                queryHistoryService.createQueryHistory(sessionCondition, new ArrayList<>(), costTime, count);
            }
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.ES_SEARCH_ERROR);
        }
        LOG.info("会话分析 - 会话列表查询结束，用时：{}秒", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson(resultMap);
    }

    private List<Map<String, Object>> queryList(Integer size, Integer page, AnalysisBaseCondition sessionCondition, SearchSourceBuilder searchSourceBuilder,
                                                SearchRequest searchRequest) {
        int from = (page - 1) * size;
        searchSourceBuilder.from(from).size(size);

        if (StringUtils.isNotEmpty(sessionCondition.getOrderField())) {
            // 是否升序
            SortOrder isAsc;
            if (sessionCondition.getAsc()) {
                isAsc = SortOrder.ASC;
            } else {
                isAsc = SortOrder.DESC;
            }
            searchSourceBuilder.sort(sessionCondition.getOrderField(), isAsc);
        }

        searchRequest.source(searchSourceBuilder);
        SearchResponse esResponse = esearchService.esSearch(searchRequest);
        long totalHits = esResponse.getHits().getTotalHits();
        ArrayList<Map<String, Object>> esResultMapList = getESResultMap(esResponse);

        // 生成关联查询集合
        List<Integer> taskIdList = sessionCondition.getTaskId();
        LinkedHashSet<String> ipSet = new LinkedHashSet<>();
        LinkedHashSet<Integer> tagIdSet = new LinkedHashSet<>();
        for (Map<String, Object> esResultMap : esResultMapList) {
            ipSet.add((String) esResultMap.getOrDefault("sIp", ""));
            ipSet.add((String) esResultMap.getOrDefault("dIp", ""));
            tagIdSet.addAll((List<Integer>) esResultMap.getOrDefault("Labels", new ArrayList<>()));
        }

        // 数据库查询
        List<TbTaskAnalysis> taskInfo;
        List<TbTagInfo> tagInfo = new ArrayList<>();
        List<String> sessionIdList = new ArrayList<>();
        List<TbUseIpPosition> ipPosition = new ArrayList<>();
        // 任务名称查询
        taskInfo = thSessionDao.listTask(taskIdList);
        // Ip地址查询
        ArrayList<String> ipList = new ArrayList<>(ipSet);
        if (ipList.size() > 0) {
            ipPosition = thSessionDao.listIpPosition(ipList);
        }

        // 标签名称查询
        ArrayList<Integer> tagList = new ArrayList<>(tagIdSet);
        List<TagInfoVo> ruleTag = null;
        if (tagList.size() > 0) {
            tagInfo = thSessionDao.listTagInfo(tagList);
            ruleTag = featureRuleDao.getListForLabel(tagList);
        }

        // 表对象取值，转换成Map
        Map<Integer, TaskInfoVo> taskNameMap = toTaskMap(taskInfo);
        Map<String, IpPositionVo> ipPositionMap = toPositionMap(ipPosition);
        Map<Integer, TagInfoVo> tagInfoMap = toTagMap(tagInfo, ruleTag);

        // 遍历ES返回结果集 与 mysql查询结果一一对应
        for (Map<String, Object> esResultMap : esResultMapList) {
            // taskId 对应 taskName(从Hkey里面取)
            //Integer taskId = getTaskId(String.valueOf(esResultMap.getOrDefault("es_key", "")));
            String esKey = String.valueOf(esResultMap.getOrDefault("es_key", ""));
            Integer taskId = Integer.parseInt(esKey.split("_")[1]);
            esResultMap.put("task_id", taskId);
            TaskInfoVo task = taskNameMap.get(taskId);
            esResultMap.put("taskName", task.getTaskName());
            // tagId 对应tag具体内容
            ArrayList<TagInfoVo> tagResultList = new ArrayList<>();
            ArrayList<Object> labels = (ArrayList<Object>) esResultMap.get("Labels");
            if (labels != null && labels.size() > 0) {
                for (Object label : labels) {
                    //es有错误数据   类似200.0 这种字符串，暂时用double替换
                    tagResultList.add(tagInfoMap.get((int) Double.parseDouble(label.toString())));
                }
            }
            esResultMap.put("tagText", tagResultList);
        }
        return esResultMapList;
    }

    @Override
    public JSONObject tagLibrary() {
        // 标签查询
        List<TagLibraryVo> tagList = new ArrayList<>();
        List<FeatureRule> ruleList = new ArrayList<>();
        try {
            tagList = thSessionDao.listTagAttribute(shieldProType);
            QueryWrapper queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            ruleList = featureRuleDao.selectList(queryWrapper);
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.TAG_LIBRARY_QUERY_ERROR);
        }

        // rule to tag
        for (FeatureRule rule : ruleList) {
            TagLibraryVo tag = new TagLibraryVo();
            tag.setAttributeName(rule.getRuleState());
            tag.setBlackList(rule.getRuleLevel());
            tag.setTagExplain(rule.getRuleDesc());
            tag.setTagId(rule.getRuleId());
            tag.setTagTargetType(6);
            tag.setWhiteList(0);
            tag.setTagText("规则_" + rule.getRuleName());

            tagList.add(tag);
        }

        return CommonUtil.successJson(tagList);
    }

    @Override
    public JSONObject taskList() {
        Integer userId = tokenService.getUserInfoByToken();
        List<TbTaskAnalysis> tbTaskAnalyses = new ArrayList<>();
        try {
            tbTaskAnalyses = thSessionDao.listTaskAll(userId);
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.QUERY_TASK_CONFIG_ERROR);
        }

        return CommonUtil.successJson(tbTaskAnalyses);
    }

    @Override
    public JSONObject protocol(int type) {
        List<ProtocolVo> protocolVos;
        try {
            protocolVos = thSessionDao.listProtocol(type);
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(protocolVos);
    }

    @Override
    public ResultVo esField() {
        String jsonStr = StringUtils.EMPTY;
        try {
            jsonStr = parseFile("dict/es_field.json");
            return ResultVo.success(jsonStr);
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.ES_QUERY_FIELD_TRANSFER_ERROR);
        }
    }

    @Override
    public JSONObject downloadPrepare(DownloadPcapCondition condition) {
        LOG.info("下载pcap包任务创建,是否全量: {}", condition.getType());
        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        // 全量查询状态下，会话条数  > 10000 不查
        if (condition.getType() && ObjectUtils.isNotEmpty(condition.getSessionId()) && condition.getSessionId().size() > 10000) {
            throw new GkException(GkErrorEnum.DOWNLOAD_FILE_COUNT_OVERATE);
        }
        // 计算pcap大小
        Long totalBytes = 0L;
        Long count = 0L;
        try {
            SearchSumCountBytesVo searchSumCountBytesVo = searchSumBytes(condition);
            totalBytes = searchSumCountBytesVo.getTotalBytes();
            count = searchSumCountBytesVo.getCount();
        } catch (NumberFormatException e) {
            LOG.error(e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        } catch (Exception e1) {
            LOG.error("ES查询失败: {}", e1.getMessage());
            throw new GkException(GkErrorEnum.ES_SEARCH_ERROR);
        }
        if (count >= 10000L) {
            throw new GkException(GkErrorEnum.DOWNLOAD_FILE_COUNT_OVERATE);
        }

        // 大于10G的不要
        if (totalBytes > (10L * 1024L * 1024L * 1024L)) {
            throw new GkException(GkErrorEnum.DOWNLOAD_FILE_COUNT_OVERATE);
        }

        DownloadTask downloadTask = null;
        try {
            // 入库，创建任务
            downloadTask = getDownloadTask(condition);
            downloadTaskDao.insert(downloadTask);
        } catch (Exception e) {
            LOG.error(e.getMessage());
            throw new GkException(GkErrorEnum.DOWNLOAD_TASK_CREATE_FAIL);
        }

        HashMap<String, Integer> result = new HashMap<>();
        result.put("id", downloadTask.getId());
        return CommonUtil.successJson(result);
    }

    @Override
    public JSONObject downloadPcap(Integer taskId) {
        LOG.info("pcap下载地址获取，taskId: [ {} ].", taskId);

        // 检测是否完成,未完成则等待1秒再起请求
        // 请求计数
        int count = 0;
        String path = null;
        try {
            while (true) {
                StringUtils.isBlank(path);
                path = downloadTaskDao.getPcapPath(taskId);
                // 不为null则表示查询到结果
                if (path != null) {
                    break;
                }
                // 请求一分钟还没拿到数据，则请求超时
                if (count >= 60) {
                    throw new GkException(GkErrorEnum.REQUEST_OVERTIME);
                }
                Thread.sleep(1000);
                count++;
            }
        } catch (Exception e) {
            LOG.info(e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(path);
    }

    @Override
    public JSONObject downloadPcapList(DownloadListSearchCondition condition) {
        LOG.info("查询Pcap下载任务列表:{}", condition);
        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        long startTime = System.currentTimeMillis();

        PageHelper.startPage(condition.getPage(), condition.getLimit());

        // 用户列表查询
        HashMap<Long, UserInfoVo> userMap = new HashMap<>();
        List<UserInfoVo> userList = userDao.listAll();
        for (UserInfoVo userInfoVo : userList) {
            userMap.put(userInfoVo.getId(), userInfoVo);
        }

        // pcap下载查询
        List<DownloadPcapAdminVo> downloadPcapAdminVos = new ArrayList<>();
        try {
            downloadPcapAdminVos = downloadTaskDao.listdownloadPcapList(condition);
        } catch (Exception e) {
            LOG.info(e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }
        PageInfo<DownloadPcapAdminVo> info = new PageInfo<>(downloadPcapAdminVos);
        // 排队计数
        int i = 0;
        for (DownloadPcapAdminVo downloadPcapAdminVo : downloadPcapAdminVos) {
            // 排队计数
            if (downloadPcapAdminVo.getState() == 0) {
                downloadPcapAdminVo.setFrontNum(i);
                i++;
            }
            UserInfoVo userInfo = userMap.get(Long.parseLong(userId.toString()));
            if (ObjectUtils.isNotEmpty(userInfo)) {
                downloadPcapAdminVo.setUserName(userInfo.getUsername());
            }

            // 数据保存剩余时间计算
            Integer deleteData = downloadPcapAdminVo.getEndTime();
            if (ObjectUtils.isNotEmpty(deleteData)) {
                long thisTime = System.currentTimeMillis() / 1000;
                Long deleteTime = (deleteData-thisTime) / 3600;
                if (deleteTime > 0) {
                    downloadPcapAdminVo.setDeleteTime(deleteTime);
                } else {
                    downloadPcapAdminVo.setDeleteTime(0L);
                }
            }

            // 填充字段
            String showQuery = downloadPcapAdminVo.getShowQuery();
            if (StringUtils.isBlank(showQuery)) {
                downloadPcapAdminVo.setShowQuery(StringUtils.EMPTY);
            }

            downloadPcapAdminVo.setQueryArray(new JSONArray(0));

        }

        // 若不是管理员不显示用户名字段
        JSONObject resultJson = new JSONObject();
        resultJson.put("records", downloadPcapAdminVos);
        resultJson.put("limit", condition.getLimit());
        resultJson.put("page", condition.getPage());
        resultJson.put("total", info.getTotal());

        LOG.info("Pcap下载任务列表查询结束,用时: {}", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson(resultJson);
    }

    @Override
    public JSONObject deletePcapRecord(Integer taskId) {
        LOG.info("执行记录删除：任务Id为:[ {} ].", taskId);

        // 获取任务详情
        DownloadTask taskRecord = thSessionDao.getTaskRecord(taskId);
        Integer state = taskRecord.getState();

        // 当前数据状态为可下载,删除记录及数据
        if (state == 1 || state == 3) {
            try {
                thSessionDao.deletePcapDownloadRecordAndData(taskId);
            } catch (Exception e) {
                LOG.error("删除记录执行失败:", e);
                throw new GkException(GkErrorEnum.DELETE_DOWNLOAD_TASK_FAIL);
            }
        } else if (state == 0 || state == 2) {
            try {
                // 为0 || 2仅删除记录
                thSessionDao.deletePcapDownloadRecord(taskId);
            } catch (Exception e) {
                LOG.error("删除记录执行失败:", e);
                throw new GkException(GkErrorEnum.DELETE_DOWNLOAD_TASK_FAIL);
            }
        }

        LOG.info("删除记录执行成功，任务Id为:[ {} ].", taskId);
        return CommonUtil.successJson();
    }

    private static Map<Integer, TaskInfoVo> toTaskMap(List<TbTaskAnalysis> taskList) {
        HashMap<Integer, TaskInfoVo> result = new HashMap<>(16);
        for (TbTaskAnalysis task : taskList) {
            TaskInfoVo taskInfoVo = new TaskInfoVo();
            taskInfoVo.setTaskId(task.getTaskId());
            taskInfoVo.setTaskName(task.getTaskName());

            result.put(taskInfoVo.getTaskId(), taskInfoVo);
        }
        return result;
    }


    private DownloadTask getDownloadTask(DownloadPcapCondition condition) {
        DownloadTask downloadTask = new DownloadTask();

        Long createTime = System.currentTimeMillis();
        downloadTask.setCreatedTime(createTime / 1000);
        // 往后推24小时得出删除时间
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, 24);
        downloadTask.setEndTime(calendar.getTimeInMillis() / 1000);

        AnalysisBaseCondition query = condition.getQuery();
        if (ObjectUtils.isNotEmpty(query)) {

            // 转换为展示查询条件
            Map<String, Map<String, Object>> queryMap = handleExpQueryData(condition);
            if (MapUtils.isEmpty(queryMap)) {
                downloadTask.setShowQuery(StringUtils.EMPTY);
            } else {
                String jsonStr = JSONObject.toJSONString(queryMap);
                downloadTask.setShowQuery(jsonStr);
            }

            // 转换成ES查询语句
            BoolQueryBuilder boolQueryBuilder = esAssemblySession(query);
            downloadTask.setQuery(new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .toString());
        } else {
            downloadTask.setQuery("[]");
        }

        Boolean type = condition.getType();
        Integer iType = 0;
        if (type) {
            iType = 1;
        }
        downloadTask.setType(iType);
        downloadTask.setUserId(condition.getUserId());
        downloadTask.setTaskId(query.getTaskId().toString());

        List<DownloadPcapCondition.Session> sessionId = condition.getSessionId();
        if (ObjectUtils.isNotEmpty(sessionId) && sessionId.size() > 0) {
            downloadTask.setSessionId(JSONArray.toJSON(sessionId).toString());
        }
        return downloadTask;
    }

    /**
     *
     *
     * @param condition
     * @return
     */
    private Map<String, Map<String, Object>> handleExpQueryData(DownloadPcapCondition condition) {

        // 是否为全量下载
        boolean type = condition.getType();

        AnalysisBaseCondition query = condition.getQuery();

        // 最外层map，分为正选&反选
        Map<String, Map<String, Object>> queryMap = new HashMap<>();
        queryMap.put("and", new HashMap<>());
        queryMap.put("not", new HashMap<>());

        List<AnalysisBaseCondition.QueryOb> queryObList = query.getQuery();
        for (AnalysisBaseCondition.QueryOb queryOb : queryObList) {
            if (queryOb.getBoolSearch().equals("and")) {
                // 正选条件项
                List<AnalysisBaseCondition.SearchInfo> searchInfoList = queryOb.getSearch();
                for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                    String queryField = searchInfo.getTarget();
                    List<Object> vals = new ArrayList<>();
                    vals.addAll(searchInfo.getVal());
                    Map<String, List<Object>> singleQuery = new HashMap<>();
                    singleQuery.put(queryField, vals);
                    Map<String, Object> includeMap = queryMap.get("and");
                    if (includeMap.containsKey(queryMap)) {
                        // 当前字段已经有查询条件，添加
                        List<Object> existTargets = (List<Object>) includeMap.get(queryField);
                        existTargets.addAll(vals);
                        includeMap.put(queryField, existTargets);
                    } else {
                        // 无当前字段，创建map
                        includeMap.put(queryField, vals);
                    }
                    queryMap.put("and", includeMap);
                }
            } else if (queryOb.getBoolSearch().equals("not")) {
                // 反选条件项
                // 正选条件项
                List<AnalysisBaseCondition.SearchInfo> searchInfoList = queryOb.getSearch();
                for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                    String queryField = searchInfo.getTarget();
                    List<Object> vals = new ArrayList<>();
                    vals.addAll(searchInfo.getVal());
                    Map<String, List<Object>> singleQuery = new HashMap<>();
                    singleQuery.put(queryField, vals);
                    Map<String, Object> excludeMap = queryMap.get("not");
                    if (excludeMap.containsKey(queryMap)) {
                        // 当前字段已经有查询条件，添加
                        List<Object> existTargets = (List<Object>) excludeMap.get(queryField);
                        existTargets.addAll(vals);
                        excludeMap.put(queryField, existTargets);
                    } else {
                        // 无当前字段，创建map
                        excludeMap.put(queryField, vals);
                    }
                    queryMap.put("not", excludeMap);
                }
            }
        }

        List<DownloadPcapCondition.Session> sessionList = condition.getSessionId();

        // 若是选中了多条会话，将选中的会话ID进行展示
        if (CollectionUtils.isNotEmpty(sessionList)) {
            List<String> showSessionList = new ArrayList<>();
            for (DownloadPcapCondition.Session session : sessionList) {
                String sessionId = session.getSessionId();
                showSessionList.add(sessionId);
            }
            Map<String, Object> sessionMap = new HashMap<>();
            sessionMap.put("SessionId", showSessionList);
            queryMap.put("and", sessionMap);
        }

        return queryMap;
    }

    private static Map<Integer, TagInfoVo> toTagMap(List<TbTagInfo> tagList, List<TagInfoVo> ruleTag) {
        HashMap<Integer, TagInfoVo> result = new HashMap<>(16);
        for (TbTagInfo tag : tagList) {
            TagInfoVo tagInfoVo = new TagInfoVo();
            tagInfoVo.setTagId(tag.getTagId());
            tagInfoVo.setTagText(tag.getTagText());
            tagInfoVo.setTagExplain(tag.getTagExplain());
            tagInfoVo.setBlackList(tag.getBlackList());
            tagInfoVo.setWhiteList(tag.getWhiteList());
            result.put(tagInfoVo.getTagId(), tagInfoVo);
        }
        if (ruleTag != null) {
            for (TagInfoVo tagInfoVo : ruleTag) {
                tagInfoVo.setTagText("规则_" + tagInfoVo.getTagText());
                result.put(tagInfoVo.getTagId(), tagInfoVo);
            }
        }

        return result;
    }


    private static Map<String, IpPositionVo> toPositionMap(List<TbUseIpPosition> ipPositionList) {
        HashMap<String, IpPositionVo> result = new HashMap<>(16);
        for (TbUseIpPosition ipPosition : ipPositionList) {
            IpPositionVo ipPositionVo = new IpPositionVo();
            ipPositionVo.setIp(ipPosition.getIp());
            ipPositionVo.setCity(ipPosition.getCity());
            ipPositionVo.setCountry(ipPosition.getCountry());
            ipPositionVo.setProvince(ipPosition.getProvince());
            result.put(ipPositionVo.getIp(), ipPositionVo);
        }

        return result;
    }


    public static int getTaskId(String esKey) {
        String tmp = esKey.replace("connectinfo_", "");
        String tmp2 = tmp.replaceAll("_[0-9]{0,}", "");
        try {
            return Integer.parseInt(tmp2);
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 会话列表 ES查询语句拼装
     *
     * @return 查询语句
     */
    public BoolQueryBuilder esAssemblySession(AnalysisBaseCondition sessionCondition) {
        // 设置时间范围
        AnalysisBaseCondition.TimeRange timeRange = sessionCondition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            sessionCondition.setTimeRange(timeRange);
        }
        Long left = timeRange.getLeft();
        if (left == null || left < 1) {
            left = -1L;
            timeRange.setLeft(left);
        }
        Long right = timeRange.getRight();
        if (right == null || right < 1) {
            right = -1L;
            timeRange.setRight(right);
        }
        // JSON query字段对应条件拼装
        BoolQueryBuilder boolQueryBuilder = ESQueryUtil.query(sessionCondition);
        return boolQueryBuilder;
    }

    private ArrayList<Map<String, Object>> getESResultMap(SearchResponse searchResponse) {
        ArrayList<Map<String, Object>> list = new ArrayList<>();

        SearchHit[] hits = searchResponse.getHits().getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> result = hit.getSourceAsMap();
            result.put("es_index", hit.getIndex());
            list.add(result);
        }
        return list;
    }

    private SearchSumCountBytesVo searchSumBytes(DownloadPcapCondition condition) {
        SearchSumCountBytesVo searchSumCountBytesVo = new SearchSumCountBytesVo();
        List<DownloadPcapCondition.Session> sessionList = condition.getSessionId();
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource();
        BoolQueryBuilder boolQueryBuilder;
        List<String> indexNames = queryFirstIndexName(condition.getQuery());
        String[] indexArray = indexNames.toArray(new String[indexNames.size()]);
        SearchRequest request = new SearchRequest(indexArray);

        if (ObjectUtils.isEmpty(sessionList) || sessionList.size() == 0) {
            AnalysisBaseCondition query = condition.getQuery();
            boolQueryBuilder = esAssemblySession(query);
        } else {
            boolQueryBuilder = QueryBuilders.boolQuery();

            // 获取sessionIdList
            ArrayList<String> sessionIdList = new ArrayList<>();
            for (DownloadPcapCondition.Session session : sessionList) {
                sessionIdList.add(session.getSessionId());

                // 修改batch_id
                String batchId = session.getBatchId();
                batchId = batchId.split("_")[2];
                session.setBatchId(batchId);
            }

            boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", sessionIdList));
        }

        sourceBuilder.query(boolQueryBuilder);

        CountRequest countRequest = new CountRequest(indexArray);
        countRequest.source(sourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        searchSumCountBytesVo.setCount(count);

        sourceBuilder.fetchSource("TotalBytes,SessionId".split(","), null);
        // 全量pcap下载限制10000条
        sourceBuilder.size(10000);
        request.source(sourceBuilder);
        SearchResponse response = esearchService.esSearch(request);
        Long totalByteSum = 0L;
        SearchHit[] hits = response.getHits().getHits();
        if (hits.length == 0) {
            return searchSumCountBytesVo;
        }

        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            if (ObjectUtils.isEmpty(sourceAsMap) || sourceAsMap.size() == 0) {
                continue;
            }
            Object totalBytes = sourceAsMap.getOrDefault("TotalBytes", 0L);

            totalBytes = InstanceofUtils.intInstanceofLong(totalBytes);

            if (totalBytes instanceof Long) {
                totalByteSum += Long.valueOf(totalBytes.toString());
            } else {
                // 若出现错误值，则抛出异常。
                throw new NumberFormatException("es返回数据格式化错误，SessionId = " + sourceAsMap.get("SessionId"));
            }
        }
        searchSumCountBytesVo.setTotalBytes(totalByteSum);
        return searchSumCountBytesVo;
    }

    /**
     * es_index下获取查询索引集
     *
     * @param condition
     * @return
     */
    private List<String> queryFirstIndexName(AnalysisBaseCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("es_index");
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("index.keyword", "connectinfo*"));
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        //es_index的时间范围特殊
        ESQueryUtil.delEsIndexTime(boolQueryBuilder, timeRange.getLeft(), timeRange.getRight());
        List<Integer> taskId = condition.getTaskId();
        if (taskId != null && taskId.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("task", taskId));
        }
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);

        //这里判断返回值 esResultMapList
        Map<String, String> map = new HashMap<>();
        List<String> indexNames = new ArrayList<>();
        if (esResultMapList.size() > 1000) {
            for (Map<String, Object> esResultMap : esResultMapList) {
                String index = esResultMap.get("index").toString();
                String[] sliV = index.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            return indexNames;
        }
        for (Map<String, Object> esResultMap : esResultMapList) {
            String index = esResultMap.get("index").toString();
            indexNames.add(index);
        }
        return indexNames;
    }

    private String parseFile(String path) throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            LOG.error("读取path->{}文件失败！error->{}", path, e);
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return null;
    }

}
