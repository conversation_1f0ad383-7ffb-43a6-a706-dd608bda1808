package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class OrgInfoVo {

    /**
     * VID
     */
    private String vid;

    /**
     * 企业名称
     */
    @JsonProperty(value = "org_name")
    private String orgName;

    /**
     * 企业说明
     */
    @JsonProperty(value = "org_desc")
    private String orgDesc;

    /**
     * 黑名单
     */
    @JsonProperty(value = "black_list")
    private Integer blackList;

    /**
     * 白名单
     */
    @JsonProperty(value = "white_list")
    private Integer whiteList;

    @JsonProperty(value = "remark")
    private String remark;

    /**
     * 标签
     */
    @JsonProperty(value = "labels")
    private List<Long> labels;

    /**
     * 备注
     */
    @JsonProperty(value = "remarks")
    private List<String> remarks;
}
