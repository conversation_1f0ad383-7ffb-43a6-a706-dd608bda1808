package com.geeksec.analysis.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.entity.common.ResultVo;

import java.util.List;


/**
 * <AUTHOR>
 * @Description：证书检索-查询历史&查询模板用Service
 */
public interface QueryTemplateService {

    /**
     * 创建证书检索查询模板
     * @param condition
     * @return
     */
    ResultVo createTemplate(AnalysisBaseCondition condition);

    /**
     * 根据用户ID查询模板列表
     * @param params
     * @return
     */
    ResultVo queryTemplateList(JSONObject params);

    /**
     * 用户进行模板查询，进行使用时间更新
     * @param templateId
     * @return
     */
    ResultVo updateTemplateUseTime(Integer templateId);

    /**
     * 批量删除模板记录
     * @param ids
     * @return
     */
    ResultVo deleteTemplateRecord(List<Integer> ids,boolean clear);
}
