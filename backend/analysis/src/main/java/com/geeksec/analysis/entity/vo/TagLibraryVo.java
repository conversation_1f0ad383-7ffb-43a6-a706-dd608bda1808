package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/10 16:28
 * @Description： <Functions List>
 */
@Data
public class TagLibraryVo {

    /**
     * 标签ID
     */
    @JsonProperty("tag_id")
    private Integer tagId;

    /**
     * 标签名称
     */
    @JsonProperty("tag_text")
    private String tagText;

    /**
     * 标签内容
     */
    @JsonProperty("tag_explain")
    private String tagExplain;

    /**
     * 黑名单权值
     */
    @JsonProperty("black_list")
    private Integer blackList;

    /**
     * 白名单权值
     */
    @JsonProperty("white_list")
    private Integer whiteList;

    /**
     * 细分类名称
     */
    @JsonProperty("attribute_name")
    private String attributeName;

    /**
     * 标签目标类型(0 ip目标，1 端口目标，2 应用目标，3 域名目标，4 证书目标，5 MAC目标，6 连接目标(会话) , 7 指纹 9999 所有)
     */
    @JsonProperty("tag_target_type")
    private Integer tagTargetType;

    /**
     * 标签等级
     */
    @JsonProperty("tag_level")
    private String tagLevel;

    /**
     * 标签使用次数
     */
    @JsonProperty("use_num")
    private Integer useNum;

}
