package com.geeksec.analysis.entity.storage;

import com.geeksec.analysis.entity.vo.CertTagVo;
import lombok.Data;

import java.util.List;

/**
 * 证书基础信息详情
 */
@Data
public class CertInfoVo {

    /**
     * 证书SHA1
     */
    private String cert;

    /** 备注 */
    private String remark;

    private List<String> remarks;

    /**
     * 父证书集合
     */
    private List<String> fatherIdList;

    /**
     * 证书黑名单权值
     */
    private Integer blackList;

    /**
     * 证书白名单权值
     */
    private Integer whiteList;

    /**
     * 首次出现时间
     */
    private long firstTime;

    /**
     * 末次出现时间
     */
    private long lastTime;

    /** 标签ids */
    private List<Integer> labels;

    /** 标签对象集合 */
    private List<CertTagVo> certTagList;

    /** 服务器热度 */
    private Integer serverHeat;

    /** 客户端热度 */
    private Integer clientHeat;

    /** 签发机构  ES:Issuer.O */
    private String issuerO;

    /** 所有者机构  ES:Subject.O*/
    private String subjectO;

    /** 颁发时间  ES：有效期开始时间 NotBefore */
    private String notBefore;

    /** 有效时间  ES：有效期结束时间 NotAfter */
    private String notAfter;

    /**
     * 出现任务名称集合
     */
    private List<String> taskNames;
}
