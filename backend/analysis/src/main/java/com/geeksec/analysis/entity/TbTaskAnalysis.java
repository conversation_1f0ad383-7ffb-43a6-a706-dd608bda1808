package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Author: GuanHao
 * @Date: 2022/5/11 10:05
 * @Description： <Functions List>
 */
@Data
@TableName("tb_task_analysis")
public class TbTaskAnalysis {

    @TableField("id")
    private Integer id;

    @TableField("task_id")
    private Integer taskId;

    @TableField("task_name")
    private String taskName;

    @TableField("netflow")
    private String netFlow;

    @TableField("task_remark")
    private String taskRemark;

    @TableField("task_state")
    private Integer taskState;

    @TableField("last_suspend_time")
    private Date lastSuspendTime;

    @TableField("suspend_times")
    private Long suspendTimes;
}
