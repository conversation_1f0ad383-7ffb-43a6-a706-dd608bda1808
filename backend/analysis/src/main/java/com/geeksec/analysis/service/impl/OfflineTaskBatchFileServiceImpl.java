package com.geeksec.analysis.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.analysis.dao.OfflineTaskBatchFileDao;
import com.geeksec.analysis.entity.OfflineTaskBatchFile;
import com.geeksec.analysis.entity.TaskBatch;
import com.geeksec.analysis.entity.dto.OfflineFilePathDto;
import com.geeksec.analysis.service.OfflineTaskBatchFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 离线任务批次关联文件服务impl
 */
@Service
@DS("nta-db")
public class OfflineTaskBatchFileServiceImpl extends ServiceImpl<OfflineTaskBatchFileDao, OfflineTaskBatchFile> implements OfflineTaskBatchFileService {

    private static final Logger logger = LoggerFactory.getLogger(OfflineTaskBatchFileServiceImpl.class);

    @Autowired
    private OfflineTaskBatchFileDao offlineTaskBatchFileDao;

    @Override
    public void add(TaskBatch offlineTaskBatch, List<OfflineFilePathDto> filePathList) {
        List<OfflineTaskBatchFile> offlineTaskBatchFileList = new ArrayList<>();
        for(OfflineFilePathDto filePath : filePathList){
            OfflineTaskBatchFile offlineTaskBatchFile = new OfflineTaskBatchFile();
            offlineTaskBatchFile.setTaskId(offlineTaskBatch.getTaskId());
            offlineTaskBatchFile.setBatchId(offlineTaskBatch.getBatchId());
            offlineTaskBatchFile.setBatchType(offlineTaskBatch.getBatchType());
            offlineTaskBatchFile.setFilePath(filePath.getServerPath());
            offlineTaskBatchFile.setLocalPath(filePath.getLocalPath());
            offlineTaskBatchFileList.add(offlineTaskBatchFile);
        }
        this.saveBatch(offlineTaskBatchFileList);
    }
}
