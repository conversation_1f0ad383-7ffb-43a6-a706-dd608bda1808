package com.geeksec.analysis.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.condition.*;
import com.geeksec.analysis.entity.vo.LabelAggVo;
import com.geeksec.analysis.entity.vo.RangeSessionVo;
import com.geeksec.analysis.entity.vo.SessionAggInfoVo;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;

public interface MetadataService {

    /**
     * 元数据列表
     * @param condition
     * @return
     */
    ResultVo<PageResultVo<JSONObject>> getMetadataList(MetadataCondition condition);

    /**
     * 标签聚合
     * @param condition
     * @return
     */
    ResultVo<LabelAggVo> getLabelsInfoList(AnalysisBaseCondition condition);

    /**
     * spring启动 初始化tag的本地缓存
     */
    void initTagCache();

    /**
     * tagId获取tag对象 包含重新init
     * @param tagId
     * @return
     */
    TbTagInfo getTagInfoForCache(Integer tagId);


    /**
     * 时间节点柱状图：连接数
     * @param condition
     * @return
     */
    ResultVo<List<RangeSessionVo>> getRangeSessionList(RangeSessionCondition condition);

    /**
     * 会话列表 聚合
     * @param condition
     * @return
     */
    ResultVo<PageResultVo<SessionAggInfoVo>> getSessionAggList(SessionAggInfoCondition condition);

    /**
     * 元数据 聚合
     * @param condition
     * @return
     */
    ResultVo<PageResultVo<JSONObject>> getMetadataAggList(MetadataAggInfoCondition condition);
}
