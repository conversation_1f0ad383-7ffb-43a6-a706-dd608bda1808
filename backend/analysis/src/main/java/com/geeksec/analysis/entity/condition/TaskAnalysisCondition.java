package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TaskAnalysisCondition {

    @JsonProperty("task_id")
    private Integer taskId;

    /** 端口集合 */
    private List<Integer> netflows;

    /** 0代表历史任务，1代表当前任务 2 代表挂起的任务 */
    @JsonProperty("task_state")
    private Integer taskState;


}
