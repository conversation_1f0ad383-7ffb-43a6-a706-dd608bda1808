package com.geeksec.analysis.service;

import com.geeksec.analysis.condition.SessionDetailCondition;
import com.geeksec.entity.common.ResultVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
public interface TargetDetailService {
    /**
     * 会话目标详情查询（By Subtype）
     */
    ResultVo getSessionDetail(SessionDetailCondition condition);

    /**
     * 添加备注
     * @param targetType
     * @param target
     * @param remark
     * @return
     */
    ResultVo addTargetRemark(String targetType, String target, String remark);

    /**
     * 删除单条备注
     * @param remarkId
     * @return
     */
    ResultVo deleteTargetRemark(String remarkId);

    /**
     * 修改目标详情标签信息
     * @param esIndex
     * @param esId
     * @param lables
     * @return
     */
    ResultVo editTargetTag(String esIndex, String esId, List<Long> lables) throws InterruptedException;

    /**
     * 确认目标黑名单
     * @param targetKey
     * @param targetType
     * @return
     */
    ResultVo confirmBlack(String targetKey, String targetType);
}
