package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: GuanHao
 * @Date: 2022/5/11 10:10
 * @Description： <Functions List>
 */
@Data
@TableName("tb_tag_attribute_rate")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TbTagAttributeRate {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("tag_id")
    private Integer tagId;

    @TableField("attribute_id")
    private Integer attributeId;


}
