package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.condition.AtlasCondition;
import com.geeksec.analysis.condition.FocusTag;
import com.geeksec.analysis.condition.VidsSearchCondition;
import com.geeksec.analysis.dao.AtlasDao;
import com.geeksec.analysis.dao.ThAnalysisDao;
import com.geeksec.analysis.entity.dict.AnalysisTagInfoEntity;
import com.geeksec.analysis.entity.storage.CertInfoVo;
import com.geeksec.analysis.entity.storage.OrgInfoVo;
import com.geeksec.analysis.service.AtlasService;
import com.geeksec.analysis.service.Metadata2Service;
import com.geeksec.constants.VertexTypeConstants;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.repository.AllAtlasDao;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.PathWrapper;
import com.vesoft.nebula.client.graph.data.Relationship;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @author: qiuwen
 * @date: 2022/9/16
 * @Description:
 **/
@Service
@Log4j2
@DS("nta-db")
public class AtlasServiceImpl implements AtlasService {

    private static final Logger logger = LoggerFactory.getLogger(AtlasServiceImpl.class);

    private static List<String> orgRelatedList = Lists.newArrayList("domain_belong_to_org", "ip_belong_to_org", "cert_belong_to_org");
    private static List<String> urlRealtedList = Lists.newArrayList("domain_url_related", "ip_url_related", "cert_url_related");
    private static List<String> connectList = Lists.newArrayList("connect_mac");

    private static Map<String, Map<String, Object>> nebulaDict = new HashMap<>(); // 点边名称字典
    private static Map<String, Map<String, Object>> parseEdgeDict = new HashMap<>(); // 点-边关系字典

    @Value("${elasticsearch.es_connect_index}")
    private String esConnectIndex;

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private AllAtlasDao allAtlasDao;

    @Autowired
    private AtlasDao atlasQueryDao;

    @Autowired
    private ThAnalysisDao thAnalysisDao;

    @Autowired
    private Metadata2Service metadata2Service;

    @Value("${enabled.atlas}")
    private Boolean hasAtlas;

    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;

    @PostConstruct
    private void initSrcDstDict() throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        InputStream inputStream = null;
        String jsonStr = StringUtils.EMPTY;
        // 读取nebula_dict文件
        try {
            if (hasAtlas) {
                jsonStr = parseFile("dict/atlas/nebula_dict.json");
            } else {
                jsonStr = parseFile("dict/analysis/nebula_dict.json");
            }
            nebulaDict = (Map<String, Map<String, Object>>) JSON.parse(jsonStr);
            logger.info("初始化nebula字典成功！");
        } catch (IOException e) {
            logger.error("读取nebula_dict文件失败！error->", e);
        }

        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();

    }

    @PostConstruct
    private void initParseEdgeDict() {
        try {
            String jsonStr = parseFile(hasAtlas ? "dict/atlas/parse_conf.json" : "dict/analysis/parse_conf.json");
            List<Map<String, Object>> relationList = JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {
            });

            for (Map<String, Object> parseMap : relationList) {
                List<String> handleEdgeList = (List<String>) parseMap.get("handle");
                List<String> rhandleEdgeList = (List<String>) parseMap.get("Rhandle");
                String tagType = (String) parseMap.get("type");

                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("handle", handleEdgeList);
                resultMap.put("Rhandle", rhandleEdgeList);
                parseEdgeDict.put(tagType, resultMap);
            }
        } catch (Exception e) {
            logger.error("初始化解析边关系字典失败！error->", e);
        }
    }

    @Override
    public ResultVo vidSearch(VidsSearchCondition condition) {
        List<String> vidList = condition.getVidList();
        Map<String, Object> resultMap = new HashMap<>();
        // 不允许存在空字符串
        if (vidList.stream().anyMatch(StrUtil::isEmpty)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // 存留确实存在的VID点
        List<String> allVid = new ArrayList<>();
        JSONArray vertexArray = new JSONArray();
        JSONArray edgeArray = new JSONArray();

        // 先通过FetchOn确定存在的节点，然后确定对应的allVid里的内容
        List<Map<String, Object>> resultList = allAtlasDao.fetchOnTagInfoByVids(vidList);
        if (ObjectUtil.isEmpty(resultList)) {
            resultMap.put("vertex", vertexArray);
            resultMap.put("edge", edgeArray);
            resultMap.put("contains", new ArrayList<>());
            return ResultVo.success(resultMap);
        }

        try {
            for (Map<String, Object> dataMap : resultList) {
                Map<String, Object> vertexInfoMap = (Map<String, Object>) dataMap.get("vertex_info");
                String tagType = (String) dataMap.get("tag_type");
                // 只取tagType字符串中，'Vertex'前面的内容，用作返回点类型

                String vid = (String) dataMap.get("vertex_id");
                if (allVid.contains(vid)) {
                    continue;
                } else {
                    allVid.add(vid);
                }
                JSONObject vertexJson = new JSONObject();
                vertexJson.put("label", vid);
                vertexJson.put("id", vid);
                vertexJson.put("type", tagType);
                vertexJson.put("v_info", vertexInfoMap);
                vertexArray.add(vertexJson);
            }

            if (allVid.size() > 1) {
                // 通过
                List<Map<String, Object>> pathResultList = allAtlasDao.findPathByVids(allVid);
                if (!CollectionUtils.isEmpty(pathResultList)) {
                    // 一次查询两个以上的节点信息
                    for (Map<String, Object> nebulaMap : pathResultList) {
                        PathWrapper pathWrapper = (PathWrapper) nebulaMap.get("P");
                        List<Node> vertexList = pathWrapper.getNodes();
                        List<Relationship> relationshipList = pathWrapper.getRelationships();
                        // 循环两个点之间的边关系，生成对应边信息
                        for (Relationship relationship : relationshipList) {
                            String from = relationship.srcId().asString();
                            String to = relationship.dstId().asString();
                            String label = relationship.edgeName().toString();
                            JSONObject json = new JSONObject();
                            json.put("from", from);
                            json.put("to", to);
                            json.put("label", label);
                            edgeArray.add(json);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("图数据库解析异常，error->", e);
            throw new GkException(GkErrorEnum.GRAPHDB_PARSE_ERROR);
        }

        // 获取到所有的点和边之后，做一个containsMap用来所相互牵连,据说是为了美观
        List<Map<String, Object>> containsMapList = new ArrayList<>();
        if (allVid.size() > 1) {
            IntStream.range(0, allVid.size())
                    .forEach(num -> {
                        String fromStr = allVid.get(num);
                        // 最后一个点与第一个点相连，用id str做牵连
                        String toStr = (num == allVid.size() - 1) ? allVid.get(0) : allVid.get(num + 1);
                        Map<String, Object> containMap = new HashMap<>();
                        containMap.put("from", fromStr);
                        containMap.put("to", toStr);
                        containMap.put("label", "contain_edge");
                        containsMapList.add(containMap);
                    });
        }


        resultMap.put("vertex", vertexArray);
        resultMap.put("edge", edgeArray);
        resultMap.put("contains", containsMapList);
        return ResultVo.success(resultMap);

    }

    @Override
    public ResultVo propertiesSearch(GraphPropertiesNextCondition condition) {
        Map<String, Object> resultMap = new HashMap<>();

        // 存留确实存在的VID点
        List<String> allVid = new ArrayList<>();
        JSONArray vertexArray = new JSONArray();
        JSONArray edgeArray = new JSONArray();
        try {
            // 先通过FetchOn确定存在的节点，然后确定对应的allVid里的内容
            List<Map<String, Object>> resultList = allAtlasDao.matchTagInfoByProperties(condition);
            if (ObjectUtil.isEmpty(resultList)) {
                resultMap.put("vertex", vertexArray);
                resultMap.put("edge", edgeArray);
                resultMap.put("contains", new ArrayList<>());
                return ResultVo.success(resultMap);
            }
            for (Map<String, Object> dataMap : resultList) {
                Map<String, Object> vertexInfoMap = (Map<String, Object>) dataMap.get("vertex_info");
                String tagType = (String) dataMap.get("tag_type");
                // 只取tagType字符串中，Vertex前面的内容，用作返回点类型

                String vid = (String) dataMap.get("vertex_id");
                if (allVid.contains(vid)) {
                    continue;
                } else {
                    allVid.add(vid);
                }
                JSONObject vertexJson = new JSONObject();
                vertexJson.put("label", vid);
                switch (tagType) {
                    case VertexTypeConstants.ORG:
                        vertexJson.put("label",vertexInfoMap.get("org_name"));
                        break;
                    case VertexTypeConstants.APPSERVICE:
                        vertexJson.put("label",vertexInfoMap.get("AppName"));
                        break;
                    case VertexTypeConstants.ISSUER:
                        vertexJson.put("label",vertexInfoMap.get("common_name"));
                        break;
                    case VertexTypeConstants.SUBJECT:
                        vertexJson.put("label",vertexInfoMap.get("common_name"));
                        break;
                }
                vertexJson.put("id", vid);
                vertexJson.put("type", tagType);
                vertexJson.put("v_info", vertexInfoMap);
                vertexArray.add(vertexJson);
            }

        } catch (Exception e) {
            if(e.getMessage().contains("use space failed:  SpaceNotFound")){
                throw new GkException(GkErrorEnum.SPACE_NOT_FIND_ERROR);
            }
            logger.error("图数据库解析异常，error->", e);
            throw new GkException(GkErrorEnum.GRAPHDB_PARSE_ERROR);
        }

        // 获取到所有的点和边之后，做一个containsMap用来所相互牵连,据说是为了美观
        List<Map<String, Object>> containsMapList = new ArrayList<>();
//        if (allVid.size() > 1) {
//            IntStream.range(0, allVid.size())
//                    .forEach(num -> {
//                        String fromStr = allVid.get(num);
//                        // 最后一个点与第一个点相连，用id str做牵连
//                        String toStr = (num == allVid.size() - 1) ? allVid.get(0) : allVid.get(num + 1);
//                        Map<String, Object> containMap = new HashMap<>();
//                        containMap.put("from", fromStr);
//                        containMap.put("to", toStr);
//                        containMap.put("label", "contain_edge");
//                        containsMapList.add(containMap);
//                    });
//        }

        resultMap.put("vertex", vertexArray);
        resultMap.put("edge", edgeArray);
        resultMap.put("contains", containsMapList);
        return ResultVo.success(resultMap);
    }

    @Override
    public void craeteAtlasRecord(Integer atlasType,String atlasCondition) {
        atlasQueryDao.craeteAtlasRecord(atlasType,atlasCondition);
    }

    @Override
    public HashMap<String, Object> relationList(AtlasCondition condition) {

        String vid = condition.getSearch();
        String tagType = condition.getTagType();
        String edgeType = condition.getEdgeType();
        Boolean direct = condition.getDirect();
        String directStr = direct.toString();

        // 获取当前查询关系的起点终点
        Map<String, Object> paramsMap = nebulaDict.get(edgeType);

        String srcType = (String) paramsMap.get("src");
        String dstType = (String) paramsMap.get("dst");
        logger.info("查询VID为 {} 的对应关系节点信息列表,边类型 edgeType: {} ,src :{} ,dst :{}", vid, edgeType, srcType, dstType);

        // 排序字段
        String orderField = condition.getOrderField();
        String sortOrder = condition.getSortOrder();

        // 当前页展示条数 当前页 偏移量
        Integer limit = condition.getPageSize();
        Integer currentPage = condition.getCurrentPage();
        Integer offset = (currentPage - 1) * limit;

        // 抽取查询终点的点类类型，作为判断排序字段类型
        String targetType = StrUtil.EMPTY;
        if (direct) {
            targetType = paramsMap.get("dst").toString();
        } else {
            targetType = paramsMap.get("src").toString();
        }
        try {
            // 使用 GO FROM 作为当前类型的模板,使用ngbatis进行条件拼装查询
            List<Map<String, Object>> queryList = allAtlasDao.getTagRelationList(vid, edgeType, direct, orderField, sortOrder, limit, currentPage, offset, targetType);
            // 统计当前查询到点数量
            Integer resultCount = allAtlasDao.countTagRelation(vid, edgeType, direct);
            if (resultCount == 0) {
                return new HashMap<>();
            }

            List<Map<String, Object>> resultList = new ArrayList<>(resultCount);
            // 循环遍历处理查询到的点结果
            for (Map<String, Object> result : queryList) {
                Map<String, Object> tagInfoMap = result.get("tag_info") == null ? new HashMap<>() : (Map<String, Object>) result.get("tag_info");
                Map<String, Object> edgeInfoMap = result.get("edge_info") == null ? new HashMap<>() : (Map<String, Object>) result.get("edge_info");
                for (Map.Entry<String, Object> entry : edgeInfoMap.entrySet()) {
                    String key = entry.getKey();
                    if (!"first_seen".equals(key) && !"last_seen".equals(key)) {
                        Object value = entry.getValue();
                        tagInfoMap.put(key, value);
                    }
                }
                handleNebulaResult(tagInfoMap);
                resultList.add(tagInfoMap);
            }

            HashMap<String, Object> resultMap = new HashMap<>();
            if (direct) {
                resultMap.put("type", dstType);
            } else {
                resultMap.put("type", srcType);
            }
            resultMap.put("list", resultList);
            resultMap.put("total", resultCount);

            return resultMap;

        } catch (Exception e) {
            logger.error("图关联节点详情列表查询失败！tagType:{},vid:{},edgeType:{},err->", tagType, vid, edgeType, e);
        }

        return null;
    }

    private Map<String, Object> handleNebulaResult(Map<String, Object> dataMap) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key.equals("white_list") || key.equals("black_list")) {
                if (value.equals("NULL")) {
                    entry.setValue(0);
                }
            }
            if (key.equals("remark")) {
                if (value.equals("NULL")) {
                    entry.setValue(StringUtils.EMPTY);
                }
            }
        }

        // 若当前查询数据为域名信息，则添加锚域名信息
        if (dataMap.containsKey("domain_addr")) {
            String domainAddr = (String) dataMap.get("domain_addr");
            String fDomain = "*." + suffixList.getRegistrableDomain(domainAddr);
            dataMap.put("fDomain", fDomain);
        }

        // 若当前查询数据为证书信息，则添加证书额外信息
        if (dataMap.containsKey("cert_id") && StrUtil.isNotEmpty(dataMap.get("cert_id").toString())) {
            ResultVo vo = metadata2Service.getCertInfo(String.valueOf(dataMap.get("cert_id")));
            CertInfoVo certInfoVo = (CertInfoVo) vo.getData();
            dataMap.put("issuerO", certInfoVo.getIssuerO());
            dataMap.put("subjectO", certInfoVo.getSubjectO());
            dataMap.put("notBefore", certInfoVo.getNotBefore());
            dataMap.put("notAfter", certInfoVo.getNotAfter());
        }

        return dataMap;
    }

    @Override
    public ResultVo getTagLabels(List<Map<String, Object>> params) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> labelVertex = new ArrayList<>();
            List<Map<String, Object>> hasLabelEdge = new ArrayList<>();

            for (Map<String, Object> tagMap : params) {
                String str = (String) tagMap.get("str");
                List<List<String>> hasLabelsList = allAtlasDao.getTagLabels(str);

                if (CollectionUtil.isNotEmpty(hasLabelsList.get(0))) {
                    Map<String, Object> labelMap = new HashMap<>();
                    labelMap.put("id", str + "_label");
                    labelMap.put("type", "LABELS");

                    List<AnalysisTagInfoEntity> labelsInfo = thAnalysisDao.getTagInfoByIds(hasLabelsList.get(0).stream()
                            .map(Integer::valueOf)
                            .collect(Collectors.toList()));

                    labelMap.put("labels", labelsInfo);
                    labelVertex.add(labelMap);

                    Map<String, Object> edgeMap = new HashMap<>();
                    edgeMap.put("label", "has_label");
                    edgeMap.put("from", str);
                    edgeMap.put("to", str + "_label");
                    hasLabelEdge.add(edgeMap);
                }
            }

            result.put("edge", hasLabelEdge);
            result.put("vertex", labelVertex);

            return ResultVo.success(result);
        } catch (Exception e) {
            logger.error("查询关联节点标签信息失败! params -> {},e ->", params, e);
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_TAG_LABEL_FAIL);
        }

    }

    @Override
    public ResultVo getVisibleRelation(String str, String type, String direct) {

        logger.info("查询当前vid为{},种类为{}的节点可展示的边种类,方向:{}", str, type, direct);
        if (parseEdgeDict.isEmpty()) {
            initParseEdgeDict();
        }
        Map<String, Object> parseMap = parseEdgeDict.get(type);
        List<String> edgeList = new ArrayList<>();
        List<String> forwardList = (List<String>) parseMap.get("handle");
        List<String> reverseList = (List<String>) parseMap.get("Rhandle");

        String edgeStr = StrUtil.EMPTY;
        List<String> visibleEdgeList = new ArrayList<>();
        switch (direct) {
            case "forward":
                // 正向关联
                // 将这个edge_list转化为字符串用于查询
                if (CollectionUtil.isNotEmpty(forwardList)) {
                    edgeStr = StringUtils.join(forwardList, ",");
                    visibleEdgeList = allAtlasDao.getVisibleRelation(str, direct, edgeStr);
                }
                break;
            case "reverse":
                // 反向关联
                // 将这个edge_list转化为字符串用于查询
                if (CollectionUtil.isNotEmpty(reverseList)) {
                    edgeStr = StringUtils.join(reverseList, ",");
                    visibleEdgeList = allAtlasDao.getVisibleRelation(str, direct, edgeStr);
                    if (CollectionUtil.isNotEmpty(visibleEdgeList)) {
                        // 赋予反向标识
                        visibleEdgeList = visibleEdgeList.stream().map(edge -> "r_" + edge).collect(Collectors.toList());
                    }
                }
                break;
            case "bothway":
                // 双向关联
                // 先获取正向的边列表
                if (CollectionUtil.isNotEmpty(forwardList)) {
                    edgeStr = StringUtils.join(forwardList, ",");
                    List<String> forwardEdgeList = allAtlasDao.getVisibleRelation(str, "forward", edgeStr);
                    if(forwardEdgeList.contains("belong_to_org")){
                        forwardEdgeList.remove("belong_to_org");
                        forwardEdgeList.add(type.toLowerCase()+"_belong_to_org");
                    }
                    visibleEdgeList.addAll(forwardEdgeList);
                }

                // 再获取反向的列表
                if (CollectionUtil.isNotEmpty(reverseList)) {
                    edgeStr = StringUtils.join(reverseList, ",");
                    List<String> reverseEdgeList = allAtlasDao.getVisibleRelation(str, "reverse", edgeStr);
                    if (CollectionUtil.isNotEmpty(reverseEdgeList)) {
                        reverseEdgeList = reverseEdgeList.stream().map(edge -> "r_" + edge).collect(Collectors.toList());
                    }
                    visibleEdgeList.addAll(reverseEdgeList);
                }
                break;
            default:
                break;
        }
        return ResultVo.success(visibleEdgeList);
    }

    @Override
    public ResultVo getVisibleSideRelation(String str, String type) {
        if (parseEdgeDict.isEmpty()) {
            initParseEdgeDict();
        }
        Map<String, Object> parseMap = parseEdgeDict.get(type);
        List<String> edgeList = new ArrayList<>();
        List<String> forwardList = (List<String>) parseMap.get("handle");
        List<String> reverseList = (List<String>) parseMap.get("Rhandle");

        String edgeStr = StrUtil.EMPTY;
        List<String> itemsToRemoveList = Arrays.asList("same_device_discover");
        forwardList.removeAll(itemsToRemoveList);
        edgeStr = StringUtils.join(forwardList, ",");
        List<String> forwardEdgeList = new ArrayList<>();
        if(StrUtil.isNotEmpty(edgeStr)){
            forwardEdgeList = allAtlasDao.getVisibleRelation(str, "forward", edgeStr);
        }
        forwardEdgeList = forwardEdgeList.stream().map(edge -> edge).collect(Collectors.toList());

        edgeStr = StrUtil.EMPTY;
        edgeStr = StringUtils.join(reverseList, ",");
        List<String> reverseEdgeList = new ArrayList<>();
        if(StrUtil.isNotEmpty(edgeStr)){
            reverseEdgeList = allAtlasDao.getVisibleRelation(str, "reverse", edgeStr);
        }
        reverseEdgeList = reverseEdgeList.stream().map(edge -> "r_" + edge).collect(Collectors.toList());
        List<String> visibleSideEdgeList = new ArrayList<>();
        visibleSideEdgeList.addAll(forwardEdgeList);
        visibleSideEdgeList.addAll(reverseEdgeList);
        return ResultVo.success(visibleSideEdgeList);
    }

    @Override
    public ResultVo queryAtlasHistory(JSONObject params) {
        // 抽离查询条件
        Integer atlasType = (Integer) params.get("atlas_type");
        Integer currentPage = params.getInteger("current_page");
        Integer pageSize = params.getInteger("page_size");
        Integer offset = (currentPage - 1) * pageSize;
        String orderBy = params.getString("sort_order");
        try {
            Integer count = atlasQueryDao.countAtlasHistory(atlasType);
            List<Map<String, Object>> historyList = atlasQueryDao.queryAtlasHistory(atlasType, offset, pageSize, orderBy);
            for (Map<String, Object> dataMap : historyList) {
                String atlasCondition = dataMap.get("atlas_condition").toString();
                JSONObject atlasConditionList = JSONObject.parseObject(atlasCondition);
                dataMap.put("atlas_condition", atlasConditionList);
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", count);
            resultMap.put("data", historyList);
            return ResultVo.success(resultMap);
        } catch (Exception e) {
            logger.error("查询图谱历史记录失败，error：{}", e.getMessage());
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_HISTORY_CLEAR_FAIL);
        }
    }

    @Override
    public ResultVo deleteAtlasHistory(JSONObject params) {
        List<Integer> idList = params.get("id_list") == null ? null : (List<Integer>) params.get("id_list");
        Boolean deleteAll = params.getBoolean("delete_all");
        try {
            if (CollectionUtil.isNotEmpty(idList) && !deleteAll) {
                // 删除对应的查询历史记录

                atlasQueryDao.deleteAtlasHistory(idList);
            } else {
                // 删除所有的查询历史记录
                atlasQueryDao.deleteAllAtlasHistory();
                return ResultVo.success("删除所有图探索查询历史记录成功");
            }
        } catch (Exception e) {
            logger.error("删除图探索查询历史记录失败，error：{}", e.getMessage());
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_HISTORY_CLEAR_FAIL);
        }
        return ResultVo.success("删除图探索查询历史记录成功");
    }

    @Override
    public ResultVo getFocusTagRelation(List<FocusTag> focusTags) {

        logger.info("查询比关注节点之间的关系列表，其查询节点为：{}", focusTags);
        // 传参样式：[{"vid":"**************","type":"IP"},{"vid":"**************","type":"IP"},{"vid":"www.baidu.com","type":"DOMAIN"}]
        // 循环查询每个节点的关联边类型，查询之后继续查询下一个节点与后续节点的关联关系

        List<Map<String, Object>> resultList = new ArrayList<>();

        for (int start = 0; start < focusTags.size(); start++) {
            if (start == focusTags.size() - 1) {
                // 最后一个不存在关联
                break;
            }
            FocusTag startFocusTag = focusTags.get(start);
            String startVid = startFocusTag.getVid();
            String startType = startFocusTag.getType();
            for (int next = start + 1; next < focusTags.size(); next++) {
                FocusTag endFocusTag = focusTags.get(next);
                String endVid = endFocusTag.getVid();
                String endType = endFocusTag.getType();
                // 开始查询后续的边关联信息
                String sql = "MATCH (v1:{startType}) - [e] - (v2:{endType}) where id(v1) == '{startVid}' and id(v2) == '{endVid}' return src(e) as src_vid,dst(e) as dst_vid, type(e) as edge_type";
                sql = sql.replace("{startType}", startType).replace("{endType}", endType).replace("{startVid}", startVid).replace("{endVid}", endVid);
                List<Map<String, Object>> queryList = allAtlasDao.getFocusTagRelation(startVid, endVid, startType, endType);
                // 单个查询条件的关联结果
                if (CollectionUtils.isEmpty(queryList)) {
                    continue;
                } else {
                    // 有关联关系
                    for (Map<String, Object> queryMap : queryList) {
                        String edgeType = queryMap.get("edge_type").toString();
                        String fromVid = queryMap.get("src_vid").toString();
                        String toVid = queryMap.get("dst_vid").toString();
                        Map<String, Object> edgeMap = new HashMap<>();
                        edgeMap.put("id", StringUtils.EMPTY);
                        edgeMap.put("from", fromVid);
                        edgeMap.put("to", toVid);
                        edgeMap.put("label", edgeType);
                        edgeMap.put("type", StringUtils.EMPTY);
                        edgeMap.put("num", StringUtils.EMPTY);
                        edgeMap.put("status", StringUtils.EMPTY);
                        edgeMap.put("lv", StringUtils.EMPTY);
                        resultList.add(edgeMap);
                    }
                }
            }
        }
        return ResultVo.success(resultList);
    }

    private String parseFile(String path) throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            logger.error("读取path->{}文件失败！error->{}", path, e);
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return null;
    }
}
