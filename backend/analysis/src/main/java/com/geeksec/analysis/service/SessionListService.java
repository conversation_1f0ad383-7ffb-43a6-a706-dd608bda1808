package com.geeksec.analysis.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.DownloadListSearchCondition;
import com.geeksec.analysis.entity.condition.DownloadPcapCondition;
import com.geeksec.entity.common.ResultVo;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/5 14:44
 * @Description： 会话列表
 */
public interface SessionListService {

    /**
     * 会话列表查询
     *
     * @param sessionCondition 查询条件
     * @return 会话列表查询
     */
    JSONObject sessionList(AnalysisBaseCondition sessionCondition);

    /**
     * 标签库查询
     *
     * @return 标签库
     */
    JSONObject tagLibrary();

    /**
     * 任务选择下拉列表查询
     *
     * @return 任务列表
     */
    JSONObject taskList();

    /**
     * 协议下拉列表查询
     *
     * @param type IP协议？ 应用协议？
     * @return 协议下拉列表查询
     */
    JSONObject protocol(int type);

    /**
     * ES字段查询
     *
     * @return ES字段查询
     */
    ResultVo esField();

    /**
     * pcap下载准备
     *
     * @param condition pcap 查询条件
     * @return pcap下载准备
     */
    JSONObject downloadPrepare(DownloadPcapCondition condition);

    /**
     * pcap包下载
     *
     * @param taskId 下载任务Id
     * @return pcap包
     */
    JSONObject downloadPcap(Integer taskId);

    /**
     * pcap下载列表查询
     *
     * @param condition 条件
     * @return pcap下载列表
     */
    JSONObject downloadPcapList(DownloadListSearchCondition condition);

    /**
     * 删除pcap下载记录与对应数据
     *
     * @param taskId 条件
     * @return 是否成功
     */
    JSONObject deletePcapRecord(Integer taskId);

}
