package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.dao.QueryTemplateDao;
import com.geeksec.analysis.entity.QueryTemplate;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.service.QueryTemplateService;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class QueryTemplateServiceImpl implements QueryTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(QueryTemplateServiceImpl.class);

    @Autowired
    private UserDao userDao;

    @Autowired
    private QueryTemplateDao queryTemplateDao;

    @Autowired
    private TokenService tokenService;

    @Override
    public ResultVo createTemplate(AnalysisBaseCondition condition) {

        logger.info("根据当前的查询条件创建查询模板,query -> {}", condition.getQuery());
        Integer userId = tokenService.getUserInfoByToken();
        if (userId == null) {
            logger.error("当前查询条件中的用户ID为空");
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }
        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String queryStr = null;
        String queryCn = null;
        try {
            queryStr = mapper.writeValueAsString(queryObList);
            queryCn = mapper.writeValueAsString(condition.getQueryCn());
        } catch (JsonProcessingException e) {
        }
        QueryWrapper<QueryTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_text", queryStr).eq("user_id", userId);
        Long count = queryTemplateDao.selectCount(queryWrapper);
        if(count>0L){
            return ResultVo.fail("该查询模版已存在！");
        }
        /**
         * 新建模板时可以添加模板的备注
         */
        String templateRemark = condition.getTemplateRemark();

        try {
            String userName = userDao.getUserNameById(Long.valueOf(userId));

            QueryTemplate queryTemplate = new QueryTemplate();
            queryTemplate.setUserId(userId);
            queryTemplate.setUserName(userName);
            queryTemplate.setRemark(templateRemark);
            queryTemplate.setTemplateText(queryStr);
            queryTemplate.setQueryCn(queryCn);
            queryTemplate.setCreatedTime(new Date());
            queryTemplateDao.insert(queryTemplate);
        } catch (Exception e) {
            logger.error("创建检索模板失败,query -> {}", queryStr);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);
        }

        return ResultVo.success("创建检索模板成功");
    }

    @Override
    public ResultVo queryTemplateList(JSONObject params) {

        Map<String, Object> resultMap = new HashMap<>();

        Integer userId = params.getInteger("user_id");

        // 获取查询条件
        Integer currentPage = params.getInteger("current_page");
        Integer pageSize = params.getInteger("page_size");

        String sortOrder = params.getString("sort_order");
        Boolean isAsc = false;
        if (sortOrder.equals("asc")) {
            isAsc = true;
        }
        String sortField = params.getString("sort_field");
        QueryWrapper<QueryTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).orderBy(true, isAsc, sortField);
        try {
            PageInfo<QueryTemplate> pageInfo = PageHelper.startPage(currentPage, pageSize)
                    .doSelectPageInfo(() -> queryTemplateDao.selectList(queryWrapper));

            List<QueryTemplate> resultData = pageInfo.getList();
            resultMap.put("data", resultData);
            resultMap.put("total", pageInfo.getTotal());
        } catch (Exception e) {
            logger.error("查询模板列表失败,userId -> {}", userId);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);
        }

        return ResultVo.success(resultMap);
    }

    @Override
    public ResultVo updateTemplateUseTime(Integer templateId) {

        logger.info("当前用户使用模板进行查询,templateId -> {}", templateId);
        QueryTemplate queryTemplate = queryTemplateDao.selectById(templateId);
        if (queryTemplate == null) {
            logger.info("查询用户使用模板失败,当前模板不存在,templateId -> {}", templateId);
            throw new GkException(GkErrorEnum.ES_TEMPLATE_QUERY_ERROR);
        }
        queryTemplate.setLastUsedTime(new Date());
        queryTemplate.setQueryCount(queryTemplate.getQueryCount()+1L);
        queryTemplateDao.updateById(queryTemplate);

        return ResultVo.success("使用模板进行查询成功");
    }

    @Override
    public ResultVo deleteTemplateRecord(List<Integer> ids, boolean clear) {
        logger.info("批量删除模板记录,ids -> {},是否为清空表 -> {}", ids, clear);
        try {
            if (clear) {
                // 当前为清空检索模板表
                queryTemplateDao.deleteAll();
            } else {
                // 批量删除模板记录
                queryTemplateDao.deleteBatchIds(ids);
            }
        } catch (Exception e) {
            logger.error("批量删除模板记录失败,ids -> {},是否为清空表 -> {}", ids, clear);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);        }

        return ResultVo.success("删除检索模板成功");
    }
}
