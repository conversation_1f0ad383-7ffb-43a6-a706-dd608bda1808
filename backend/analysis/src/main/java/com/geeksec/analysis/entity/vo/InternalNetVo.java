package com.geeksec.analysis.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description： 内网IP展示VO
 */
@Data
@TableName("tb_internal_net")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class InternalNetVo {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty(value = "id")
    private Integer id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    @JsonProperty(value = "task_id")
    private Integer taskId;

    /**
     * 内网IP地址
     */
    @TableField("inter_ip")
    @JsonProperty(value = "inter_ip")
    private String interIp;

    /**
     * 子网掩码
     */
    @TableField("ip_mask")
    @JsonProperty(value= "ip_mask")
    private String ipMask;

    /**
     * mac地址
     */
    @TableField("mac")
    @JsonProperty(value= "mac")
    private String mac;

    /**
     * 记录创建时间
     */
    @TableField("created_time")
    @JsonProperty(value = "created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    /**
     * 最后修改时间
     */
    @TableField("last_modified_time")
    @JsonProperty(value = "last_modified_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastModifiedTime;

}
