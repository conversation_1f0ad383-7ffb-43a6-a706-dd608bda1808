package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_danger_information")
public class DangerInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 目标key
     */
    @TableField("information")
    private String information;

    /**
     * 目标类型
     */
    @TableField("information_type")
    private Integer informationType;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 标签
     */
    @TableField("danger_label")
    private String dangerLabel;

    /**
     * 生效时间
     */
    @TableField("time")
    private Integer time;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Integer createdTime;


}
