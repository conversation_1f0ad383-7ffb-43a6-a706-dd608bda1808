package com.geeksec.analysis.dao;

import com.geeksec.analysis.entity.DownloadTask;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.TbTaskAnalysis;
import com.geeksec.analysis.entity.TbUseIpPosition;
import com.geeksec.analysis.entity.vo.ProtocolVo;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
public interface ThSessionDao {

    /**
     * 按Task id查task
     *
     * @param taskIdList task id
     * @return TbTaskAnalysis
     */
    List<TbTaskAnalysis> listTask(List<Integer> taskIdList);

    /**
     * 查询所有task
     *
     * @return TbTaskAnalysis
     */
    List<TbTaskAnalysis> listTaskAll(@Param("userId")Integer userId);

    /**
     * 按 ip查地址
     *
     * @param ipList ip
     * @return TbUseIpPosition
     */
    List<TbUseIpPosition> listIpPosition(List<String> ipList);

    /**
     * 按 tag id 查 tag
     *
     * @param tagList tag id
     * @return TbTagInfo
     */
    List<TbTagInfo> listTagInfo(List<Integer> tagList);

    /**
     * 查当前产品 tag 详细信息
     *
     * @return TagLibraryVo
     */
    List<TagLibraryVo> listTagAttribute(@Param("shieldProType") Integer shieldProType);

    /**
     * 查询所有标签信息
     */
    List<TagLibraryVo> listAllTagAttribute();

    /**
     * 查协议
     *
     * @return 协议
     */
    List<ProtocolVo> listProtocol(int type);

    /**
     * ES字段查询
     *
     * @return ES字段的JSON字符串表示形式
     */
    String getEsField();

    /**
     * 将taskId 对应的记录改为删除状态并删除对应数据
     *
     * @param condition
     */
    void deletePcapDownloadRecordAndData(Integer condition);

    /**
     * 仅删除记录
     *
     * @param condition
     */
    void deletePcapDownloadRecord(Integer condition);

    /**
     * 获取详情记录
     *
     * @param condition 条件
     * @return 任务详情
     */
    DownloadTask getTaskRecord(Integer condition);
}
