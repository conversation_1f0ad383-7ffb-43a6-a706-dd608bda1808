package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.vo.MacCommunicationVo;
import com.geeksec.analysis.entity.vo.MacVo;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.MacCommunicationService;
import com.geeksec.analysis.utils.ESQueryUtil;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Author: GuanHao
 * @Date: 2022/7/25 17:13
 * @Description： <Functions List>
 */
@Service
@DS("nta-db")
public class MacCommunicationServiceImpl implements MacCommunicationService {
    private static final Logger LOG = LoggerFactory.getLogger(MacCommunicationServiceImpl.class);

    @Value("${elasticsearch.es_connect_index}")
    private String esConnectIndex;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Autowired
    EsearchService esearchService;

    @Autowired
    private AggrTargetService aggrTargetService;

    @Override
    public ResultVo macList(AnalysisBaseCondition condition) {
        LOG.info("开始查询Mac通讯列表，condition->{}", condition);
        long startTime = System.currentTimeMillis() / 1000;
        HashMap<String, Object> result = new HashMap<String, Object>(2);


        ResultVo errorVo = ESQueryUtil.checkParam(condition);
        if (ObjectUtils.isNotEmpty(errorVo)) {
            return errorVo;
        }

        // 查询ES Index。
        List<String> index = queryFirstIndexName(condition);
        if (ObjectUtils.isEmpty(index) || index.size() == 0) {
            LOG.error("ES当前查询数据为空");
            return ResultVo.success("当前查询数据为空");
        }

        // 查Ids
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        SearchRequest esRequest = new SearchRequest(index.toArray(new String[]{}));
        BoolQueryBuilder boolQueryBuilder = ESQueryUtil.query(condition);
        boolQueryBuilder = aggrTargetService.getTargetTagCondition(condition, "ALL", boolQueryBuilder);

        // 如果没有对应的实体标签结果    则返回空
        if (boolQueryBuilder == null) {
            return ResultVo.success("当前Mac通讯列表查询为空");
        }

        List<String> idList = getEsIds(boolQueryBuilder, index);
        if (CollUtil.isEmpty(idList)) {
            return ResultVo.success(result);
        }

        if (CollectionUtils.isEmpty(idList)) {
            result.put("communication", new ArrayList<>());
            result.put("mac", new ArrayList<>());
            return ResultVo.success(result);
        } else {
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("_id", idList));
            sourceBuilder.query(boolQueryBuilder);
        }

        // 聚合 sMac 与 dMac两个字段
        Script script = new Script("doc['sMac'].value +'####'+ doc['dMac'].value");
        TermsAggregationBuilder macAgg = AggregationBuilders.terms("Mac").script(script).size(100000);

        // 默认降序，是否升序？
        Boolean asc = condition.getAsc();
        if (ObjectUtils.isNotEmpty(asc) && asc) {
            macAgg.order(BucketOrder.aggregation("TotalPacketNum", true));
        } else {
            macAgg.order(BucketOrder.aggregation("TotalPacketNum", false));
        }

        // 求TotalPacketNum的和
        SumAggregationBuilder totalPacketNum = AggregationBuilders.sum("TotalPacketNum").field("TotalPacketNum");
        macAgg.subAggregation(totalPacketNum);

        sourceBuilder.aggregation(macAgg);

        // 只返回统计结果及字段
        sourceBuilder.size(0);
        esRequest.source(sourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(esRequest);

        // 取值
        List<? extends Terms.Bucket> buckets;
        try {
            Terms mac = searchResponse.getAggregations().get("Mac");
            buckets = mac.getBuckets();
        } catch (Exception e) {
            LOG.warn("返回数据为空或ES桶转换失败！");
            throw new GkException(GkErrorEnum.ES_AGGR_ERROR);
        }

        // 取线段，仅取50条
        ArrayList<MacCommunicationVo> mcvList = new ArrayList<MacCommunicationVo>(50);
        HashMap<String, MacVo> mvMap = new HashMap<>(70);
        for (Terms.Bucket bucket : buckets) {
            MacCommunicationVo mcv = new MacCommunicationVo();
            String keyS = bucket.getKeyAsString();
            String[] macs = keyS.split("####");

            if (macs.length != 2) {
                continue;
            }

            // sMac
            String sMac = macs[0];
            // dMac
            String dMac = macs[1];
            Sum aggregationSum = bucket.getAggregations().get("TotalPacketNum");
            Double packetNum = aggregationSum.getValue();

            // 仅仅取50条
            if (mcvList.size() <= 50) {
                // 添加线段
                mcv.setSMac(sMac);
                mcv.setDMac(dMac);
                mcv.setTotalPacketNum(packetNum);
                mcvList.add(mcv);

                // 添加端点
                MacVo mac1 = mvMap.getOrDefault(sMac, new MacVo(sMac));
                mac1.setTotalPacketNum(mac1.getTotalPacketNum() + packetNum);
                mvMap.put(sMac, mac1);

                MacVo mac2 = mvMap.getOrDefault(dMac, new MacVo(dMac));
                mac2.setTotalPacketNum(mac2.getTotalPacketNum() + packetNum);
                mvMap.put(dMac, mac2);
            } else {
                // 累加50条以外，但又存在于端点内的值
                if (mvMap.containsKey(sMac)) {
                    MacVo macVo = mvMap.get(sMac);
                    macVo.setTotalPacketNum(macVo.getTotalPacketNum() + packetNum);
                }
                if (mvMap.containsKey(dMac)) {
                    MacVo macVo = mvMap.get(dMac);
                    macVo.setTotalPacketNum(macVo.getTotalPacketNum() + packetNum);
                }
            }

        }

        // 返回值
        result.put("communication", mcvList);
        result.put("mac", mvMap.values());

        LOG.info("查询结束，用时：{}秒。", System.currentTimeMillis() / 1000 - startTime);
        return ResultVo.success(result);
    }


    /**
     * 查询ID
     *
     * @param queryBuilder
     * @param indexNames
     * @return
     */
    private List<String> getEsIds(BoolQueryBuilder queryBuilder, List<String> indexNames) {
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        countRequest.source(aggrSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        if (count == 0) {
            return new ArrayList<>();
        }
        // 聚合范围100W
        Integer limit = esLimit;
        if (count < esLimit) {
            //设置循环参数
            limit = (int) count;
        }
        SearchRequest idsSearchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        idsSearchRequest.source(aggrSourceBuilder);
        ResultVo<List<String>> esIds = esearchService.getEsIds(10000, limit, queryBuilder, indexNames,
                "CreateTime", "_id");
        return esIds.getData();
    }

    /**
     * es_index下获取索引集
     *
     * @param condition
     * @return
     */
    private List<String> queryFirstIndexName(AnalysisBaseCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("es_index");
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("index.keyword", esConnectIndex));
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        //es_index的时间范围特殊
        ESQueryUtil.delEsIndexTime(boolQueryBuilder, timeRange.getLeft(), timeRange.getRight());
        List<Integer> taskId = condition.getTaskId();
        if (taskId != null && taskId.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("task", taskId));
        }
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);
        //这里判断返回值 esResultMapList

        Map<String, String> map = new HashMap<>();
        List<String> indexNames = new ArrayList<>();
        if (esResultMapList.size() > 1000) {
            for (Map<String, Object> esResultMap : esResultMapList) {
                String index = esResultMap.get("index").toString();
                String[] sliV = index.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            return indexNames;
        }
        for (Map<String, Object> esResultMap : esResultMapList) {
            String index = esResultMap.get("index").toString();
            indexNames.add(index);
        }
        return indexNames;
    }

}
