package com.geeksec.analysis.service;

import com.geeksec.analysis.condition.ModelCondition;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description：模型管理服务层
 */
public interface ModelService {

    /**
     * 根据条件查询模型列表
     * @param condition
     * @return
     */
    HashMap<String, Object> getModelInfolList(ModelCondition condition);

    /**
     * 模型状态开关切换
     * @param modelId
     * @param state
     * @return
     */
    Boolean updateModelState(Integer modelId, Integer state);
}
