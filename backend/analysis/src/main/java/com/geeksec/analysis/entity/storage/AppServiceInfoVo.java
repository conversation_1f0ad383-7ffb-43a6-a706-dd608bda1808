package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：应用服务展示实体VO
 */
@Data
public class AppServiceInfoVo {
    /**
     * 应用服务key
     */
    @JsonProperty(value = "service_key")
    private String serviceKey;

    /**
     * ip地址
     */
    @JsonProperty(value = "ip_addr")
    private String ipAddr;

    /**
     * 应用名称
     */
    @JsonProperty(value = "app_name")
    private String appName;

    /**
     * 服务端口
     */
    @JsonProperty(value = "d_port")
    private Long dPort;

    /**
     * IP协议
     */
    @JsonProperty(value = "ip_pro")
    private String ipPro;

}
