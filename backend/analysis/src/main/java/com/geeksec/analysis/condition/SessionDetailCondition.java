package com.geeksec.analysis.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：会话详情查询条件
 */
@Data
public class SessionDetailCondition {

    /**
     * 详情类型
     */
    @JsonProperty(value = "sub_type")
    private String subType;

    /**
     * 协议类型
     */
    @JsonProperty(value = "app_name")
    private String appName;

    /**
     * 会话归属ES索引
     */
    @JsonProperty(value = "es_index")
    private String esIndex;

    /**
     * 查询字段
     */
    @JsonProperty(value = "search")
    private String search;

    /**
     * 展示条数
     */
    @JsonProperty("limit")
    private Integer limit;

    /**
     * 排序字段
     */
    @JsonProperty("order_field")
    private String orderField;

    /**
     * 升序降序 desc/asc
     */
    @JsonProperty("order_type")
    private String orderType;

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * Hkey
     */
    @JsonProperty("Hkey")
    private String hKey;

}
