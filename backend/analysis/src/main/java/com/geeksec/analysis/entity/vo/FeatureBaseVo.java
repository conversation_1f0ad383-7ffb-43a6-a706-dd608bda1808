package com.geeksec.analysis.entity.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class FeatureBaseVo {

    /**
     * 规则ID
     */
    @JSONField(name="APPID")
    private Integer ruleId;

    /**
     * 规则级别
     */
    @JSONField(name="Level")
    private Integer ruleLevel;

    /**
     * 规则名称
     */
    @JSONField(name="Name")
    private String ruleName;

    /**
     * 规则描述
     */
    //@JSONField(serialize = false)
    private String ruleDesc;

    /**
     * 采集模式
     */
    @JSONField(name="Trance")
    private Integer captureMode;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    //@JSONField(serialize = false)
    private Integer ruleFamily;

    /**
     * 日志响应 pb留存  0保留  1丢弃
     */
    @JSONField(name="PbDrop")
    private Integer pbDrop;

    /**
     * 流量响应 pcap留存 0保留  1丢弃
     */
    @JSONField(name="PcapDrop")
    private Integer pcapDrop;

    @JSONField(name="BytePs")
    private Long bytePs;

    /**
     * 存储大小，负数不限
     */
    @JSONField(name="SaveBytes")
    private Long saveBytes;
    /**
     * 开启动态库响应
     */
    //@JSONField(serialize = false)
    private Integer libRespondOpen;

    /**
     * 库路径
     */
    //@JSONField(serialize = false)
    private String libRespondLib;

    /**
     * 库配置路径
     */
    //@JSONField(serialize = false)
    private String libRespondConfig;

    //@JSONField(serialize = false)
    private Long libRespondPktNum;

    /**
     * so文件base64字符串
     */
    private String libDataSo;

    /**
     * conf文件base64字符串
     */
    private String libDataConf;
}
