package com.geeksec.analysis.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class OfflineTaskBatchDto {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 批次类型（1-服务器数据；2-数据上传；）
     */
    @JsonProperty("batch_type")
    private Integer batchType;

    /**
     * 全流量留存，ON启用，OFF停用
     */
    @JsonProperty("fullflow_state")
    private String fullflowState;

    /**
     * 流量日志留存，ON启用，OFF停用
     */
    @JsonProperty("flowlog_state")
    private String flowlogState;

    /**
     * 数据描述
     */
    @JsonProperty("batch_description")
    private String batchDescription;

    /**
     * 文件路径列表
     */
    @JsonProperty("file_path_list")
    private List<OfflineFilePathDto> filePathList;

}
