package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.QueryHistory;
import org.apache.ibatis.annotations.Update;

/**
* <AUTHOR>
* @description 针对表【tb_query_history(查询历史记录表)】的数据库操作Mapper
* @createDate 2023-05-12 11:47:24
* @Entity generator.com.geeksec.TbQueryHistory
*/
public interface QueryHistoryDao extends BaseMapper<QueryHistory> {

    /**
     * 删除所有的检索记录
     */
    @Update("truncate table tb_query_history")
    void deleteAll();
}




