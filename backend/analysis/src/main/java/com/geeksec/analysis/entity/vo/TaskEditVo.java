package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 前端交互编辑任务的一些字段展示
 */
@Data
public class TaskEditVo {
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    /**
     * 描述
     */
    @JsonProperty("batch_remark")
    private String batchRemark;

    /**
     * 全流量留存 ON  OFF
     */
    @JsonProperty("fullflow_state")
    private String fullflowState;

    /**
     * 会话元数据保留 0否 1是
     */
    @JsonProperty("full_flow_should_log_def")
    private Integer fullFlowShouldLogDef;

    /**
     * 协议元数据保留 0否 1是
     */
    @JsonProperty("parse_proto_should_log_def")
    private Integer parseProtoShouldLogDef;

    /**
     * 协议元数据 集合
     */
    @JsonProperty("parse_proto_should_log_def_list")
    private List<Integer> parseProtoShouldLogDefList;

    /**
     * DDos数据留存 0否 1是
     */
    @JsonProperty("ddos_state")
    private Integer dDosState;

    /** 0代表历史任务，1代表当前任务 2 代表挂起的任务, */
    @JsonProperty("task_state")
    private Integer taskState;

    @JsonProperty("netflow_vos")
    private List<NetworkFlowVo> netflowVos;
}
