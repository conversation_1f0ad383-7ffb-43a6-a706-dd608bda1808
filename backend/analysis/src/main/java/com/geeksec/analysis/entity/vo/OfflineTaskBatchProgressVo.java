package com.geeksec.analysis.entity.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 离线任务批次进度
 */
@Data
public class OfflineTaskBatchProgressVo {

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    /**
     * 批次状态（1-等待导入；2-正在导入；3-导入完成；）
     */
    @JsonProperty("batch_status")
    private Integer batchStatus;

    /**
     * 进度
     */
    @JsonProperty("batch_progress")
    private Double batchProgress;

    /**
     * 导入结束时间
     */
    @JsonProperty("end_time")
    private Integer endTime;

}
