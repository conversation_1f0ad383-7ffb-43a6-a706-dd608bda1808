package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.analysis.entity.vo.FilterInfoVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel
@Data
public class FilterConfigInCondition {


    /**
     * 过滤规则ID
     */
    private Long id;

    /**
     * 任务id
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 任务批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    ///**
    // * 创建规则用户ID
    // */
    //@JsonProperty("user_id")

    /**
     * 过滤IP地址
     */
    @JsonProperty("ip")
    private String ip;

    /**
     * 规则JSON数据字符串
     */
    @JsonProperty("filter_json")
    private String filterJson;

    /**
     * 过滤规则对象信息
     */
    @JsonProperty("filter_info")
    private FilterInfoVo filterInfo;

    /**
     * 过滤规则对象：0表示端口，1表示IP-PRO，2表示网段
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * hash值
     */
    private String hash;

    //csv上传记录行号
    private Integer rowNo;
}
