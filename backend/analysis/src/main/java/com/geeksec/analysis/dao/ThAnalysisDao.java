package com.geeksec.analysis.dao;

import com.geeksec.analysis.entity.dict.*;
import com.geeksec.analysis.entity.session.SessionIdEntity;
import com.geeksec.analysis.entity.session.SessionTagEntity;
import com.geeksec.analysis.entity.session.TargetRemarkInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Mapper
public interface ThAnalysisDao {

    List<ValSetEntity> getCaptureMode();

    /**
     * 获取标签字典
     * @return
     */
    List<AnalysisTagInfoEntity> getTagInfo(@Param("proId") Integer proId);

    /**
     * 获取全部标签信息
     */
    List<AnalysisTagInfoEntity> getAllTagInfo();

    /**
     * 通过tag_id获取标签信息
     */
    List<AnalysisTagInfoEntity> getTagInfoByIds(@Param("labels") List<Integer> labels);

    /**
     * 获取规则字典
     * @return
     */
    List<RuleEntity> getRule();

    /**
     * 获取协议字典
     * @return
     */
    List<AppProValue> getAppProValueDict();

    List<AppProValue> getAppRuleDict();

    List<TagAttribute> getTagAttribute();

    List<SessionTagEntity> getSessionTag(@Param("lables") List<Integer> labels);

    /**
     * 查询单体会话Session
     * @param sessionId
     * @return
     */
    SessionIdEntity getSessionIdInfo(@Param("sessionId") String sessionId);

    /**
     * 查询Session在单体是否存在
     */
    Integer existSessionIdInfo(@Param("sessionId")String sessionId);

    /**
     * 根据TaskId获取任务名称
     * @param taskId
     * @return
     */
    String getTaskNameByTaskId(@Param("taskId") Integer taskId);

    /**
     * 创建单体SessionId实体
     * @param target
     * @param remark
     */
    void addSessionIdInfo(@Param("sessionId") String sessionId, @Param("remark") String remark);

    /**
     * 根据目标Key值获取对应标签
     * @param sessionId
     * @param targetType
     * @return
     */
    List<TargetRemarkInfo> getTargetRemarkByKey(@Param("targetKey") String targetKey, @Param("targetType") String targetType);

    /**
     * 添加单条目标详情备注
     * @param targetType
     * @param targetKey
     * @param remark
     */
    void addTargetRemark(@Param("targetType") String targetType, @Param("targetKey") String targetKey, @Param("remark") String remark);

    /**
     * 删除单条目标详情
     * @param remarkId
     */
    void deleteTargetRemarkById(@Param("remarkId") String remarkId);
}
