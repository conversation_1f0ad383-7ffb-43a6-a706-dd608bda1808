package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.FlowUpCondition;
import com.geeksec.analysis.entity.condition.TaskAnalysisCondition;
import com.geeksec.analysis.entity.condition.TaskConfigEditCondition;
import com.geeksec.analysis.entity.vo.TaskEditVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public interface TaskConfigService {

    /**
     * 查询任务配置信息
     * @param taskId
     * @return
     */
    Map<String,Object> configTaskInfo(Integer taskId);

    /**
     * 修改任务配置
     * @param condition
     */
    ResultVo configTaskEdit(TaskConfigEditCondition condition);

    /**
     * 查看任务基本情况
     * @return
     */
    List<TaskEditVo> taskEditVoList();

    /**
     * 更新网口
     * @param conditions
     * @return
     */
    ResultVo updateAnalysis(List<TaskAnalysisCondition> conditions);

    /**
     * 网口名字更新
     * @param conditions
     * @return
     */
    ResultVo updateFlowName(List<FlowUpCondition> conditions);
}
