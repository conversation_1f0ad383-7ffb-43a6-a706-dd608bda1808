package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.condition.OfflineTaskQueryCondition;
import com.geeksec.analysis.entity.dto.OfflineTaskDeleteDto;
import com.geeksec.analysis.entity.dto.OfflineTaskDto;
import com.geeksec.analysis.entity.vo.OfflineTaskPageVo;
import com.geeksec.analysis.entity.vo.OfflineTaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskAnalysisDao  extends BaseMapper<TaskAnalysis> {


    /**
     * 校验端口是否合法
     * @param nets
     * @return
     */
    List<Integer> checkFlowId(@Param("nets") List<Integer> nets);

    /**
     * 更新网口
     * @param taskAnalysis
     * @return
     */
    Integer updateFlow(TaskAnalysis taskAnalysis);


    Integer getLastTaskId();

    Integer updateTask(OfflineTaskDto dto);

    Integer updateTaskId(@Param("id")Integer id);

    Integer deleteTask(OfflineTaskDeleteDto dto);

    List<OfflineTaskPageVo> pageTask(OfflineTaskQueryCondition condition);

    OfflineTaskVo getTask(@Param("taskId")Integer taskId);

    OfflineTaskVo getLastTask(@Param("userId")Integer userId);

    TaskAnalysis getTaskAnalysis(@Param("taskId")Integer taskId);

}
