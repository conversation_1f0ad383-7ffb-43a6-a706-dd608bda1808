package com.geeksec.analysis.entity.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FilterInfoVo {

    @JSONField(name = "IP")
    private String ip;

    /** 子网掩码 */
    @JsonProperty("subnet_mask")
    @JSONField(name = "NetMask")
    private String subnetMask;

    /**
     * IP-PRO协议号
     */
    @JsonProperty("ip_pro")
    @JSONField(name = "IPPro")
    private List<Integer> ipPro;

    /**
     * IP-PRO类型
     */
    @JsonProperty("ip_pro_type")
    @JSONField(name = "IPPro_Type")
    private String ipProType;

    /**
     * TCP端口选择
     */
    @JsonProperty("tcp_port")
    @JSONField(name = "TCPPort")
    private List<Integer> tcpPort;

    @JsonProperty("tcp_port_type")
    @JSONField(name = "TCPPort_Type")
    /** Select：正选  Invert：反选  */
    private String tcpPortType;

    @JsonProperty("udp_port")
    @JSONField(name = "UDPPort")
    private List<Integer> udpPort;

    @JsonProperty("udp_port_type")
    @JSONField(name = "UDPPort_Type")
    /** Select：正选  Invert：反选  */
    private String udpPortType;

}
