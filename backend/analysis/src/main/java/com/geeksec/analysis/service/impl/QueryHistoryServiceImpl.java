package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.dao.QueryHistoryDao;
import com.geeksec.analysis.entity.QueryHistory;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.service.QueryHistoryService;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.authentication.service.UserService;
import com.geeksec.entity.common.BaseCondition;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class QueryHistoryServiceImpl implements QueryHistoryService {
    private static final Logger logger = LoggerFactory.getLogger(QueryHistoryServiceImpl.class);

    // 创建查询历史记录时的锁
    private final Lock lock = new ReentrantLock();

    @Autowired
    UserService authService;

    @Autowired
    private QueryHistoryDao queryHistoryDao;

    @Autowired
    private TokenService tokenService;

    @Override
    public void createQueryHistory(AnalysisBaseCondition condition, List<Integer> sourceList, Long costTime, long count) {
        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();
        // 如果当前不存在任何查询条件，不进行检索历史的记录
        if (CollectionUtil.isEmpty(queryObList)) {
            return;
        }
        // 查询来源同样需要记录
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            String queryStr = mapper.writeValueAsString(queryObList);
            String queryCn = mapper.writeValueAsString(condition.getQueryCn());
            createRecord(JSON.toJSONString(sourceList), queryStr, queryCn, costTime, count);
        } catch (JsonProcessingException e) {
        }
    }

    @Override
    public ResultVo searchQueryHistoryList(BaseCondition condition) {

        Map<String, Object> resultMap = new HashMap<>();

        logger.info("查询当前所有的检索历史记录:condition->{}", condition);

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        try {
            QueryWrapper<QueryHistory> queryWrapper = new QueryWrapper();
            queryWrapper.orderByDesc("query_time");
            PageInfo<QueryHistory> pageInfo = PageHelper.startPage(currentPage, pageSize)
                    .doSelectPageInfo(() -> queryHistoryDao.selectList(queryWrapper));

            List<QueryHistory> resultData = pageInfo.getList();
            resultMap.put("data", resultData);
            resultMap.put("total", pageInfo.getTotal());
        } catch (Exception e) {
            logger.error("查询检索历史记录列表失败,condition: {},error:{}", condition, e);
            throw new GkException(GkErrorEnum.QUERY_HISTORY_FAILED);
        }

        return ResultVo.success(resultMap);
    }

    @Override
    public ResultVo deleteQueryHistory(List<Integer> ids, Boolean clear) {
        logger.info("删除查询历史记录,ids->{}，是否为全量删除:{}", clear);
        try {
            if (clear) {
                // 当前为清空检索记录
                queryHistoryDao.deleteAll();
            } else {
                // 批量删除模板记录
                queryHistoryDao.deleteBatchIds(ids);
            }
        } catch (Exception e) {
            logger.error("批量删除查询历史失败,ids -> {},是否为清空表 -> {}", ids, clear);
            throw new GkException(GkErrorEnum.MYSQL_EXECUTE_ERROR);
        }

        return ResultVo.success("删除查询历史成功");
    }

    // 通过查询类型，创建对应的查询历史记录
    @Transactional(rollbackFor = MybatisPlusException.class)
    public void createRecord(String type, String queryStr, String queryCn, Long costTime, long count) {
        try {
            // 当前查询用户
            String token = MDC.get("token");
            UserInfoVo userInfoVo = (UserInfoVo) authService.getLoginUserInfo(token).getData();
            String userName = userInfoVo.getUsername();

            // 判断当前查询条件有无进行查询过,有的话，比对查询次数，若次数大于等于1，则查询次数 + 1
            QueryHistory existHistory = new QueryHistory();
            existHistory.setUserId(Math.toIntExact(userInfoVo.getId()));
            existHistory.setUserName(userName);
            existHistory.setSource(type);
            existHistory.setConditionText(queryStr);
            existHistory.setQueryCn(queryCn);
            existHistory.setHitCount(count);
            existHistory.setCostTime(costTime);
            existHistory.setQueryCount(1L);
            existHistory.setQueryTime(new Date());
            queryHistoryDao.insert(existHistory);
            // 在这里进行数据库的存储以防用户点的过快进行暂停 同时创建多个同查询条件的查询历史
            //logger.info("创建{}查询历史成功,condition->{}", type, queryStr);
        } catch (Exception e) {
            logger.error("创建{}查询历史失败,error->{}", type, e.getMessage());
        }
    }
}
