package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FilterRuleCondition extends BaseCondition {

    @ApiModelProperty("任务ID")
    @JsonProperty("task_id")
    private Integer taskId;

    @ApiModelProperty("批次ID")
    @JsonProperty("batch_id")
    private Integer batchId;

    @ApiModelProperty("过滤规则：0表示保留，1表示丢弃")
    private Integer state;
    @JsonProperty("order_field")
    @ApiModelProperty("排序字段，可填入id,created_time,updated_time")
    private String orderField;
}
