package com.geeksec.analysis.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
@Mapper
public interface AtlasDao {

    /**
     * 根据查询条件创建查询历史
     */
    void craeteAtlasRecord(@Param("atlasType") Integer atlasType,@Param("atlasCondition") String atlasCondition);

    /**
     * 查询图探索查询记录
     * @param atlasType
     * @param offset
     * @param pageSize
     * @return
     */
    List<Map<String, Object>> queryAtlasHistory(@Param("atlasType") Integer atlasType, @Param("offset") Integer offset, @Param("limit") Integer pageSize,@Param("orderBy") String orderBy);

    /**
     * 计算目前的探索记录条数
     * @param atlasType
     * @return
     */
    Integer countAtlasHistory(@Param("atlasType") Integer atlasType);

    /**
     * 删除图探索查询记录（选中or单条id）
     */
    void deleteAtlasHistory(@Param("list") List<Integer> idList);

    /**
     * 清空表，删除所有图探索查询记录
     */
    void deleteAllAtlasHistory();
}
