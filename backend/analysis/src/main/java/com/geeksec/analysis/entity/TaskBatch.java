package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_task_batch")
public class TaskBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //自增批次ID ---批次信息表---
     */
    @TableId(value = "batch_id", type = IdType.AUTO)
    private Integer batchId;

    /**
     * //任务ID
     */
    @TableField("task_id")
    private Integer taskId;

    /**
     * 任务类型（1-在线任务；2-离线任务；）
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 批次类型（1-服务器数据；2-数据上传；）
     */
    private Integer batchType;

    /**
     * //批次描述
     */
    @TableField("batch_remark")
    private String batchRemark;

    /**
     * //全流量留存，ON启用，OFF停用
     */
    @TableField("fullflow_state")
    private String fullflowState;

    /**
     * //流量日志留存，ON启用，OFF停用
     */
    @TableField("flowlog_state")
    private String flowlogState;

    /**
     * //导入的数据类型，1（pcap），2（pb），3（探针数据）
     */
    @TableField("data_type")
    private Integer dataType;

    /**
     * //MAC日志留存，ON启用，OFF停用
     */
    @TableField("topology_state")
    private String topologyState;

    /**
     * //导入开始时间
     */
    @TableField("begin_time")
    private Integer beginTime;

    /**
     * //导入结束时间
     */
    @TableField("end_time")
    private Integer endTime;

    /**
     * //数据开始时间
     */
    @TableField("data_begin_time")
    private Integer dataBeginTime;

    /**
     * //数据结束时间
     */
    @TableField("data_end_time")
    private Integer dataEndTime;

    /**
     * //导入数据量
     */
    @TableField("batch_bytes")
    private Long batchBytes;

    /**
     * //导入会话量
     */
    @TableField("batch_session")
    private Integer batchSession;

    /**
     * //批次的告警量
     */
    @TableField("batch_alarm")
    private Integer batchAlarm;

    /**
     * //高危目标数量
     */
    @TableField("importrarnt_target")
    private Integer importrarntTarget;

    /**
     * 过滤数据量
     */
    @TableField("filter_data_total")
    private Long filterDataTotal;

    /**
     * 采集规则命中数据量
     */
    @TableField("rule_hits_data_total")
    private Long ruleHitsDataTotal;

    /**
     * 白名单过滤量
     */
    @TableField("whitelist_filter_total")
    private Long whitelistFilterTotal;

    /**
     * 任务状态（1-等待导入；2-正在导入；3-导入完成；）
     */
    @TableField("batch_status")
    private Integer batchStatus;

    /**
     * 导入进度
     */
    @TableField("batch_progress")
    private Double batchProgress;

    /**
     * //当前任务批次数据路径
     */
    @TableField("batch_dir")
    private String batchDir;

    /**
     * //批次数据报告生成路径
     */
    @TableField("report_path")
    private String reportPath;

    /**
     * 筛选条件
     */
    @TableField("screening_conditions")
    private String screeningConditions;

    /**
     * //每线程每秒钟平均可写pcap字节数
     */
    @TableField("avg_byte_pt_ps")
    private Integer avgBytePtPs;

    /**
     * //每线程每秒钟最多可写pcap字节数
     */
    @TableField("max_byte_pt_ps")
    private Integer maxBytePtPs;

    /**
     * //批次地址
     */
    @TableField("addr")
    private String addr;

    /**
     * //0代表初始化状态。，1代表 非初始化状态     更新标的时候更新
     */
    @TableField("task_update")
    private Integer taskUpdate;

    /**
     * 开启的插件iD默认值 ， 1 为开启 0  位关闭 
     */
    @TableField("full_flow_should_log_def")
    private Integer fullFlowShouldLogDef;

    /**
     * 开启的插件iD默认值 ， 1 为开启 0  位关闭 
     */
    @TableField("parse_proto_should_log_def")
    private Integer parseProtoShouldLogDef;

    /**
     * 批次值 ， 1 为正在运行 0  位关闭 
     */
    @TableField("state")
    private Integer state;


}
