package com.geeksec.analysis.service;

import com.geeksec.entity.common.ResultVo;

/**
 * @author: jerryzhou
 * @date: 2024/1/29 18:10
 * @Description:
 **/
public interface CertLogTemplateService {
    /**
     * 查询当前用户的证书详情日志模板
     *
     * @param userId
     * @return
     */
    ResultVo getUserTemplate(Integer userId);

    /**
     * 修改指定用户的证书元数据展示模板
     *
     * @param userId
     * @param key
     * @param value
     * @return
     */
    ResultVo modifyUserTemplate(Integer userId, String key, String value);
}
