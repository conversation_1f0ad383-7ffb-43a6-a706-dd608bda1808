package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class TagSearchCondition extends BaseCondition {

    /**
     * 标签名称
     */
    @JsonProperty("tag_text")
    private String tagText;

    /**
     * 标签目标类型(0 ip目标，1 端口目标，2 应用目标，3 域名目标，4 证书目标，5 MAC目标，6 连接目标(会话) , 7 指纹 9999 所有)
     */
    @JsonProperty("tag_target_type")
    private Integer tagTargetType;

    /**
     * 标签细分类类型ID
     * 1 威胁，2 功能描述，3 合法性 ，4 行为描述 ，5 APT，6 远程控制，7 基础属性，8 指纹描述，9 代理，10 加密流量检测
     */
    @JsonProperty("tag_attribute_id")
    private Integer tagAttributeId;

    /**
     * 全量展示 or 列表形式
     */
    @JsonProperty("search_type")
    private String searchType;

    @JsonProperty("white_weight")
    private List<Integer> whiteWeight;

    @JsonProperty("black_weight")
    private List<Integer> blackWeight;

    private Integer userId;
}
