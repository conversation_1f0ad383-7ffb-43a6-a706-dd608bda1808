package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class LabelAggVo {


    private Integer total;

    /** 会话数量排序 */
    @JsonProperty("cnt_order_asc")
    private List<LabelInfoVo> cntOrderAsc;

    /** 黑名单排序 */
    @JsonProperty("black_order_asc")
    private List<LabelInfoVo> blackOrderAsc;

    /** 会话数量排序 */
    @JsonProperty("cnt_order_desc")
    private List<LabelInfoVo> cntOrderDesc;

    /** 黑名单排序 */
    @JsonProperty("black_order_desc")
    private List<LabelInfoVo> blackOrderDesc;

    @Data
    public static class LabelInfoVo{
        @JsonProperty("tag_id")
        public Integer tagId;

        /** 连接数 */
        public Long cnt;

        @JsonProperty("black_list")
        public Integer blackList;

        @JsonProperty("white_list")
        public Integer whiteList;

        @JsonProperty("tag_text")
        /** 标签内容 */
        public String tagText;
    }


}
