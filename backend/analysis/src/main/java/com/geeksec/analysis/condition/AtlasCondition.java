package com.geeksec.analysis.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class AtlasCondition extends BaseCondition {

    /**
     * 查询节点VID
     */
    @JsonProperty(value = "search")
    private String search;

    /**
     * 边类型
     */
    @JsonProperty(value = "edge_type")
    private String edgeType;

    /**
     * 节点类型
     */
    @JsonProperty(value = "tag_type")
    private String tagType;

    /**
     * 正/反向(true 正向 false 反向)
     */
    @JsonProperty(value = "direct")
    private Boolean direct;

    /**
     * 排序字段
     */
    @JsonProperty(value = "order_field")
    private String orderField;
}
