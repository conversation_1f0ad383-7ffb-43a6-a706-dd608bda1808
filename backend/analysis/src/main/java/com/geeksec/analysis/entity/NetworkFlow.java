package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_network_flow")
public class NetworkFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //自增ID ---任务信息表---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * //任务ID
     */
    @TableField("pcie_id")
    private String pcieId;

    /**
     * //任务名称
     */
    @TableField("flow_name")
    private String flowName;

    /**
     * //任务备注
     */
    @TableField("network_type")
    private String networkType;

    /**
     * //保存任务的时间
     */
    @TableField("created_time")
    private Integer createdTime;

    /**
     * //0代表历史任务，1代表当前任务
     */
    @TableField("state")
    private Integer state;


}
