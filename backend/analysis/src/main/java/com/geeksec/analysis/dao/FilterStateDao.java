package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.FilterState;
import com.geeksec.analysis.entity.condition.FilterRuleCondition;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
public interface FilterStateDao extends BaseMapper<FilterState> {

    Integer updateStateByTaskId(FilterRuleCondition condition);

    FilterState getFilterStateByTaskId(@Param("taskId") Integer taskId);

    Integer createFilterState(@Param("task_id") Integer taskId, @Param("state") int state);
}
