package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class DomainListVo {

    private String domain;

    private long count;

    private Set<Long> labels;

    @JsonProperty("fLabels")
    private Set<Long> fLabels;

    private Integer blackList;

    private Long firstTime;

    private Long lastTime;

    private String whoIs;

    private String fDomain;

    /**
     * 反查ip  parse_to cname_result
     */
    private Set<String> relatedIps;

    /**
     * CName指向该域名数量
     */
    private int cnameToOther;

    /**
     * CName查询该域名数量
     */
    private int cnameToMe;

    /**
     * 兄弟域名数量
     */
    private int brotherNum;

    /**
     * 访问ip热度
     */
    private int ipHeart;

    /**
     * 回应类型
     */
    private Set<String> respondTypes = new HashSet<>();

    /**
     * 出现位置
     */
    private Location location;

    /**
     * 告警数量
     */
    private Long alarmNum = 0L;

    @Data
    public static class Location {
        private Boolean dns = false;
        private Boolean ssl = false;
        private Boolean http = false;
        private Boolean cert = false;
    }
}
