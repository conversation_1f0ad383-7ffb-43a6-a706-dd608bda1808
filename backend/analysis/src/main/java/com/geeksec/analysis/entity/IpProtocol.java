package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(" ip_protocol")
public class IpProtocol implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 协议ID
     */
    @TableId(value = "pro_id")
    private String proId;

    /**
     * 协议名称
     */
    @TableField("protocol_type")
    private String protocolType;

    /**
     * 协议备注
     */
    @TableField("protocol_remark")
    private String protocolRemark;

}
