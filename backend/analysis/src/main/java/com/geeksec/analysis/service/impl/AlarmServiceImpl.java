package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.dao.DownloadTaskDao;
import com.geeksec.analysis.dao.FeatureRuleDao;
import com.geeksec.analysis.dao.KnowledgeAlarmDao;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.entity.DownloadTask;
import com.geeksec.analysis.entity.FeatureRule;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.condition.AlarmCommonCondition;
import com.geeksec.analysis.entity.condition.AlarmListCondition;
import com.geeksec.analysis.entity.condition.AlarmStatusUpCondition;
import com.geeksec.analysis.entity.condition.DownloadPcapCondition;
import com.geeksec.analysis.entity.vo.AlarmTargetAggVo;
import com.geeksec.analysis.entity.vo.AlarmTypeAggVo;
import com.geeksec.analysis.entity.vo.KnowledgeAlarmVo;
import com.geeksec.analysis.entity.vo.KnowledgeTypeVo;
import com.geeksec.analysis.service.AlarmService;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.CommonRangeVo;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.BulkByScrollTask;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
@DS("nta-db")
public class AlarmServiceImpl implements AlarmService {

    @Autowired
    private EsearchService esearchService;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Value("${send-url.alarm_report_export}")
    private String alarmReportExprotUrl;

    private String alarmEsIndex = "alarm_*";

    @Autowired
    private KnowledgeAlarmDao knowledgeAlarmDao;

    @Autowired(required = false)
    DownloadTaskDao downloadTaskDao;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private FeatureRuleDao featureRuleDao;

    private static Map<Integer, KnowledgeTypeVo> alarnKnowledgeMap = new HashMap<>();

    @Autowired
    private TokenService tokenService;

    @PostConstruct
    @Override
    public void initKnowledgeType() {
        List<KnowledgeTypeVo> knowledgeTypes = knowledgeAlarmDao.getKnowledgeType();
        for (KnowledgeTypeVo knowledgeType : knowledgeTypes) {
            alarnKnowledgeMap.put(knowledgeType.getKnowledgeAlarmId(), knowledgeType);
        }
    }

    @Override
    public ResultVo<AlarmTargetAggVo> getAlarmTargetAgg(AlarmCommonCondition condition) {
        log.info("告警：指标信息，condition={}", condition);
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) return errorVo;
        List<Integer> taskIds = condition.getTaskIds();
        String index = Constants.ALARM;
        if (taskIds.size() >1) {
            index = index + "*";
        } else if (taskIds.size() == 1) {
            Integer taskId = taskIds.get(0);
            index = index + "_" + taskId + "*";
        }
        AlarmTargetAggVo resultVo = new AlarmTargetAggVo();

        //1.告警各等级  总数直接相加
        List<CommonRangeVo> levelRanges1 = getLevelRange();
        Long total = 0L;
        long t1 = System.currentTimeMillis();
        for (CommonRangeVo levelRange : levelRanges1) {
            CountRequest countRequestLevel = new CountRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            countRequestLevel.source(searchSourceBuilder);
            BoolQueryBuilder boolQueryBuilder = getCommonQueryBuilder(condition);
            searchSourceBuilder.query(boolQueryBuilder);
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_level").gte(levelRange.getLeft()).lte(levelRange.getRight()));
            CountResponse countResponse = esearchService.esSearchForCount(countRequestLevel);

            long count = countResponse.getCount();
            String key = levelRange.getKey();
            total += count;
            switch (key) {
                case "low":
                    resultVo.setLowLevel(count);
                    break;
                case "middle":
                    resultVo.setMiddleLevel(count);
                    break;
                case "high":
                    resultVo.setHighLevel(count);
                    break;
            }
        }
        long t2 = System.currentTimeMillis();
        log.info("告警指标-各等级count耗时t={}", (t2 - t1));
        resultVo.setAlarmCnt(total);
        //2.处理状态 0:未处理 1:确认 2:误报
        List<Integer> statusList = new ArrayList<>();
        statusList.add(0);
        statusList.add(1);
        statusList.add(2);
        for (Integer status : statusList) {
            CountRequest countRequestLevel = new CountRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            countRequestLevel.source(searchSourceBuilder);
            BoolQueryBuilder boolQueryBuilder = getCommonQueryBuilder(condition);
            searchSourceBuilder.query(boolQueryBuilder);
            boolQueryBuilder.must(QueryBuilders.termQuery("alarm_status", status));
            CountResponse countResponse = esearchService.esSearchForCount(countRequestLevel);
            long docCount = countResponse.getCount();
            switch (status) {
                case 0:
                    resultVo.setAlarmStatus0(docCount);
                    break;
                case 1:
                    resultVo.setAlarmStatus1(docCount);
                    break;
                case 2:
                    resultVo.setAlarmStatus2(docCount);
                    break;
            }
        }
        long t3 = System.currentTimeMillis();
        log.info("告警指标-各处理情况count耗时t={}", (t3 - t2));
        return ResultVo.success(resultVo);

    }

    private ResultVo checkParam(AlarmCommonCondition commonCondition) {
        List<Integer> taskIds = commonCondition.getTaskIds();
        if (taskIds == null || taskIds.size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {
                    String[] split = attackLevel.split("-");
                    try {
                        Integer left = Integer.valueOf(split[0]);
                        Integer right = Integer.valueOf(split[1]);
                        if (left > right || left < 61 || right > 100) {
                            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                        }
                    } catch (Exception e) {
                        throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                    }
                }
            }
        }

        return null;
    }

    private BoolQueryBuilder getCommonQueryBuilder(AlarmCommonCondition commonCondition) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long left = commonCondition.getLeft();
        Long right = commonCondition.getRight();
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("time");
        Boolean isTime = false;
        if (left != null && left > 0) {
            //gt  大于    这里只用流量的开始时间~
            timeRangeQuery.gte(left);
            isTime = true;
        }
        if (right != null && right > 0) {
            timeRangeQuery.lte(right);
            isTime = true;
        }
        if (isTime) {
            boolQueryBuilder.must(timeRangeQuery);
        }

        //告警名称
        List<Integer> alarmIds = commonCondition.getAlarmIds();
        if (alarmIds != null && alarmIds.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_knowledge_id", alarmIds));
        }
        //taskId
        List<Integer> taskIds = commonCondition.getTaskIds();
        boolQueryBuilder.must(QueryBuilders.termsQuery("task_id", taskIds));

        String targetName = commonCondition.getTargetName();
        if (StringUtils.isNotEmpty(targetName)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("targets.name.keyword", targetName));
        }

        //受害方 victim 下面的字段都查
        String victims = commonCondition.getVictim();
        if (StringUtils.isNotEmpty(victims)) {
            BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
            shouldBuild.should(QueryBuilders.termsQuery("victim.ip", victims));
            boolQueryBuilder.must(shouldBuild);
        }
        //攻击方:ip
        String attackerIp = commonCondition.getAttackerIp();
        if (StringUtils.isNotEmpty(attackerIp)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("attacker.ip", attackerIp));
        }
        //威胁等级
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            BoolQueryBuilder shouldBool = new BoolQueryBuilder();
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {

                    String[] split = attackLevel.split("-");
                    shouldBool.should(QueryBuilders.rangeQuery("attack_level").gte(split[0]).lte(split[1]));
                }
            }
            boolQueryBuilder.must(shouldBool);
        }
        //处理状态
        List<Integer> alarmStatusList = commonCondition.getAlarmStatusList();
        if (alarmStatusList != null && alarmStatusList.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_status", alarmStatusList));
        }

        return boolQueryBuilder;
    }

    private List<CommonRangeVo> getLevelRange() {
        List<CommonRangeVo> list = new ArrayList<>();

        CommonRangeVo low = new CommonRangeVo();
        low.setLeft(60);
        low.setRight(80);
        low.setKey("low");
        list.add(low);

        CommonRangeVo middle = new CommonRangeVo();
        middle.setLeft(81);
        middle.setRight(90);
        middle.setKey("middle");
        list.add(middle);

        CommonRangeVo high = new CommonRangeVo();
        high.setLeft(91);
        high.setRight(100);
        high.setKey("high");
        list.add(high);
        return list;
    }

    @Override
    public ResultVo getModelAlarmAttackChainAggr(AlarmCommonCondition condition) {

        log.info("告警：模型告警攻击链聚合，condition={}", condition);
        List<Integer> taskIds = condition.getTaskIds();
        if (taskIds == null || taskIds.size() == 0) {
            return ResultVo.success(new ArrayList<>());
        }
        String index = Constants.ALARM + "*";

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        long startTime = condition.getLeft();
        long endTime = condition.getRight();
        if (startTime != 0 && endTime != 0) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("time").gte(startTime).lte(endTime));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("alarm_type.keyword", "模型"));
        boolQueryBuilder.must(QueryBuilders.termsQuery("task_id", taskIds));
        AlarmTypeAggVo modelAggrVo = new AlarmTypeAggVo();
        modelAggrVo.setAlarmType("模型");
        modelAggrVo.setAlarmCnt(0);

        // 组装模型告警链路聚合结构
        TermsAggregationBuilder chainNameAggrBuilder = AggregationBuilders.terms("chain_name").field("attack_chain_name").size(100);
        TermsAggregationBuilder knowledgeIdAggBuilder = AggregationBuilders.terms("knowledge_id").field("alarm_knowledge_id").size(1000);

        SearchRequest searchRequest = new SearchRequest(index);
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(chainNameAggrBuilder.subAggregation(knowledgeIdAggBuilder));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);

        Aggregations aggregations = searchResponse.getAggregations();
        // 如果当前机器没有任何告警，则返回空
        if (aggregations == null){
            return ResultVo.success(new ArrayList<>());
        }
        ParsedStringTerms alarmChainTerms = aggregations.get("chain_name");
        List<? extends Terms.Bucket> chainBuckets = alarmChainTerms.getBuckets();

        List<AlarmTypeAggVo.AttackChain> chainList = new ArrayList<>();
        for (Terms.Bucket chainBucket : chainBuckets) {
            AlarmTypeAggVo.AttackChain attackChain = new AlarmTypeAggVo.AttackChain();
            attackChain.setAttackChainName(chainBucket.getKeyAsString());
            attackChain.setAttackChainCnt(chainBucket.getDocCount());

            // 子聚合（分告警类型）
            Aggregations subAggregations = chainBucket.getAggregations();
            ParsedLongTerms knowledgeIdTerms = subAggregations.get("knowledge_id");
            List<? extends Terms.Bucket> knowledgeBuckets = knowledgeIdTerms.getBuckets();


            List<AlarmTypeAggVo.AlarmKnowledge> knowledgeList = new ArrayList<>();
            for (Terms.Bucket knowledgeBucket : knowledgeBuckets) {
                AlarmTypeAggVo.AlarmKnowledge alarmKnowledge = new AlarmTypeAggVo.AlarmKnowledge();
                Integer knowledgeId = Integer.valueOf(knowledgeBucket.getKeyAsString());
                Long knowledgeCnt = knowledgeBucket.getDocCount();
                alarmKnowledge.setAlarmKnowledgeCnt(knowledgeCnt);
                alarmKnowledge.setAlarmKnowledgeId(knowledgeId);
                knowledgeList.add(alarmKnowledge);
            }
            attackChain.setAlarmKnowledgeList(knowledgeList);
            chainList.add(attackChain);
        }

        // 获取知识库名称
        List<KnowledgeAlarmVo> knowledgeAlarmList = getKnowledgeAlarmList();
        Map<Integer, String> map = new HashMap<>(knowledgeAlarmList.size());
        for (KnowledgeAlarmVo knowledgeAlarmVo : knowledgeAlarmList) {
            map.put(knowledgeAlarmVo.getId(), knowledgeAlarmVo.getAlarmName());
        }

        for (AlarmTypeAggVo.AttackChain attackChain : chainList) {
            List<AlarmTypeAggVo.AlarmKnowledge> alarmKnowledgeList = attackChain.getAlarmKnowledgeList();
            if (alarmKnowledgeList != null && alarmKnowledgeList.size() > 0) {
                for (AlarmTypeAggVo.AlarmKnowledge alarmKnowledge : alarmKnowledgeList) {
                    Integer id = alarmKnowledge.getAlarmKnowledgeId();
                    String name = map.get(id);
                    if (name != null) {
                        alarmKnowledge.setAlarmKnowledgeName(name);
                    } else {
                        alarmKnowledge.setAlarmKnowledgeName("错误的知识库id");
                    }
                }
            }
        }

        return ResultVo.success(chainList);
    }


    @Override
    public List<KnowledgeAlarmVo> getKnowledgeAlarmList() {
        Integer userId = tokenService.getUserInfoByToken();
        return knowledgeAlarmDao.getKnowledgeAlarmList(userId);
    }

    @Override
    public ResultVo<PageResultVo<Map<String, Object>>> getAlarmList(AlarmListCondition condition) {
        log.info("告警：列表，condition={}", condition);
        String alarmType = condition.getAlarmType();
        //模型  防御   规则      注：  前3者是探针。   挖矿是   威胁情报 替换 防御
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }     //参数校验    1.时间 2.页码 3.每页条数
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) {
            return errorVo;
        }
        BoolQueryBuilder boolQueryBuilder = getCommonQueryBuilder(condition);
        List<Integer> taskIds = condition.getTaskIds();
        String index = Constants.ALARM;
        if (taskIds.size() > 1) {
            index = index + "*";
        } else if (taskIds.size() == 1) {
            Integer taskId = taskIds.get(0);
            index = index + "_" + taskId + "*";
        }

        // 筛选告警类型
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize= condition.getPageSize();
        if (pageSize < 1) {
            //全部导出
            searchSourceBuilder.size(10000);
            searchSourceBuilder.sort("time", SortOrder.DESC);
        } else {
            int from = (currentPage - 1) * pageSize;
            searchSourceBuilder.size(pageSize);
            searchSourceBuilder.from(from);
            String orderField = condition.getOrderField();
            Boolean asc = condition.getAsc();
            //设置排序
            if (StringUtils.isNotEmpty(orderField)) {
                SortOrder sortOrder = null;
                if (asc) {
                    sortOrder = SortOrder.ASC;
                } else {
                    sortOrder = SortOrder.DESC;
                }
                searchSourceBuilder.sort(orderField, sortOrder);
            } else {
                SortOrder sortOrder = null;
                if (asc) {
                    sortOrder = SortOrder.ASC;
                } else {
                    sortOrder = SortOrder.DESC;
                }
                searchSourceBuilder.sort("time", sortOrder);
            }
        }

        boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_type.keyword", alarmType));
        //这里是前端勾选数据  不走查询条件
        List<String> ids = condition.getIds();
        if (CollectionUtil.isNotEmpty(ids)) {
            //id查询  清空上面的条件
            searchSourceBuilder = new SearchSourceBuilder();
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termsQuery("_id", ids));
            boolQueryBuilder.must(QueryBuilders.termsQuery("task_id", taskIds));
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(ids.size());
        }
        SearchRequest searchRequest = new SearchRequest(new String[]{index}, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);

        List<Map<String, Object>> list = new ArrayList<>();
        SearchHit[] hits = searchResponse.getHits().getHits();

        for (SearchHit hit : hits) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();

            // 添加 _id 字段
            sourceMap.put("_id", hit.getId());

            // 告警索引归属
            String alarmIndex = hit.getIndex();
            sourceMap.put("alarm_index", alarmIndex);

            // 处理攻击类型字段
            int knowledgeId = Integer.parseInt(sourceMap.get("alarm_knowledge_id").toString());
            KnowledgeTypeVo knowledgeTypeVo = alarnKnowledgeMap.get(knowledgeId);
            if (knowledgeTypeVo != null) {
                sourceMap.put("attack_type", knowledgeTypeVo.getAttackType());
                sourceMap.put("attack_type_name", knowledgeTypeVo.getAttackTypeName());
            } else {
                sourceMap.put("attack_type", 0);
                sourceMap.put("attack_type_name", "未知");
            }

            // 告警名称
            Object alarmName = sourceMap.get("alarm_name");
            if (alarmName == null) {
                String alarmNameValue;
                if (knowledgeTypeVo != null) {
                    alarmNameValue = knowledgeTypeVo.getAlarmName();
                } else {
                    // 对于规则告警中的告警ID，需要从标签和规则当中去查找对应的告警名称
                    TbTagInfo tag = tagInfoDao.getTagInfoById(knowledgeId);
                    FeatureRule rule = featureRuleDao.selectById(knowledgeId);
                    if (!ObjectUtils.isEmpty(tag)) {
                        alarmNameValue = tag.getTagText();
                    } else if (!ObjectUtils.isEmpty(rule)) {
                        alarmNameValue = rule.getRuleName();
                    } else {
                        alarmNameValue = "未知";
                    }
                }
                sourceMap.put("alarm_name", alarmNameValue);
            }

            list.add(sourceMap);
        }

        // 构造会话ID列表
        List<String> sessionIdList = new ArrayList<>();
        if (alarmType.equals("模型")) {
            sessionIdList = list.stream()
                    .map(resultMap -> (List<String>) resultMap.getOrDefault("alarm_session_list", Collections.emptyList()))
                    .filter(sessionIds -> !sessionIds.isEmpty())
                    .map(sessionIds -> sessionIds.get(0))
                    .collect(Collectors.toList());
        } else if (alarmType.equals("规则")) {
            for (Map<String, Object> map : list) {
                Map<String, Object> sessionTarget = ((List<Map<String, Object>>) map.get("targets")).get(0);
                String sessionId = (String) sessionTarget.get("name");
                sessionIdList.add(sessionId);
            }
        }


        // 查询会话信息
        Map<String, Map<String, Object>> connectInfoMap = new HashMap<>();
        if (!sessionIdList.isEmpty()) {
            BoolQueryBuilder connectQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("SessionId", sessionIdList));
            SearchSourceBuilder connectSourceBuilder = new SearchSourceBuilder().query(connectQueryBuilder);
            SearchRequest connectRequest = new SearchRequest("connectinfo_*").source(connectSourceBuilder);
            SearchResponse connectResponse = esearchService.esSearch(connectRequest);
            for (SearchHit hit : connectResponse.getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String sessionId = (String) sourceAsMap.get("SessionId");
                connectInfoMap.put(sessionId, hit.getSourceAsMap());
            }

            // 更新告警记录
            for (Map<String, Object> resultMap : list) {
                if (!resultMap.containsKey("alarm_session_list") && !resultMap.containsKey("targets")) {
                    continue;
                }

                String sessionId = null;
                if (alarmType.equals("模型")) {
                    sessionId = ((List<String>) resultMap.get("alarm_session_list")).get(0);
                } else if (alarmType.equals("规则")) {
                    Map<String, Object> sessionTarget = ((List<Map<String, Object>>) resultMap.get("targets")).get(0);
                    sessionId = (String) sessionTarget.get("name");
                }

                Map<String, Object> connectMap = connectInfoMap.get(sessionId);
                if (connectMap == null) {
                    resultMap.put("sIp", StringUtils.EMPTY);
                    resultMap.put("dIp", StringUtils.EMPTY);
                    resultMap.put("sPort", null);
                    resultMap.put("dPort", null);
                    resultMap.put("ProName", StringUtils.EMPTY);
                    continue;
                }

                resultMap.put("sIp", connectMap.getOrDefault("sIp", StringUtils.EMPTY));
                resultMap.put("dIp", connectMap.getOrDefault("dIp", StringUtils.EMPTY));
                resultMap.put("sPort", connectMap.get("sPort"));
                resultMap.put("dPort", connectMap.get("dPort"));
                resultMap.put("ProName", connectMap.get("ProName"));
            }
        }

        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setRecords(list);
        pageResultVo.setTotal(searchResponse.getHits().totalHits);
        return ResultVo.success(pageResultVo);
    }


    @Override
    public ResultVo<Map<String, Object>> getAlarmDetail(String id) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("_id", id));
        searchSourceBuilder.query(boolQueryBuilder);

        SearchRequest searchRequest = new SearchRequest(new String[]{alarmEsIndex}, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length <= 0) {
            return new ResultVo<>();
        }
        SearchHit hit = hits[0];
        Map<String, Object> resultMap = hit.getSourceAsMap();
        resultMap.put("_id", id);
        Integer knowledgeId = Integer.parseInt(resultMap.get("alarm_knowledge_id").toString());
        KnowledgeTypeVo knowledgeTypeVo = alarnKnowledgeMap.get(knowledgeId);
        if (ObjectUtils.isEmpty(knowledgeTypeVo)) {
            resultMap.put("attack_type", knowledgeTypeVo.getAttackType());
            resultMap.put("attack_type_name", knowledgeTypeVo.getAttackTypeName());
        } else {
            resultMap.put("attack_type", 0);
            resultMap.put("attack_type_name", "未知");
        }

        //告警名称
        Object alarmName = resultMap.get("alarm_name");
        if (alarmName == null) {
            if (knowledgeTypeVo != null) {
                resultMap.put("alarm_name", knowledgeTypeVo.getAlarmName());
            } else {
                resultMap.put("alarm_name", "未知");
            }
        }

        return ResultVo.success(resultMap);
    }

    @Override
    public ResultVo<Map<String, Object>> getAlarmDetail2(String esIndex, String alarmId) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("_id", alarmId));
        searchSourceBuilder.query(boolQueryBuilder);

        SearchRequest searchRequest = new SearchRequest(new String[]{esIndex}, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length <= 0) {
            return new ResultVo<>();
        }
        SearchHit hit = hits[0];
        Map<String, Object> resultMap = hit.getSourceAsMap();
        resultMap.put("_id", alarmId);
        Integer knowledgeId = Integer.parseInt(resultMap.get("alarm_knowledge_id").toString());
        KnowledgeTypeVo knowledgeTypeVo = alarnKnowledgeMap.get(knowledgeId);

        if (!ObjectUtils.isEmpty(knowledgeTypeVo)) {
            resultMap.put("attack_type", knowledgeTypeVo.getAttackType());
            resultMap.put("attack_type_name", knowledgeTypeVo.getAttackTypeName());
        } else {
            resultMap.put("attack_type", 0);
            resultMap.put("attack_type_name", "未知");
        }

        resultMap.put("es_index", esIndex);

        //告警名称
        Object alarmName = resultMap.get("alarm_name");
        if (alarmName == null) {
            if (knowledgeTypeVo != null) {
                resultMap.put("alarm_name", knowledgeTypeVo.getAlarmName());
            } else {
                // 对于规则告警中的告警ID，需要从标签和规则当中去查找对应的告警名称
                TbTagInfo tag = tagInfoDao.getTagInfoById(knowledgeId);
                FeatureRule rule = featureRuleDao.selectById(knowledgeId);
                if (!ObjectUtils.isEmpty(tag)) {
                    resultMap.put("alarm_name", tag.getTagText());
                } else if (!ObjectUtils.isEmpty(rule)) {
                    resultMap.put("alarm_name", rule.getRuleName());
                } else {
                    resultMap.put("alarm_name", "未知");
                }
            }
        }

        if (!resultMap.containsKey("alarm_related_label")) {
            resultMap.put("alarm_related_label", new ArrayList<>());
        }

        return ResultVo.success(resultMap);
    }


    @Override
    public ResultVo updateDoc(AlarmStatusUpCondition condition) throws InterruptedException {
        log.info("告警：修改文档状态，condition={}", condition);
        String id = condition.getId();
        Integer taskId = condition.getTaskId();
        Integer alarmStatus = condition.getAlarmStatus();
        if (StringUtils.isEmpty(id) || taskId == null || alarmStatus == null || alarmStatus < 0 || alarmStatus > 2) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        String index = Constants.ALARM + "*";

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("_id", id));
        boolQueryBuilder.must(QueryBuilders.termQuery("task_id", taskId));
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest(new String[]{index}, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits < 1) {
            log.error("告警：修改文档状态，查询数据为0");
            throw new GkException(GkErrorEnum.ES_UPDATE_ERROR);
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        Map<String, Object> esData = hits[0].getSourceAsMap();
        //直接获取到具体的index
        index = hits[0].getIndex();
        esData.put("alarm_status", alarmStatus);
        UpdateRequest updateRequest = new UpdateRequest(index, "_doc", id);
        updateRequest.fetchSource(true);
        updateRequest.doc(esData);
        UpdateResponse response = esearchService.updateDoc(updateRequest);
        // 在此处等待ES数据请求处理
        Thread.sleep(1500);
        RestStatus status = response.status();
        if (status.getStatus() == 200) {
            if (condition.getAlarmStatus() == 2) {
                // 修改状态完成后，告警中存在这个"attack_chaim_list"这个key的话，需要进行数据库的录入
                Optional.ofNullable(esData.get("attack_chain_list")).map(list -> (List<String>) list).ifPresent(attackChainList -> attackChainList.stream().map(chain -> chain.split("_")).filter(array -> array.length >= 3).forEach(array -> {
                    String victimIp = array[0];
                    String attackerIp = array[1];
                    String labelStr = array[2];
                    long count = knowledgeAlarmDao.countAttackChain(attackerIp, victimIp, labelStr);
                    if (count == 0) {
                        knowledgeAlarmDao.insertAttackChain(attackerIp, victimIp, labelStr);
                    }
                }));
            }
            return ResultVo.success("修改告警状态成功");
        } else {
            throw new GkException(GkErrorEnum.ES_UPDATE_ERROR);
        }
    }

    @Override
    public ResultVo deleteDoc(Map<Integer, List<String>> map) throws InterruptedException {
        if (map == null || map.size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        List<QueryBuilder> should = boolQueryBuilder.should();

        for (Integer taskId : map.keySet()) {
            List<String> ids = map.get(taskId);
            BoolQueryBuilder mustBuilder = new BoolQueryBuilder();
            if (ids.size() != 0) {
                mustBuilder.must(QueryBuilders.termsQuery("_id", ids));
                mustBuilder.must(QueryBuilders.termQuery("task_id", taskId));
                should.add(mustBuilder);
            }
        }
        DeleteByQueryRequest request = new DeleteByQueryRequest(Constants.ALARM + "*");
        request.setQuery(boolQueryBuilder);
        BulkByScrollResponse response = esearchService.esDelete(request);
        Thread.sleep(1500);
        BulkByScrollTask.Status status = response.getStatus();
        if (status.getTotal() != 0) {
            return ResultVo.success(status.getTotal());
        } else {
            throw new GkException(GkErrorEnum.ES_DELETE_DOC_ERROR);
        }
    }

    @Override
    public ResultVo deleteAllAlarm() {
        log.info("删除所有的告警相关文档信息");

        // 构建索引前缀
        String partern = "alarm*";

        // 构建删除请求
        DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest(partern);

        //执行删除操作
        AcknowledgedResponse response = esearchService.deleteDoc(deleteIndexRequest);

        if (response.isAcknowledged()) {
            return ResultVo.success("删除所有告警成功");
        } else {
            throw new GkException(GkErrorEnum.ES_DELETE_DOC_ERROR);
        }
    }

    @Override
    public ResultVo exportAlarmReport(AlarmListCondition condition) {
        log.info("告警：列表，condition={}", condition);
        String alarmType = condition.getAlarmType();
        //模型  防御   规则      注：  前3者是探针。   挖矿是   威胁情报 替换 防御
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }     //参数校验    1.时间 2.页码 3.每页条数
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) {
            return errorVo;
        }
        BoolQueryBuilder boolQueryBuilder = getCommonQueryBuilder(condition);
        List<Integer> taskIds = condition.getTaskIds();
        String index = Constants.ALARM;
        if (taskIds.size() > 1) {
            index = index + "*";
        } else if (taskIds.size() == 1) {
            Integer taskId = taskIds.get(0);
            index = index + "_" + taskId + "*";
        }

        // 筛选告警类型
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);

        String orderField = condition.getOrderField();
        Boolean asc = condition.getAsc();
        //设置排序
        if (StringUtils.isNotEmpty(orderField)) {
            SortOrder sortOrder = null;
            if (asc) {
                sortOrder = SortOrder.ASC;
            } else {
                sortOrder = SortOrder.DESC;
            }
            searchSourceBuilder.sort(orderField, sortOrder);
        } else {
            SortOrder sortOrder = null;
            if (asc) {
                sortOrder = SortOrder.ASC;
            } else {
                sortOrder = SortOrder.DESC;
            }
            searchSourceBuilder.sort("time", sortOrder);
        }

        boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_type.keyword", alarmType));
        //这里是前端勾选数据  不走查询条件
        List<String> ids = condition.getIds();
        if (ids != null && ids.size() > 0) {
            //id查询  清空上面的条件
            searchSourceBuilder = new SearchSourceBuilder();
            boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termsQuery("_id", ids));
            boolQueryBuilder.must(QueryBuilders.termsQuery("task_id", taskIds));
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(ids.size());
        }

        searchSourceBuilder.query(boolQueryBuilder);
        String searchSourceStr = searchSourceBuilder.toString();

        try {
            String result = HttpRequest.post(alarmReportExprotUrl).body(searchSourceStr).timeout(60000).execute().body();
            log.info("生成告警检测报告PDF成功，返回结果：{}", result);
            JSONObject resultJson = JSONObject.parseObject(result);
            String code = resultJson.getString("code");
            String msg = resultJson.getString("ERROR_TEST_DATA");
            String filePath = resultJson.getString("data");
            if (!"200".equals(code)) {
                return ResultVo.fail(msg);
            }
            return ResultVo.success(filePath);
        } catch (Exception e) {
            log.error("导入脆弱性检测任务，异常：{}", e);
            throw new GkException(GkErrorEnum.IMPORT_VULN_CHECK_TASK_FAIL);
        }

    }

    @Override
    public ResultVo prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        log.info("通过告警关联会话ID，生成PCAP文件下载任务，sessionList={}, alarmType={}", alarmSessionList, alarmType);

        Map<String, Object> result = new HashMap<>();

        // 通过sessionID去查找会话元数据，先获取对应ES索引
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        //带入sessionId直接进行查询获取到元数据
        boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", alarmSessionList));
        SearchSourceBuilder sessionSourceBuilder = new SearchSourceBuilder();
        sessionSourceBuilder.query(boolQueryBuilder).size(alarmSessionList.size()).from(0);
        String[] includeFields = new String[]{"StartTime", "EndTime","SessionId","FirstProto","ThreadId"};
        SearchRequest sessionRequest = new SearchRequest("connectinfo*").source(sessionSourceBuilder);
        SearchResponse sessionResponse = esearchService.esSearch(sessionRequest);
        SearchHit[] sessionHits = sessionResponse.getHits().getHits();
        if (sessionHits.length == 0) {
            log.error("未找到对应的会话元数据，sessionList={}", alarmSessionList);
            return ResultVo.success("对应的会话元数据正在准备中，请稍后");
        }
        // 遍历查询到的所有session会话相关信息进行条件拼装组合
        List<DownloadPcapCondition.Session> pcapSessionList = new ArrayList<>();
        Set<Integer> taskIds = new HashSet<>();
        for(SearchHit sessionHit : sessionHits) {
            Map<String,Object> esMap = sessionHit.getSourceAsMap();

            DownloadPcapCondition.Session session = new DownloadPcapCondition.Session();
            session.setBatchId(sessionHit.getIndex().split("_")[2]);
            Long startTime = esMap.get("StartTime") == null ? null : Long.valueOf(esMap.get("StartTime").toString());
            session.setStartTime(startTime);
            Long endTime = esMap.get("EndTime") == null ? null : Long.valueOf(esMap.get("EndTime").toString());
            session.setEndTime(endTime);

            String sessionId = (String) esMap.get("SessionId");
            session.setFirstProto((Integer) esMap.get("FirstProto"));
            session.setSessionId(sessionId);

            // 任务ID为会话index第一个下划线后的第一个数字
            Integer taskId = Integer.parseInt(sessionHit.getIndex().split("_")[1]);;
            taskIds.add(taskId);
            session.setTaskId(taskId);
            session.setThreadId((Integer) esMap.get("ThreadId"));
            pcapSessionList.add(session);
        }

        // 组装show_query展示数据内容,固定与条件
        List<String> queryList = new ArrayList<>(alarmSessionList);
        Map<String, Map<String, Object>> showQuery = new HashMap<>();
        showQuery.put("and", new HashMap<String, Object>() {
            {
                put("SessionId", JSONArray.toJSON(queryList));
            }
        });
        showQuery.put("not", new HashMap<>());

        try {
            DownloadTask downloadTask = new DownloadTask();
            downloadTask.setCreatedTime(System.currentTimeMillis() / 1000);
            downloadTask.setUserId(userId);
            String queryStr = sessionSourceBuilder.query().toString();
            queryStr = queryStr.replaceAll("\\s+","");
            downloadTask.setQuery(queryStr);
            downloadTask.setType(0);
            downloadTask.setSessionId(JSONArray.toJSONString(pcapSessionList));
            downloadTask.setTaskId(taskIds.toString());
            downloadTask.setState(0);
            downloadTask.setStatus(1);
            downloadTask.setShowQuery(JSONObject.toJSONString(showQuery));
            downloadTaskDao.insert(downloadTask);
            result.put("id", downloadTask.getId());
        } catch (Exception e) {
            log.error("生成告警关联会话PCAP下载任务失败，异常：", e);
            throw new GkException(GkErrorEnum.ALARM_PCAP_DOWNLOAD_TASK_FAIL);
        }

        return ResultVo.success(result);
    }
}
