package com.geeksec.util;

import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;

import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.Normalizer;
import java.util.List;
import java.util.Scanner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description：IP地址工具类
 */
public class IpUtils {
    public static boolean isIpv4Str(String str) {
        // 0、先判断是否有网段标识并判断是否合法
        if (!judgeIpStrMask(str)) {
            return false;
        }

        // 1、判断是否是7-15位之间（0.0.0.0-***************.255/24）
        if (str.length() < 7 || str.length() > 18) {
            return false;
        }

        // 2、判断是否能以小数点分成四段
        if (str.contains("/")) {
            str = str.split("/")[0];
        }

        String[] ipArray = str.split("\\.");
        if (ipArray.length != 4) {
            return false;
        }

        // 3、判断IP的每段是否都是数字并且在0-255之间
        for (String s : ipArray) {
            try {
                int number = Integer.parseInt(s);
                if (number < 0 || number > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }

    public static boolean judgeIpStrMask(String str) {
        if (str.contains("/")) {
            String[] ipSplit = str.split("/");
            // 携带'/'必定会有两段
            String mask = ipSplit[1];
            int maskInt = Integer.parseInt(mask);
            return maskInt > 0 && maskInt < 32;
        }
        return true;
    }

    public static boolean isIpv6Str(String str) {
        String regex = "(^((([0-9A-Fa-f]{1,4}:){7}(([0-9A-Fa-f]{1,4}){1}|:))"
                + "|(([0-9A-Fa-f]{1,4}:){6}((:[0-9A-Fa-f]{1,4}){1}|"
                + "((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|"
                + "(([0-9A-Fa-f]{1,4}:){5}((:[0-9A-Fa-f]{1,4}){1,2}|"
                + ":((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|"
                + "(([0-9A-Fa-f]{1,4}:){4}((:[0-9A-Fa-f]{1,4}){1,3}"
                + "|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){3}((:[0-9A-Fa-f]{1,4}){1,4}|"
                + ":((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|"
                + "(([0-9A-Fa-f]{1,4}:){2}((:[0-9A-Fa-f]{1,4}){1,5}|"
                + ":((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))"
                + "|(([0-9A-Fa-f]{1,4}:){1}((:[0-9A-Fa-f]{1,4}){1,6}"
                + "|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|"
                + "([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|"
                + "(:((:[0-9A-Fa-f]{1,4}){1,7}|(:[fF]{4}){0,1}:((22[0-3]|2[0-1][0-9]|"
                + "[0-1][0-9][0-9]|([0-9]){1,2})"
                + "([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:)))$)";

        if (str == null) {
            System.out.println("IPv6 address is null ");
            return false;
        }
        str = Normalizer.normalize(str, Normalizer.Form.NFKC);
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        boolean match = matcher.matches();

        return match;
    }

    /**
     * IP热度
     *
     * @param ipList
     * @return
     */
    public static Integer ipHotCrc(List<String> ipList) {
        Integer[] arrayRefVar = new Integer[128];
        for (String ip : ipList) {
            int iIp = iPToLong(ip);
            if (iIp == 0) {
                continue;
            }
            int num = iIp % 100;
            arrayRefVar[num] = 1;
        }
        int result = 0;
        for (int i = 0; i < 128; i++) {

            if (arrayRefVar[i] == null) {
                continue;
            }
            if (arrayRefVar[i] == 1) {
                result++;
            }
        }
        return result;
    }

    private static Integer iPToLong(String ipaddress) {

        int result = 0;

        // 默认不判断Ipv6地址
        if (IpUtils.isIpv6Str(ipaddress)) {
            return 0;
        }

        String[] ipaddressinarray = ipaddress.split("\\.");

        for (int i = 3; i >= 0; i--) {

            int ip = Integer.parseInt(ipaddressinarray[3 - i]);

            result |= ip << (i * 8);
        }

        return Math.abs(result);

    }

    /**
     * IPV4地址转换为数字
     * @param ip
     * @return
     */
    public static Long ipv4ToNumeric(String ip) {
        Scanner sc = new Scanner(ip).useDelimiter("\\.");
        Long restValue = (sc.nextLong() << 24) + (sc.nextLong() << 16) + (sc.nextLong() << 8) + (sc.nextLong());
        sc.close();
        return restValue;
    }

    /**
     * IPV6地址转换为数字
     * @param ip
     * @return
     */
    public static BigInteger ipv6ToNumeric(String ip) {
        BigInteger ipNum = null;
        try {
            ipNum = new BigInteger(1, InetAddress.getByName(ip).getAddress());
        } catch (UnknownHostException e) {
            throw new GkException(GkErrorEnum.IP_NUMERICAL_ERROR);
        }
        return ipNum;
    }

    /**
     * 获取IP地址的终止地址
     * @param s
     * @return
     */
    public static String getEndIpAddr(String s) {
        String[] ipSplit = s.split("/");
        String ip = ipSplit[0];
        String[] ipArray = ip.split("\\.");

        for (int i = 0; i < ipArray.length; i++) {
            int octet = Integer.parseInt(ipArray[i]);
            if (octet == 0) {
                ipArray[i] = "254";
            }
        }

        return String.join(".", ipArray);
    }

}
