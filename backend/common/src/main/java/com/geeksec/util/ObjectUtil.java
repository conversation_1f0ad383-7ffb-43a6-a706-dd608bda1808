package com.geeksec.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
public class ObjectUtil {
    private static final Logger logger = LoggerFactory.getLogger(ObjectUtil.class);

    /**
     * 对象深复制: 需要对象引用的属性都实现Serializable接口
     *
     * @param <T>
     * @param t
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(T t) throws Exception {
        Assert.notNull(t, "the object must not be null");
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos);
             ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
             ObjectInputStream ois = new ObjectInputStream(bais);) {
            oos.writeObject(t);
            return (T) ois.readObject();
        } catch (Exception e) {
            logger.error("deepCopy error:", e);
            throw e;
        }
    }

    /**
     * 判断两个bean对象里，get方法的返回值是否一样
     *
     * @param data1
     * @param data2
     * @param clazz
     * @return
     */
    public static <T> boolean isSameBean(T data1, T data2, Class<T> clazz) {
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            try {
                if (method.getName().startsWith("get")) {
                    Object d1Value = method.invoke(data1);
                    Object d2Value = method.invoke(data2);
                    if (d1Value == null) {
                        if (d2Value == null) {
                            continue;
                        } else {
                            return false;
                        }
                    }
                    if (!d1Value.equals(d2Value)) {
                        return false;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }

        return true;
    }

    //java对象转map
    public static Map<String, Object> objectToMap(Object obj) throws Exception {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }
        return map;
    }

    /**
     * 判断对象是否全为空
     *
     * @param o
     * @return
     */
    public static boolean allEntityFieldIsNull(Object o) {
        try {
            if (!ObjectUtils.isEmpty(o)) {
                for (Field field : o.getClass().getDeclaredFields()) {
                    field.setAccessible(true); // 私有属性公有化
                    Object object = field.get(o);
                    if (object instanceof CharSequence) {
                        if (!ObjectUtils.isEmpty(object)) {
                            return false;
                        } else {
                            if (!ObjectUtils.isEmpty(object)) {
                                return false;
                            }
                        }
                    }
                }
            } else {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }
}
