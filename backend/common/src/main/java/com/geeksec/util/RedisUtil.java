package com.geeksec.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @author: qiuwen
 * @date: 2022/9/14
 * @Description:
 **/
@Log4j2
@Component
@SuppressWarnings("unused")
public class RedisUtil implements ApplicationContextAware {

    /**
     * 使用StringRedisTemplate(,其是RedisTemplate的定制化升级)
     */
    private static StringRedisTemplate redisTemplate;

    private static ListOperations<String, String> listOperations;

    /**
     * 推荐标签队列长度
     */
    private static final int QUEUE_LENGTH = 10;

    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        RedisUtil.redisTemplate = applicationContext.getBean(StringRedisTemplate.class);
        RedisUtil.listOperations = redisTemplate.opsForList();
    }

    public static Boolean delete(String key) {
        log.info("delete(...) => key -> {}", key);
        // 返回值只可能为true/false,不可能为null
        Boolean result = redisTemplate.delete(key);
        log.info("delete(...) => result -> {}", result);
        return result;
    }

    public static void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public static void setEx(String key, String value, long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.MINUTES);
    }

    public static void setEx(String key, String value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public static String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public static void expire(String key,long timeout){
        redisTemplate.expire(key,timeout,TimeUnit.MINUTES);
    }

    // 判断是否存在key
    public static boolean existKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获取排名前十的元素
     *
     * @param key
     * @return
     */
    public static Set<String> getTagTopTen(String key) {
        return redisTemplate.opsForZSet().reverseRange(key, 0, 9);
    }

    public static Set<ZSetOperations.TypedTuple<String>> getTopTenWithScores(String key) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 9);
    }

    public static void handleScore(String key, String member) {
        Double score = redisTemplate.opsForZSet().score(key, member);
        if (score == null) {
            // member不存在，执行新增操作
            redisTemplate.opsForZSet().add(key, member, 1.0);
        } else {
            // member已存在，执行增加操作
            redisTemplate.opsForZSet().incrementScore(key, member, 1.0);
        }
    }

    /**
     * 从key中获取当前已生成的队列，判断两个字符串的余集，将起放入队列最前端并进行出列
     * @param key
     * @param tagIds
     */
    public static void handleRecentQueue(String key, List<String> tagIds) {
        Set<String> existingValues = new HashSet<>(listOperations.range(key, 0, -1));
        // 判断两个字符串的余集
        for(String tagId : tagIds){
            if(!existingValues.contains(tagId)){
                // 先判断队伍长度是否达到10个，如果达到满的先推出最先进入的字符串
                if (listOperations.size(key) >= QUEUE_LENGTH){
                    listOperations.rightPop(key);
                }

                // 将新元素插入到队列最前面
                listOperations.leftPush(key, tagId);
                existingValues.add(tagId);
            }
        }
    }

    public static Set<String> getRecentTagQuene(String key) {
        return new HashSet<>(listOperations.range(key, 0, -1));
    }

    /**
     * 获取排名前十的元素
     *
     * @param key
     * @return
     */
    public static Set<String> getCertTagTopTen(String key) {
        return redisTemplate.opsForZSet().reverseRange(key, 0, 9);
    }

}
