package com.geeksec.entity.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@ApiModel
public class BaseCondition {

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    @ApiModelProperty("当前页")
    private Integer currentPage = 1;

    /**
     * 当前页展示数量
     */
    @JsonProperty("page_size")
    @ApiModelProperty("当前页展示数量")
    private Integer pageSize = 10;

    /**
     * 升降序
     */
    @JsonProperty("sort_order")
    @ApiModelProperty("排序方式：desc/asc")
    private String sortOrder = "desc";
}
