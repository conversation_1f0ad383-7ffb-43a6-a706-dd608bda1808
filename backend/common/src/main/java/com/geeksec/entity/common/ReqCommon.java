package com.geeksec.entity.common;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：ES 查询公共参数
 */
@Data
public class ReqCommon {
    /**
     * 升序/降序
     */
    private boolean asc;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 偏移量
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderField;

    /**
     * 开始时间
     */
    private Long startTime = 0L;

    /**
     *  结束时间
     */
    private Long endTime = 0L;

    /**
     * 任务编号
     */
    private List<Integer> taskId;

    private Integer top;

}
