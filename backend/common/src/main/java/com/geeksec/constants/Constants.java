package com.geeksec.constants;

/**
 * @author: heeexy
 * @description: 通用常量类, 单个业务的常量请单开一个类, 方便常量的分类管理
 * @date: 2017/10/24 10:15
 */
public class Constants {
    public static final String SUCCESS_CODE = "200";
    public static final String SUCCESS_MSG = "";

    //对应数据库正常标识  status=1
    public static final Integer ON = 1;
    //对应数据库删除标识  status=0
    public static final Integer OFF = 0;

    //对应数据库开启标识  status=ON
    public static final String SYS_ON = "ON";
    //对应数据库关闭标识  status=OFF
    public static final String SYS_OFF = "OFF";

    //特征规则 rule_id 边界值
    public static final Integer RULE_MAX = 200000;

    public static final String STATE_ON = "生效";

    public static final String STATE_OFF = "失效";

    /** 告警索引前缀 */
    public static final String ALARM = "alarm";

    /** 过滤规则模板文件文件名 */
    public static final String FILTER_RULE_FILE = "filter_template.csv";

    /** 特征规则模板文件文件名 */
    public static final String FEATURE_RULE_FILE = "feature_template.csv";

    /** 动态库规则 zip文件名 */
    public static final String FEATURE_RULE_ZIP = "LibFolder.zip";

    /** 多字段聚合分隔符 */
    public static final String AGGS_SPLIT = "_&&&_";

    /**
     * 标签推荐查询redis key前缀
     */
    public static final String TAG_RECOMMEND_KEY = "tag_recommend_";

    public static final String CERT_TAG_RECOMMEND_KEY = "cert_tag_recommend_";

    public static final String CERT_TAG_RECENT_KEY = "cert_tag_recent_";
}
