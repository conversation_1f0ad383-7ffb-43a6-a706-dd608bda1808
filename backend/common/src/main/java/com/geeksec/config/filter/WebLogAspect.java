package com.geeksec.config.filter;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 打印每个请求的入参、出参等信息
 */
@Aspect
@Component
@Slf4j
@Order(1)
public class WebLogAspect {

    private static final int MAX_RETURN_RES_LENGTH = 10000;

    @Pointcut("execution(public * com.geeksec.*.controller..*.*(..))")
    public void webLog() {
    }

    @Pointcut(" execution(public * com.geeksec.*.config.exception.GlobalExceptionHandler.*(..))")
    public void exceptions() {
    }

    /**
     * 只在进入controller时记录请求信息
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        log.debug("请求路径 {} ,进入方法 {}", request.getRequestURI(), joinPoint.getSignature().getDeclaringTypeName() + ":" + joinPoint.getSignature().getName());
        MDC.put("req", getRequestInfo(request).toJSONString());
        MDC.put("startTime", String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 打印请求日志
     */
    @AfterReturning(pointcut = "webLog()|| exceptions()", returning = "result")
    public void afterReturning(Object result) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        // 字典返回的内容太多，不进行日志内容打印
        String url = request.getRequestURI();
        if (url.equals("/dict")) {
            return;
        }
        if (MapUtil.isNotEmpty(contextMap)) {
            JSONObject jsonObject = new JSONObject(true);
            jsonObject.put("url", request.getRequestURI());
            jsonObject.put("took", System.currentTimeMillis() - Long.parseLong(contextMap.getOrDefault("startTime", String.valueOf(System.currentTimeMillis()))) + "ms");
            jsonObject.put("username", contextMap.getOrDefault("username", ""));
            jsonObject.put("token", contextMap.getOrDefault("token", StringUtils.EMPTY));
            jsonObject.put("req", JSON.parseObject(contextMap.getOrDefault("req", "")));
            if (result != null && JSONObject.toJSONString(result).length() < MAX_RETURN_RES_LENGTH) {
                jsonObject.put("res", JSON.parseObject(JSONObject.toJSONString(result)));
            } else {
                jsonObject.put("remark", "返回res结果过长，不予打印到日志中!");
            }
            log.info(jsonObject.toJSONString());
        }
    }

    /**
     * 读取请求信息,转换为json
     */
    private JSONObject getRequestInfo(HttpServletRequest req) {
        JSONObject requestInfo = new JSONObject();
        try {
            StringBuffer requestURL = req.getRequestURL();
            requestInfo.put("requestURL", requestURL.toString());

            String method = req.getMethod();
            requestInfo.put("method", method);

            String queryString = req.getQueryString();
            if (queryString != null) {
                requestInfo.put("queryString", URLDecoder.decode(queryString, "UTF-8"));
            }

            String remoteAddr = req.getRemoteAddr();
            requestInfo.put("remoteAddr", remoteAddr);

            if (req instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) req;
                String bodyStr = new String(wrapper.getContentAsByteArray(), StandardCharsets.UTF_8);

                if (bodyStr.startsWith("{")) {
                    JSONObject requestBody = JSON.parseObject(bodyStr);
                    requestInfo.put("requestBody", requestBody);
                }
            }
        } catch (Exception e) {
            log.error("解析请求失败", e);
            requestInfo.put("parseError", e.getMessage());
        }
        return requestInfo;
    }
}
