package com.geeksec.config.database;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Description：auth鉴权API数据源配置
 */
//@Configuration
//@MapperScan(basePackages = {"com.geeksec.authentication.dao"}, sqlSessionFactoryRef = "AuthSqlSessionFactory")
public class AuthDataSourceConfig {

    @Bean(name = "AuthDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.auth-db") //配置文件前缀
    @Primary
    public DataSource getAuthDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "AuthSqlSessionFactory")
    @Primary
    public SqlSessionFactory AuthSqlSessionFactory(@Qualifier("AuthDataSource") DataSource datasource)
            throws Exception {
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        // 配置打印SQL语句
//        configuration.setLogImpl(StdOutImpl.class);

        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setConfiguration(configuration);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/auth/*.xml"));
        return bean.getObject();// 设置mybatis的xml所在位置
    }


    @Primary
    @Bean("AuthSqlSessionTemplate")
    // 表示数据源为默认数据源
    public SqlSessionTemplate AuthSqlSessionTemplate(
            @Qualifier("AuthSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }

}
