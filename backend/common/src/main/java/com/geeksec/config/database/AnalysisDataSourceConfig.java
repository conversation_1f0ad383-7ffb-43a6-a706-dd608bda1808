package com.geeksec.config.database;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：探针用th_analysis数据源配置
 */
//@Configuration
//@MapperScan(basePackages = {"com.geeksec.analysis.dao"}, sqlSessionFactoryRef = "AnalysisSqlSessionFactory")
public class AnalysisDataSourceConfig {

    @Bean(name = "AnalysisDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.nta-db")
    public DataSource getAnalysisDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "AnalysisSqlSessionFactory")
    public SqlSessionFactory AnalysisSqlSessionFactory(@Qualifier("AnalysisDataSource") DataSource dataSource)
            throws Exception {
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        // 配置打印sql语句
//        configuration.setLogImpl(StdOutImpl.class);
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setConfiguration(configuration);

        List<Resource> resourceList = new ArrayList<Resource>();
        resourceList.addAll(Arrays.asList(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/analysis/*.xml")));

        Resource[] resources = resourceList.toArray(new Resource[]{});
        bean.setMapperLocations(resources);
        return bean.getObject();// 设置mybatis的xml所在位置
    }

    @Bean(name = "AnalysisSqlSessionTemplate")
    public SqlSessionTemplate AnalysisSqlSessionTemplate(@Qualifier("AnalysisSqlSessionFactory")SqlSessionFactory sqlSessionFactory){
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
