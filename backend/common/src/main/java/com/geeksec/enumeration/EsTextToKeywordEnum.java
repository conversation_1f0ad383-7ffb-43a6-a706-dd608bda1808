package com.geeksec.enumeration;

import lombok.Getter;

/**
 * @author: qiuwen
 * @date: 2022/9/2Cert
 * @Description:
 **/
@Getter
public enum EsTextToKeywordEnum {
    //会话分析的
    HTTP_HOST("HTTP.Host","HTTP.Host.keyword"),
    DNS_DOMAIN("DNS.Domain","DNS.Domain.keyword"),
    SSL_SERVER("SSL.CH_ServerName","SSL.CH_ServerName.keyword"),

    INDEX_HTTP_HOST("Host","Host.keyword"),
    INDEX_DNS_DOMAIN("Domain","Domain.keyword"),
    INDEX_SSL_SERVER("CH_ServerName","CH_ServerName.keyword");

    private final String field;
    //方便es查询字段
    private final String esUse;

    EsTextToKeywordEnum(String field, String esUse){
        this.field = field;
        this.esUse = esUse;
    }

    public static String getEsUse(String field){
        for(EsTextToKeywordEnum aggEnum : EsTextToKeywordEnum.values()){
            if(aggEnum.getField().equals(field)){
                return aggEnum.getEsUse();
            }
        }
        //返回本身
        return field;
    }
}
