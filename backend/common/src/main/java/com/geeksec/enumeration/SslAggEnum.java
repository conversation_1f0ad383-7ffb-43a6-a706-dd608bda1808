package com.geeksec.enumeration;

import java.util.ArrayList;
import java.util.List;

public enum SslAggEnum {
    //支持的聚合字段：客户端IP、服务端IP、服务端端口、服务端证书Hash、服务器名、客户端指纹、服务端指纹
    //客户端=s、源   服务端=d、目标
    SIP("sIp","sIp"),
    DIP("dIp","dIp"),
    DPort("dPort","dPort"),
    AppName("appName","AppName"),
    DCertHashStr("dCertHashStr","dCertHash"),
    CHServerName("CH_ServerName","CH_ServerName.keyword"),
    SS<PERSON><PERSON>inger("sSSLFinger","sSSLFinger"),
    DSSLFinger("dSSLFinger","dSSLFinger");

    private final String field;
    //方便es查询字段
    private final String esUse;

    SslAggEnum(String field,String esUse){
        this.field = field;
        this.esUse = esUse;
    }

    public String getField() {
        return field;
    }

    public String getEsUse() {
        return esUse;
    }

    public static Boolean checkField(String field){
        for(SslAggEnum aggEnum : SslAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return true;
            }
        }
        return false;
    }

    public static String getEsField(String field){
        for(SslAggEnum aggEnum : SslAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return aggEnum.getEsUse();
            }
        }
        return null;
    }

    /**
     * 获取前端对应字段集合
     * @return list
     */
    public static List<String> getAllField(){
        List<String> list = new ArrayList<>();
        for(SslAggEnum aggEnum : SslAggEnum.values()){
            list.add(aggEnum.getField());
        }
        return list;
    }
}
