package com.geeksec.enumeration;

import lombok.Getter;

/**
 * @description: 业务异常提示
 * @author: shiwenxu
 * @createtime: 2023/8/30 09:46
 **/
@Getter
public enum GkErrorEnum {

    /**
     * 系统类异常
     */
    REQUEST_GRAMMAR_ERROR(400,"服务器错误"),
    UNAUTHORIZED(401, "用户登录状态过期，请重新登录"),
    SERVER_REJECT_REQUEST(403,"服务器拒绝请求"),
    REQUEST_PATH_ERROR(404,"请求路径不存在"),
    REQUEST_OVERTIME(408, "请求超时"),
    REQUEST_METHOD_ERROR(409, "请求方式有误，请检查"),
    FAIL(500, "系统异常，请联系管理员！"),
    NOT_IMPLEMENTS(501, "未实现的方法"),
    NET_ERROR(502, "错误网关"),
    LOGIN_PASSWORD_FAILED(503, "登录密码错误"),
    LOGOUT_ERROR(504,"用户登出失败" ),
    JSON_PARSE_ERROR(505,"JSON对象转义失败"),
    MYSQL_EXECUTE_ERROR(600, "数据库执行异常"),
    SYSTEM_SHUTDOWN_ERROR(700,"服务器关机失败"),
    SYSTEM_REBOOT_ERROR(800,"服务器重启失败"),
    OLD_PASSWORD_ERROR(900,"修改密码失败：原密码输入错误"),
    OLD_PASSWORD_REPEAT(901,"修改密码失败：新密码与原始密码相同"),
    MODIFY_USER_PASSWORD_ERROR(902,"修改密码失败"),
    USER_NOT_EXIST(903,"用户不存在"),
    USER_NOT_LOGIN(904,"用户未登录"),

    /**
     * 用户权限参数类异常
     */
    TOKEN_EMPTY(1001, "token为空,请重新登录"),
    LOGIN_EXPIRE(1002, "登陆已过期,请重新登录"),
    REQUEST_PARAM_ERROR(1003, "请求参数格式异常,请检查"),
    REQUEST_PARAM_EMPTY(1004, "请求参数为空,请检查"),
    REQUEST_PARAM_LEAK(1005, "缺少必填参数"),
    QUERY_IP_PARAM_ERROR(1006, "IP类型查询参数格式异常,请检查"),
    IP_FORMAT_ERROR(1007,"IP地址格式错误，请检查"),
    LOGIN_STATUS_CHECK_USER_EMPTY(1008, "当前登录的用户信息为空"),
    PORT_FORMAT_ERROR(1009,"端口参数错误"),
    RULE_PROTOCOL_FORMAT_ERROR(1010,"协议规则参数格式错误"),
    CHECK_SO_EMPTY(1011,"动态库检验文件为空"),
    CHECK_SO_FAIL(1012,"动态库检验失败"),
    LOGIN_STATUS_CHECK_ERROR(1013, "用户登录状态校验异常"),
    LOGIN_STATUS_CHECK_REQUEST_FAIL(1014,"权限校验系统请求失败"),

    /**
     * ES查询类异常
     */
    ES_SEARCH_ERROR(2001, "ES文档查询异常"),
    ES_AGGR_ERROR(2002, "ES聚合查询异常"),
    ES_UPDATE_ERROR(2003, "ES文档更新异常"),
    ES_TEMPLATE_QUERY_ERROR(2004, "ES模板查询异常"),
    ES_TARGET_MODIFY_LABEL_ERROR(2005, "ES目标修改标签异常"),
    ES_OPERATE_ERROR(2006,"ES操作异常"),
    ES_DELETE_DOC_ERROR(2007,"ES删除异常"),
    ES_QUERY_FIELD_TRANSFER_ERROR(2008,"ES可查询字段获取异常"),

    /**
     * 图数据库相关异常
     */
    GRAPHDB_QUERY_ERROR(3001, "图数据库查询异常"),
    GRAPHDB_PARSE_ERROR(3002, "图数据库解析异常"),
    GRAPHDB_QUERY_EMPTY(3003,"图数据库查询结果为空"),
    GRAPHDB_QUERY_TAG_LABEL_FAIL(3004,"查询关联节点标签信息失败"),
    GRAPHDB_QUERY_HISTORY_CLEAR_FAIL(3005,"图数据库历史清空失败"),
    SPACE_NOT_FIND_ERROR(3006, "该时间范围无数据"),

    /**
     * 服务组件相关异常
     */
    REDIS_QUERY_ERROR(4001,"Redis缓存查询异常"),
    SYSTEM_INFO_QUERY_ERROR(4002,"系统信息查询异常"),
    FILE_READ_FAILURE(40005, "文件读取失败"),
    FILE_CONTENT_FORMAT_ILLEGAL(40010, "文件内容格式非法"),

    /**
     * 具体业务类异常
     */
    FILTER_RULE_TYPE_ERROR(9001,"创建过滤规则失败：不支持的过滤类型"),
    GET_USER_CERT_LOG_ERROR(9002,"获取当前用户的证书详情日志模板失败"),
    MODIFY_USER_CERT_LOG_ERROR(9003,"修改当前用户的证书详情日志模板失败"),
    RULE_SYNC_URL_EMPTY(9004,"探针规则同步URL未配置"),
    RULE_SYNC_REQUEST_ERROR(9005,"探针规则同步请求异常"),
    SANKEY_GRAPH_QUERY_ERROR(9006,"查询会话中通信相关桑基图异常"),
    IMPORT_VULN_CHECK_TASK_FAIL(9007,"引入脆弱性检测任务失败"),
    CREATE_QUERY_TEMPLATE_LEAK(9008, "创建检索模板失败：至少含有一个查询条件"),
    QUERY_HISTORY_FAILED(9009, "查询检索历史记录列表失败"),
    CERT_BLACK_WHITE_REPEAT(9010, "修改证书标签失败：证书不能同时为白名单证书和黑名单证书"),
    CERT_TAG_MODIFY_EMPTY(9011, "修改证书信息失败：未查找到对应证书"),
    CERT_TAG_MODIFY_ERROR(9012,"修改证书信息失败"),
    MODEL_SWITCH_ERROR(9013,"模型开关修改失败"),
    IMPORT_PCAP_FILE_ERROR(9014,"导入pcap文件失败"),
    IP_NUMERICAL_ERROR(9015,"IP地址转换失败"),
    OFFLINE_TASK_BATCH_ERROR(9016,"离线导入数据导入失败"),
    FEATURE_IMPORT_ERROR(9017,"特征规则导入失败"),
    CERT_TAG_QUERY_ERROR(9018,"根据证书标签查询证书失败"),
    FEATURE_RULE_REPEAT(9019,"特征规则数据冲突"),
    FEATURE_RULE_ADD_ERROR(9020,"特征规则添加失败"),
    FILTER_RULE_REPEAT(9021,"过滤规则数据冲突"),
    ALARM_TARGET_AGGR_ERROR(9022,"告警指标信息聚合查询异常"),
    ALARM_ATTACK_CHAIN_QUERY_ERROR(9023,"告警攻击链查询异常"),
    ALARM_LIST_QUERY_ERROR(9024,"告警列表查询异常"),
    ALARM_DETAIL_QUERY_ERROR(9025,"告警详情查询异常"),
    ALARM_JUDGE_GRAPH_ERROR(9026,"告警研判绘制失败"),
    ALARM_STATUS_UPDATE_ERROR(9027,"告警状态切换异常"),
    ALARM_DELETE_ERROR(9028,"告警删除异常"),
    IP_DETAIL_QUERY_ERROR(9029,"IP详情查询失败"),
    CERT_DETAIL_QUERY_ERROR(9030,"证书详情查询失败"),
    DOMAIN_DETAIL_QUERY_ERROR(9031,"域名详情查询失败"),
    BLOCKCHAIN_DETAIL_QUERY_ERROR(9032,"区块链详情信息查询失败"),
    FINGER_DETAIL_QUERY_ERROR(9033,"指纹详情信息查询失败"),
    UPDATE_LABELS_ERROR(9034,"修改会话标签失败"),
    UPDATE_REMARK_ERROR(9035,"修改详情备注失败"),
    FILTER_RULE_IMPORT_ERROR(9036,"导入过滤规则失败"),
    UPDATE_FEATURE_RULE_ERROR(9037,"编辑特征规则失败"),
    FEATURE_RULE_DELETE_ERROR(9038,"删除特征规则失败"),
    TASK_RELATED_NETFLOW_ERROR(9039,"任务关联网口异常"),
    DISK_READ_MODEL_LOAD_ERROR(9040,"数据库磁盘模式字段读取异常"),
    IP_SITUATION_QUERY_ERROR(9041,"IP态势图查询异常"),
    QUERY_TASK_CONFIG_ERROR(9042,"查询任务配置失败"),
    TAG_LIBRARY_QUERY_ERROR(9043,"标签哭查询失败"),
    ADD_INTERNAL_IP_NET_ERROR(9044,"添加IP内网段失败"),
    THREAT_INFO_QUERY_ERROR(9045,"情报列表信息查询失败"),
    SYSTEM_CLEAR_STATUS_CHECK_FAIL(9046,"查询系统清理状态失败"),
    CREATE_QUERY_HISTORY_ERROR(9047,"创建会话查询历史失败"),
    APPSERVICE_DETAIL_QUERY_ERROR(9048,"应用服务详情信息查询失败"),
    APP_DETAIL_QUERY_ERROR(9049,"应用详情信息查询失败"),
    MISSING_REQUIRED_PARAMETERS(90003, "缺少必填参数"),
    FILE_CREATION_FAILED(90008, "文件创建失败"),
    TEMP_FOLDER_LOCATION_NOT_CONFIG(90004, "临时文件夹位置未配置，请联系管理员"),

    /**
     * 下载导出类异常
     */
    FILE_DOWNLOAD_FAIL(8001, "文件下载失败"),
    FILE_NOT_EXIST(8002, "文件下载失败：文件不存在"),
    DELETE_DOWNLOAD_TASK_FAILED(8003, "删除下载任务失败"),
    DOWNLOAD_TASK_NOT_EXIST(8004, "下载任务不存在"),
    SESSION_LOG_EXPORT_QUERY_ERROR(8005,"会话日志导出查询失败：查询日志失败"),
    CSV_EXPORT_FAIL(8006,"CSV导出失败"),
    ALARM_PCAP_DOWNLOAD_TASK_FAIL(8007,"生成告警关联会话PCAP下载任务失败"),
    ALARM_DATA_CSV_EXPORT_FAIL(8008,"告警列表CSV文件导出失败"),
    ALARM_DATA_PDF_EXPORT_FAIL(8009,"告警报告PDF文件导出失败"),
    FILE_DOWNLOAD_PATH_EMPTY(8010,"文件下载路径为空"),
    FILE_UPLOAD_PATH_EMPTY(8011,"文件上传路径为空"),
    UPLOAD_FILE_EMPTY(8012,"上传文件为空"),
    UPLOAD_FILE_FAIL(8013,"上传文件失败"),
    UPLOAD_CSV_FILE_FAIL(8014,"上传导入CSV文件失败"),
    FEATURE_RULE_TEMPLATE_DOWNLOAD_FAIL(8015,"特征规则模板文件下载失败"),
    DOWNLOAD_FILE_COUNT_OVERATE(8016,"下载文件数量过大"),
    FILTER_RULE_TEMPLATE_DOWNLOAD_FAIL(8016,"过滤规则模板下载失败"),
    DOWNLOAD_TASK_CREATE_FAIL(8017,"导入任务创建失败"),
    DELETE_DOWNLOAD_TASK_FAIL(8018,"下载任务删除失败"),

    /**
     * 探针相关错误
     */
    E_60001(60001, "探针重启同步成功"),
    E_60002(60002, "探针重启同步失败"),
    E_60003(60003, "探针重启同步中"),
    E_60004(60004, "探针重启同步 静止"),

    /**
     * 会话业务类异常
     */
    SESSION_DETAIL_PARSE_ERROR(10001,"会话详情数据解析失败"),
    SESSION_BASIC_QUERY_ERROR(10002,"会话详情基础信息查询失败"),
    PROTOCOL_METADATA_QUERY_ERROR(10003,"协议元数据查询失败"),
    IP_AGGR_QUERY_ERROR(10004,"会话聚合IP列表查询失败"),
    DOMAIN_AGGR_QUERY_ERROR(10005,"会话聚合域名列表查询失败"),
    CERT_AGGR_QUERY_ERROR(10006,"会话聚合证书列表查询失败"),
    FINGER_AGGR_QUERY_ERROR(10007,"会话聚合指纹列表查询失败"),
    PORT_AGGR_QUERY_ERROR(10008,"会话聚合端口列表查询失败"),
    APP_AGGR_QUERY_ERROR(10009,"会话聚合应用列表查询失败"),
    METADATA_LIST_QUERY_ERROR(10010,"协议元数据列表查询失败"),
    TAG_AGGR_QUERY_ERROR(10011,"会话标签聚合失败"),
    SESSION_AGGR_QUERY_ERROR(10012,"会话列表聚合失败"),
    SESSION_METADATA_AGGR_ERROR(10013,"会话元数据聚合查询失败"),
    SESSION_DETAIL_QUERY_ERROR(10014,"会话详情查询失败");

    private final Integer err;

    private final String msg;

    GkErrorEnum(Integer err, String msg) {
        this.err = err;
        this.msg = msg;
    }


}
