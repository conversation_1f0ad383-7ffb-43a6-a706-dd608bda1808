package com.geeksec.base;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class LmdbNetClient {
    private String dbUrl = null;
    private String dbPath = "/lmdb/";
    PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();

    public LmdbNetClient(String dburls) {
        dbUrl = dburls;
        //设置最大连接数
        cm.setMaxTotal(100);
        //设置每个主机的最大连接数
        cm.setDefaultMaxPerRoute(10);
    }

    public boolean put(String db, String key, byte[] value) {
        String url = dbUrl + dbPath + "put";
        Map<String, Object> respBody = new HashMap<>();
        System.out.println("url ====" + url);
        System.out.println("DB ====" + db);
        System.out.println("key ====" + key);
        respBody.put("DB", db);
        respBody.put("key", key);
        respBody.put("value", Base64.getEncoder().encodeToString(value));
        byte[] rsp = post(url, null, respBody);
        if (rsp == null) {
            return false;
        }
        Map<String, Object> reMap = httpRespToMap(rsp);
        return true;
    }

    public boolean putBatch(String db, List<Map<String, byte[]>> value) {
        String url = dbUrl + dbPath + "putbatch";
        Map<String, Object> respBodyMap = new HashMap<>();
        respBodyMap.put("DB", db);
        List<Map<String, Object>> respBody = new ArrayList<>();
        for (Map<String, byte[]> map : value) {
            for (String key : map.keySet()) {
                //System.out.println("第二种:" + Value.get(key));
                Map<String, Object> t = new HashMap<>();
                t.put(key, Base64.getEncoder().encodeToString(map.get(key)));
                respBody.add(t);
            }
        }
        respBodyMap.put("list", respBody);
        byte[] rsp = post(url, null, respBodyMap);
        if (rsp == null) {
            return false;
        }
        return true;
        //return false;
    }

    public byte[] get(String db, String key) {

        String url = dbUrl + dbPath + "get";
        Map<String, Object> respBodyMap = new HashMap<>();
        respBodyMap.put("DB", db);
        respBodyMap.put("key", key);
        byte[] rsp = post(url, null, respBodyMap);
        if (rsp == null) {
            return null;
        }
        Map<String, Object> reMap = httpRespToMap(rsp);

        if (reMap.containsKey("data")) {
            return Base64.getDecoder().decode(reMap.get("data").toString());
        }

        return null;
    }

    public boolean delDB(String db) {

        String url = dbUrl + dbPath + "deleteDB";
        Map<String, Object> respBodyMap = new HashMap<>();
        respBodyMap.put("DB", db);
        byte[] rsp = post(url, null, respBodyMap);
        if (rsp == null) {
            return false;
        }
        Map<String, Object> reMap = httpRespToMap(rsp);
        return true;
    }

    byte[] post(String url, Map<String, Object> paramMap, Map<String, Object> resBodyMap) {

        // 创建httpClient实例对象
        try {
            // org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();//设置数据传输的最长时间
            HttpPost httpPost = new HttpPost(url);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(1000)//设置创建连接的最长时间
                    .setConnectionRequestTimeout(500)//设置获取连接的最长时间
                    .setSocketTimeout(10 * 1000)//设置数据传输的最长时间
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置httpClient连接主机服务器超时时间：15000毫秒
            // CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();
            //httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(15000);
            // 创建post请求方法实例对象
            //PostMethod postMethod = new PostMethod(url);
            //postMethod.setRequestBody();
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            //httpPost.getMethod(new StringEntity(JSONObject.toJSONString(map)));
            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(resBodyMap), "utf-8");
            httpPost.setEntity(requestEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }

            byte[] p = EntityUtils.toByteArray(entity);
            //System.out.println("接收到的响应信息:--------"+ EntityUtils.toString(entity,"UTF-8"));
            response.close();


            return p;

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    byte[] get(String url, Map<String, Object> paramMap) {
        // 创建httpClient实例对象
        try {
            URIBuilder uri = new URIBuilder(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(1000)//设置创建连接的最长时间
                    .setConnectionRequestTimeout(500)//设置获取连接的最长时间
                    .setSocketTimeout(10 * 1000)//设置数据传输的最长时间
                    .build();
            if (paramMap != null && paramMap.size() > 0) {
                //List<BasicNameValuePair> list = new LinkedList<>();
                for (String key : paramMap.keySet()) {
                    //String value = String.valueOf(paramMap.get(key));
                    BasicNameValuePair param2 = new BasicNameValuePair(key, paramMap.get(key).toString());
                    // list.add(param2);
                    uri.setParameters(param2);
                }
                //uri.setParameters(list);
            }
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();//设置数据传输的最长时间
            HttpGet httpGet = new HttpGet(uri.build());
            httpGet.setConfig(requestConfig);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            byte[] p = EntityUtils.toByteArray(entity);
            //System.out.println("接收到的响应信息:--------"+ EntityUtils.toString(entity,"UTF-8"));
            response.close();
            return p;
            //return EntityUtils.toByteArray(entity);
        } catch (URISyntaxException e) {
            e.printStackTrace();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    Map<String, Object> httpRespToMap(byte[] responseBody) {
        String result;
        result = new String(responseBody, StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONObject.parseObject(result);
        Map map = JSONObject.parseObject(jsonObject.toString(), Map.class);
        return map;

    }
}

