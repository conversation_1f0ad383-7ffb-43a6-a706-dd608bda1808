package com.geeksec.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

class QueryFetchQue {
    private String tempSql = "";

    private ChartReslutHandle chartReslutHandle = new ChartReslutHandle();

    // 值转换
    private String value2Str(Object param) {

        if (param instanceof Integer) {
            return String.valueOf((Integer) param);

        } else if (param instanceof String) {
            return "'" + (String) param + "'";

        } else if (param instanceof Float) {
            return String.valueOf((Float) param);
        } else if (param instanceof Double) {
            return String.valueOf((Double) param);
        }
        return "";
    }

    public String sqlCmd(Map<String, Object> reqMap) {
        String sql = tempSql;
        for (Map.Entry<String, Object> entry : reqMap.entrySet()) {
            // //System.out.ut.print(entry.getKey() + "-" + entry.getValue() + "	");
            sql = sql.replace("@" + entry.getKey(), value2Str(entry.getValue()));
        }
        return sql;
    }

    public void parse(JSONObject obj) {
        tempSql = obj.get("sql").toString();
        JSONObject respJson = obj.getJSONObject("resp");
        chartReslutHandle.parse(respJson);
    }

    public void chartByResp(List<Map<String, Object>> result, List<Object> vList, List<Object> eList, VEMap pVEMAap) {
        chartReslutHandle.handle(result, vList, eList, pVEMAap);
    }
    //

}
