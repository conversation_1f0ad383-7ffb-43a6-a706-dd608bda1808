package com.geeksec.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

class ChartNode {

    Map<String,NodePrase> respMap =  new HashMap<>();
    public   ChartNode(JSONObject jsonObject) {

        for (Map.Entry entry : jsonObject.entrySet()) {
            respMap.put(entry.getKey().toString(),new NodePrase((JSONObject) entry.getValue()));
        }
        /*for(Map.Entry<String, J> entry : jsonObject.entrySet()) {
            ////System.out.ut.println(entry.getKey()+entry.getValue());
            RespMap.put(entry.getKey(),new NodePrase(entry.getValue()));
        }*/
    }
    public  Map<String,Object > handle(Map<String,Object > src, Map <String,Object > v ) {


        for (Map.Entry<String, NodePrase> entry : respMap.entrySet())
        {
            v.put(entry.getKey(),entry.getValue().handle(src));
        }
        return v;
    }
}

