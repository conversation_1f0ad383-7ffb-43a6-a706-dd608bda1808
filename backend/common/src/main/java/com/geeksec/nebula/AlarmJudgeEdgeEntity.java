package com.geeksec.nebula;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class AlarmJudgeEdgeEntity {

    /**
     * 起点
     */
    @JsonProperty(value = "from")
    private String from;

    /**
     * 终点
     */
    @JsonProperty(value = "to")
    private String to;

    /**
     * 边ID
     */
    @JsonProperty(value = "id")
    private String rankId;

    /**
     * 请求使用服务
     */
    @JsonProperty(value = "label")
    private String label;

    @JsonProperty(value = "lv")
    private Integer level;

    @JsonProperty(value = "num")
    private String num;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "type")
    private String type;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AlarmJudgeEdgeEntity edge = (AlarmJudgeEdgeEntity) o;
        return Objects.equals(from, edge.from) &&
                Objects.equals(to, edge.to);
    }

    @Override
    public int hashCode() {
        return Objects.hash(from, to);
    }

}
