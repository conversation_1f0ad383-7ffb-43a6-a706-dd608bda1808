package com.geeksec.nebula;

import java.util.ArrayList;
import java.util.List;

public class Where {
    private String vertexLName;  // 节点名称
    private String flied = "id"; // 字段名称 。默认是ID
    private String opr;
    private String v = "";
    private Integer val = 0;
    private Long lVal = 0L;
    private String sVal = "";
    private List<Object> sValueList = new ArrayList<>();

    public Where(String sVLName, Object... sValueMap) {
        vertexLName = sVLName;

        for (Object e : sValueMap) {
            sValueList.add(e);
        }
        ////System.out.ut.println("where data type === int");
    }

    private String value2Str(Object param) {
        ////System.out.ut.println("v=== "+v);
        if (param instanceof Integer) {
            return String.valueOf((Integer) param);

        } else if (param instanceof String) {
            return "'" + (String) param + "'";

        } else if (param instanceof Float) {
            return String.valueOf((Float) param);
        } else if (param instanceof Double) {
            return String.valueOf((Double) param);
        }
        return "";
    }

    public SqlParse parse(SqlParse sqlParse) {
        String par = vertexLName;
        for (Object e : sValueList) {
            String t = value2Str(e);
            par = par.replace("?", t);
        }
        sqlParse.sql += par;
        return sqlParse;
    }

}
