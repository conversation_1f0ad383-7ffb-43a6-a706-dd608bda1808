package com.geeksec.nebula;

public class Vertex extends VLBase {
    public Vertex(String vertexName) {
        name = vertexName;
        type = "V";
    }
    public Vertex(String vertexName,String type) {
        name = vertexName;
        this.type = type;
    }
    public  SqlParse parse(SqlParse sqlParse) {
        if (sqlParse.lastVType.equals("V")) {
            sqlParse.sql +=  "-[]-";
        } else if (sqlParse.lastVType.equals("E")) {
            sqlParse.sql += "-";
        }else if(type.equals("Q")) {
            sqlParse.sql +=  "-[]->";
        }
        //String VAliseName = PSql.GetVateAlias(Name);
        String vAliasName = name;
        sqlParse.sql += "(" + vAliasName+":"+ name +")";
        return sqlParse;
    }
}
