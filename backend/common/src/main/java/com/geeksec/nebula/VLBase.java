package com.geeksec.nebula;

import java.util.HashMap;
import java.util.Map;

class SqlParse {

    public String sql = "";
    public Map<String, String> aliasVertexMap = new HashMap<>();  //  别+
    private int vertexNum = 1;
    private int edgeNum = 1;

    public String getVateAlias(String SName) {
        if (aliasVertexMap.containsKey(SName)) {
            return aliasVertexMap.get(SName);
        } else {
            String aliasVName = "v" + String.valueOf(vertexNum);
            vertexNum++;
            aliasVertexMap.put(SName, aliasVName);
            return aliasVName;
        }
    }

    public String getEdgeAlias(String SName) {
        if (aliasVertexMap.containsKey(SName)) {
            return aliasVertexMap.get(SName);
        } else {
            String aliasVName = "e" + String.valueOf(edgeNum);
            vertexNum++;
            aliasVertexMap.put(SName, aliasVName);
            return aliasVName;
        }
    }

    public String getAlias(String name) {
        if (aliasVertexMap.contains<PERSON>ey(name)) {
            return aliasVertexMap.get(name);
        }
        return null;
    }

    public String lastVType = "";
}

public class VLBase {
    public String name = "";
    public String type = "";

    public SqlParse parse(SqlParse sqlParse) {
        return sqlParse;

    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public SqlParse typeParse(SqlParse sqlParse) {

        parse(sqlParse);
        sqlParse.lastVType = type;
        return sqlParse;
    }

}
