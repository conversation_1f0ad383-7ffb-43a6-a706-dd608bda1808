package com.geeksec.nebula;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// {"V":[] , "E":[]}
public class ChartReslutHandle {
    private List<List<ChartNode>> vvDefList = new ArrayList<>();
    private List<List<ChartNode>> eeDefList = new ArrayList<>();

    public void parse(JSONObject obj) {
        JSONArray vertexDef = obj.getJSONArray("vertex");
        JSONArray edgeDef = obj.getJSONArray("edge");
        for (int i = 0; i < vertexDef.size(); i++) {
            JSONArray vObj = vertexDef.getJSONArray(i);
            List<ChartNode> vertexDefList = new ArrayList<>();
            for (int ii = 0; ii < vObj.size(); ii++) {
                JSONObject vvObj = vObj.getJSONObject(ii);
                vertexDefList.add(new ChartNode(vvObj));
            }
            vvDefList.add(vertexDefList);

        }
        for (int i = 0; i < edgeDef.size(); i++) {
            JSONArray eObj = edgeDef.getJSONArray(i);
            List<ChartNode> edgeDefList = new ArrayList<>();
            for (int ii = 0; ii < eObj.size(); ii++) {
                JSONObject eeObj = eObj.getJSONObject(ii);
                // 去重
                edgeDefList.add(new ChartNode(eeObj));
            }
            eeDefList.add(edgeDefList);
        }
    }

    public void handle(List<Map<String, Object>> result, List<Object> vList, List<Object> eList, VEMap pVEMAap) {
        for (Map<String, Object> src : result) {

            for (List<ChartNode> vmList : vvDefList) {
                Map<String, Object> vt = new HashMap<>();
                for (ChartNode v : vmList) {
                    v.handle(src, vt);
                }
                // 处理 特殊的V
                String vertexType = (String) vt.get("type");
                //VT = handleSpecialVertex(vertexType,VT);
                if (pVEMAap.addVertex(vt)) {
                    vList.add(vt);
                }
            }

            for (List<ChartNode> emList : eeDefList) {
                Map<String, Object> et = new HashMap<>();
                for (ChartNode e : emList) {
                    e.handle(src, et);
                }
                if (pVEMAap.addEdge(et)) {
                    //System.out.ut.println("EEEEEEEEEE" );
                    eList.add(et);
                }
            }

        }
    }

    private Map<String, Object> handleSpecialVertex(String vertexType, Map<String, Object> vt) {
        switch (vertexType) {
            case "APPSERVICE":
                String ip = ((String) vt.get("label")).split("_")[0];
                vt.put("label", ip + "_" + vertexType);
                return vt;
            default:
                return vt;
        }
    }

}
