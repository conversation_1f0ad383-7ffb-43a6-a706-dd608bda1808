package com.geeksec.nebula;

import java.util.ArrayList;
import java.util.List;

public class MATCH extends VLBase {
    List<VLBase> vlBaseList = new ArrayList<>();
    List<Where> whereList = new ArrayList<>();
    List<RETURN> returnList = new ArrayList<>();
    List<Order> orderList = new ArrayList<>();
    String strSql = "";
    private SqlParse sqlParse = null ;

    public MATCH addVLBase(VLBase node) {
        vlBaseList.add(node);
        return this;
    }
    public MATCH addVLWhere(Where tes) {
        whereList.add(tes);
        return this;
    }
    public MATCH addVLReturn(RETURN tes) {
        returnList.add(tes);
        return this;
    }
    public MATCH addOrder(Order e) {

        orderList.add(e);
        return this;
    }
    public SqlParse parse() {
        sqlParse = new SqlParse();
        sqlParse.lastVType = "M";
        sqlParse.sql = "MATCH ";
        for (VLBase tmp : vlBaseList) {
            tmp.typeParse(sqlParse);
        }
        //  WHERE
        if (!whereList.isEmpty()) {
            sqlParse.sql += " WHERE ";
            sqlParse.lastVType = "W";
            for(Where tmp : whereList)  {
                tmp.parse(sqlParse);
            }
        }
        if (!returnList.isEmpty()) {
            sqlParse.sql += " RETURN ";
            sqlParse.lastVType = "W";
            int num = 0;
            for ( RETURN tmp : returnList) {
                if (num > 0) {
                    sqlParse.sql += ",";
                }
                tmp.parse(sqlParse);
                num ++;
            }

        }
        if (!orderList.isEmpty()) {
            sqlParse.sql += " ORDER BY  ";
            int num = 0;
            for ( Order tmp : orderList) {
                if (num > 0) {
                    sqlParse.sql += ",";
                }
                tmp.parse(sqlParse);
                num ++;
            }
        }

        return sqlParse;
    }

}