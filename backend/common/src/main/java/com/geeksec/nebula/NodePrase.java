package com.geeksec.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

class NodePrase{
    public  String key = "" ;
    public  String type = "string" ; // string , int
    public  String text = "";
    NodePrase(JSONObject map) {
        //Map<String, Object> map = new BeanMap(obj);
        key = map.get("key").toString();
        if (map.containsKey("type")) {
            type = map.get("type").toString();
        }
        if (map.containsKey("text")) {
            text = map.get("text").toString();
        }

    }
    public Object handle(Map<String,Object> src ) {
        if (!("".equals(key))) {
            Object v = src.get(key);
            return  v;
        } else {
            return text;
        }

    }
}

