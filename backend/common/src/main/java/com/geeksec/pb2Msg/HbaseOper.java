package com.geeksec.pb2Msg;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.CompareFilter.CompareOp;
import org.apache.hadoop.hbase.filter.Filter;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HbaseOper {

    private static final Logger logger = LoggerFactory.getLogger(HbaseOper.class);

    // kerberos认证参数
    private static Configuration conf = null;
    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";

    @Value("${hbase.quorum}")
    private String hbaseAddr;

    @Value("${hbase.port}")
    private String hbasePort;

    //与HBase数据库的连接对象
    public Connection connection;

    //数据库元数操作对象
    public Admin admin;

    public void setUp() throws IOException {
        // 开始krb认证
        init("hbase.zookeeper.quorum=192.168.101.183" , "hbase.zookeeper.property.clientPort=2181");
        //取得一个数据库连接对象
        connection = ConnectionFactory.createConnection(conf);
        admin = connection.getAdmin();
        System.out.println("HBaseDemo.setUp()->admin:" + admin);
    }


    /**
     * 创建表的方法，输入参数为表名
     */
    public void createTable(String tableNameString) throws IOException, IOException {
        System.out.println("-------------------创建表开始了哦------------------------");

        //新建一个数据表表名对象
        TableName tableName = TableName.valueOf(tableNameString);
        System.out.println("HBaseDemo.createTable()->tabelName:" + tableName);
        //if新建的表存在
        if (admin.tableExists(tableName)) {
            System.out.println("表已经存在！");
        }

        //if需要新建的表不存在
        else {
            //数据表描述对象
            HTableDescriptor hTableDescriptor = new HTableDescriptor(tableName);
            System.out.println("HBaseDemo.createTable()->hTableDescriptor:" + hTableDescriptor);

            //数据簇描述对象
            HColumnDescriptor family = new HColumnDescriptor("base");
            System.out.println("HBaseDemo.createTable()->family:" + family);
            //在数据表中新建一个列簇
            hTableDescriptor.addFamily(family);

            //新建数据表
            admin.createTable(hTableDescriptor);
            System.out.println("HBaseDemo.createTable()->admin3:" + admin);
        }
        System.out.println("-----------------创建表结束 ---------------");
    }


    /**
     * 查询表中的数据
     */
    public Map<String, String> queryTable(String tableNameString) throws IOException {
        System.out.println("--------------------查询整表的数据--------");
        Map<String, String> dvMap = new HashMap<String, String>();
        //获取数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));

        //获取表中的数据
        ResultScanner scanner = table.getScanner(new Scan());

        //循环输出表中的数据
        for (Result result : scanner) {

            byte[] row = result.getRow();
            System.out.println("row key is:" + new String(row));

            List<Cell> listCells = result.listCells();
            for (Cell cell : listCells) {

                String c = new String(CellUtil.cloneQualifier(cell));
                String v = new String(CellUtil.cloneValue(cell));
                System.out.println("c :  " + c);
                System.out.println(" v :  " + v);
                dvMap.put(c, v);
            }
        }
        System.out.println("---------------查询整表数据结束----------");
        return dvMap;
    }

    /*
      输入行键的名字
      @param rowName
      @throws IOException
     */
    public Map<String, byte[]> queryTableByRowKey(String tableNameString, String rowNameString) throws IOException {
        logger.info("按RowKey进行行键查询,rowKey--->{}",rowNameString);
        Map<String, byte[]> dvMap = new HashMap<String, byte[]>();
        //取得数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));

        //新建一个查询对象作为查询条件
        Get get = new Get(rowNameString.getBytes());

        //按行查询数据
        Result result = table.get(get);
        if (result.size() == 0) {
            return dvMap;
        }
        String row = Bytes.toString(result.getRow());
        System.out.println("row key is:" + row);

        List<Cell> listCells = result.listCells();
        for (Cell cell : listCells) {
            String c = new String(CellUtil.cloneQualifier(cell));
            byte[] v = CellUtil.cloneValue(cell);

            System.out.println("c :  " + c);
            System.out.println(" v :  " + v);
            //retMap.put(d, Integer.parseInt(v));
            dvMap.put(c, v);
            logger.info("按RowKey查询键值成功,result--->{}",v);
        }
        return dvMap;
    }


    public void queryTableCondition(String tableNameString) throws IOException {

        System.out.println("------------------按条件查询---------------");

        //取得数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));

        //创建查询器
        Filter filter = new SingleColumnValueFilter(Bytes.toBytes("base"),
                Bytes.toBytes("name"), CompareOp.EQUAL, Bytes.toBytes("bookName"));

        //创建扫描器
        Scan scan = new Scan();

        //将查询过滤器的加入到数据表扫描器对象
        scan.setFilter(filter);

        //执行查询操作，并获取查询结果
        ResultScanner scanner = table.getScanner(scan);

        //输出结果

        for (Result result : scanner) {
            byte[] row = result.getRow();
            System.out.println("row key is:" + new String(row));

            List<Cell> listCells = result.listCells();
            for (Cell cell : listCells) {

                String familyArray = Bytes.toString(cell.getFamilyArray());
                String qualifierArray = Bytes.toString(cell.getQualifierArray());
                String valueArray = Bytes.toString(cell.getValueArray());
                System.out.println("row value is:" + familyArray +
                        qualifierArray + valueArray);

            }
        }
        System.out.println("------------------------按条件查询结束--------------------");

    }


    /**
     * 这是清空表的函数，用以使表变得无效
     *
     * @param tableNameString
     * @throws IOException
     */
    public void truncateTable(String tableNameString) throws IOException {

        System.out.println("-------------------------清空表开始------------------");

        //取得目标数据表的表明对象
        TableName tableName = TableName.valueOf(tableNameString);

        //设置表状态为无效
        admin.disableTable(tableName);
        //清空指定表的数据
        admin.truncateTable(tableName, true);

        System.out.println("-------------------------清空表结束-----------------");
    }

    /**
     * 删除指定的表，输入值为表名
     *
     * @param tableNameString
     * @throws IOException
     */
    public void deleteTable(String tableNameString) throws IOException {
        System.out.println("-----------------------删除表---------------");

        // 设置表的状态为无效
        admin.disableTable(TableName.valueOf(tableNameString));

        //删除指定的表
        admin.deleteTable(TableName.valueOf(tableNameString));

        System.out.println("-------------------------删除表-----------------------");

    }

    /**
     * rowkey 是第二层，一行有很多的数据
     *
     * @throws IOException
     */
    public void deleteByRowKey(String tableNameString, String rowKey) throws IOException {
        System.out.println("删除行开始");

        //获取待操作的数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));

        //创建删除条件对象
        Delete delete = new Delete(Bytes.toBytes(rowKey));

        table.delete(delete);

        System.out.println("删除行结束");
    }

    /**
     * 新建一个列簇，第一个是表名，第二个是列簇名
     *
     * @param tableNameString
     * @param columnFamily
     * @throws IOException
     */
    public void addColumnFamily(String tableNameString, String columnFamily) throws IOException {
        System.out.println("新建列簇开始");

        //取得目标数据表的标明对象
        TableName tableName = TableName.valueOf(tableNameString);

        //创建列簇对象
        HColumnDescriptor columnDescriptor = new HColumnDescriptor(columnFamily);

        //将新建的加入到指定的数据表
        admin.addColumn(tableName, columnDescriptor);

        System.out.println("新建列簇结束");
    }

    /**
     * 删除列簇的函数，第一个是表名，第二个是列簇名
     *
     * @param tableNameString
     * @param columnFamily
     * @throws IOException
     */
    public void deleteColumnFamily(String tableNameString, String columnFamily) throws IOException {
        System.out.println("删除列簇开始");

        //取得目标数据表的表明对象
        TableName tableName = TableName.valueOf(tableNameString);

        //删除指定数据表中的指定列簇

        admin.deleteColumn(tableName, columnFamily.getBytes());

        System.out.println("删除列簇成功");
    }

    /**
     * 是插入的一个函数，插入的是一个put的list。具体创建方法
     * List<Put> putList = new ArrayList<put>();
     * Put put;
     * for(int i = 0; i < 10; i++){
     * put = new Put(Bytes.toBytes("row" + i));
     * put.addColumn(Bytes.toBytes("Base")//列簇,Bytes.toBytes("name")//列名,Bytes.toBytes("bookName")//值);
     * putList.ad(put);
     * }
     *
     * @param tableNameString
     * @param putList
     * @throws IOException
     */
    public void insert(String tableNameString, List<Put> putList) throws IOException {
        System.out.println("开始执行插入操作");

        //取得一个数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));
        //将数据插入到数据库中
        table.put(putList);

        System.out.println("插入成功");

    }

    public void insert(String tableNameString, Put put) throws IOException {
        System.out.println("开始执行插入操作");

        //取得一个数据表对象
        Table table = connection.getTable(TableName.valueOf(tableNameString));
        //将数据插入到数据库中
        table.put(put);

        System.out.println("插入成功");

    }

    private static void init(String... items) throws IOException {
        // Default load from conf directory
        conf = HBaseConfiguration.create();
        for (String item : items) {
            String[] ps = item.split("=");
            System.out.println(ps[0] + "====" + ps[1]);
            conf.set(ps[0], ps[1]);
        }

    }

    //    4.关闭资源
    public void close() {
        if (admin != null) {
            try {
                admin.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
