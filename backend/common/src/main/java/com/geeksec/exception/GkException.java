/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.geeksec.exception;

import com.geeksec.enumeration.GkErrorEnum;
import lombok.Data;

/**
*@description: 自定义异常
*@author: shiwenxu
*@createtime: 2023/8/30 09:46
**/
@Data
public class GkException extends RuntimeException {

    /**
     * 错误码
     */
    private Integer err;
    /**
     * 错误信息
     */
    private String msg;

    public GkException(GkErrorEnum gkErrorEnum) {
        super(gkErrorEnum.getMsg());
        this.err = gkErrorEnum.getErr();
        this.msg = gkErrorEnum.getMsg();
    }

}
